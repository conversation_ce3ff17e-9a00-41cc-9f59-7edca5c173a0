version: '3.8'

services:
  # Production application service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres}@db:5432/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - NEXTAUTH_URL=${NEXTAUTH_URL:-https://example.com}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY}
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-mainnet}
      - ALCHEMY_GAS_MANAGER_POLICY_ID=${ALCHEMY_GAS_MANAGER_POLICY_ID}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-mainnet}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-polygon}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum}
      - BASE_NETWORK=${BASE_NETWORK:-base}
      - NODE_ENV=production
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh
    networks:
      - app_network

  # Database service
  db:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=carbon_exchange
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  # Nginx service for SSL termination and serving static files
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    depends_on:
      - app
    networks:
      - app_network

  # Certbot service for SSL certificates
  certbot:
    image: certbot/certbot
    restart: unless-stopped
    volumes:
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    networks:
      - app_network

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
