import { Alchemy, Network, AssetTransfersCategory, TokenBalancesResponseErc20 } from "alchemy-sdk";
import { ethers } from "ethers";
import { logger } from "@/lib/logger";
import { SupportedNetwork, getNetworkConfig } from "@/lib/blockchain-config";

// Import Alchemy Account Abstraction SDK components
import { LightSmartContractAccount, getDefaultLightAccountFactoryAddress, AlchemyProvider, LocalAccountSigner } from "@/lib/blockchain/alchemy";
import { type Chain } from "viem";
import { sepolia, polygon, polygonMumbai, optimism, optimismSepolia, arbitrum, arbitrumSepolia, base, baseSepolia } from "viem/chains";

// Configure Alchemy SDK with additional features
const settings = {
  apiKey: process.env.ALCHEMY_API_KEY,
  network: process.env.ALCHEMY_NETWORK as Network || Network.ETH_SEPOLIA,
  // Enable Alchemy's enhanced APIs
  maxRetries: 5,
};

// Create an Alchemy instance
export const alchemy = new Alchemy(settings);

/**
 * Map network to viem chain
 * @param network SupportedNetwork
 * @param useTestnet Whether to use testnet
 * @returns Chain object
 */
function getViemChain(network: SupportedNetwork, useTestnet: boolean): Chain {
  if (useTestnet) {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia;
      case SupportedNetwork.POLYGON:
        return polygonMumbai;
      case SupportedNetwork.OPTIMISM:
        return optimismSepolia;
      case SupportedNetwork.ARBITRUM:
        return arbitrumSepolia;
      case SupportedNetwork.BASE:
        return baseSepolia;
      default:
        return sepolia;
    }
  } else {
    switch (network) {
      case SupportedNetwork.ETHEREUM:
        return sepolia; // Replace with mainnet in production
      case SupportedNetwork.POLYGON:
        return polygon;
      case SupportedNetwork.OPTIMISM:
        return optimism;
      case SupportedNetwork.ARBITRUM:
        return arbitrum;
      case SupportedNetwork.BASE:
        return base;
      default:
        return sepolia; // Replace with mainnet in production
    }
  }
}

/**
 * Create a new Smart Wallet using Alchemy's Account Kit
 * @param userId User ID for reference
 * @param network Blockchain network to use
 * @param useTestnet Whether to use testnet
 * @returns Smart wallet details
 */
export async function createSmartWallet(userId: string, network: SupportedNetwork = SupportedNetwork.ETHEREUM, useTestnet: boolean = true) {
  try {
    // Generate a new EOA (Externally Owned Account) as the owner
    const ownerWallet = ethers.Wallet.createRandom();
    const ownerAddress = ownerWallet.address;

    // Encrypt the owner's private key
    const encryptedOwnerKey = await ownerWallet.encrypt(
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Get network configuration
    const networkConfig = getNetworkConfig(network, useTestnet);

    // Get the viem chain for the network
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the owner's private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(ownerWallet.privateKey);

    // Create an Alchemy Provider
    const provider = new AlchemyProvider({
      apiKey: process.env.ALCHEMY_API_KEY!,
      chain,
    }).connect(
      (rpcClient: any) =>
        new LightSmartContractAccount({
          rpcClient,
          owner: signer,
          chain,
          factoryAddress: getDefaultLightAccountFactoryAddress(chain),
        } as any)
    );

    // Get the smart wallet address
    const smartWalletAddress = await provider.getAddress();

    // Get the factory address
    const factoryAddress = getDefaultLightAccountFactoryAddress(chain);

    // For implementation address, we'll use a placeholder since it's not directly exposed by the SDK
    const implementationAddress = "0x"; // This would be determined from the contract

    logger.info(`Created smart wallet for user ${userId}: ${smartWalletAddress} on ${networkConfig.name}`);

    return {
      smartWalletAddress,
      ownerAddress,
      encryptedOwnerKey,
      factoryAddress,
      implementationAddress,
      network: network,
      chainId: networkConfig.chainId,
      isTestnet: useTestnet
    };
  } catch (error) {
    logger.error("Error creating smart wallet:", error);
    throw new Error("Failed to create smart wallet");
  }
}

/**
 * Get portfolio balances for a wallet
 * @param address Wallet address
 * @returns Portfolio details including tokens and NFTs
 */
export async function getPortfolio(address: string) {
  try {
    // Get ETH balance
    const ethBalance = await alchemy.core.getBalance(address);
    const ethBalanceFormatted = ethers.formatEther(ethBalance.toString());

    // Get ERC-20 token balances
    const tokenBalances = await alchemy.core.getTokenBalances(address);

    // Get token metadata for non-zero balances
    const tokenMetadata = await Promise.all(
      tokenBalances.tokenBalances
        .filter(token => token.tokenBalance !== "0")
        .map(async token => {
          const metadata = await alchemy.core.getTokenMetadata(token.contractAddress);
          return {
            ...metadata,
            contractAddress: token.contractAddress,
            balance: token.tokenBalance,
            formattedBalance: token.tokenBalance && metadata.decimals
              ? ethers.formatUnits(token.tokenBalance, metadata.decimals)
              : "0",
          };
        })
    );

    // Get NFTs owned by the address
    const nftsResponse = await alchemy.nft.getNftsForOwner(address);
    const nfts = nftsResponse.ownedNfts.map(nft => ({
      contractAddress: nft.contract.address,
      tokenId: nft.tokenId,
      name: nft.name || nft.contract.name || 'Unnamed NFT',
      description: nft.description,
      tokenType: nft.tokenType,
      media: null, // TODO: Fix media property access when Alchemy SDK types are updated
    }));

    logger.info(`Retrieved portfolio for ${address}: ${tokenMetadata.length} tokens, ${nfts.length} NFTs`);

    return {
      address,
      nativeBalance: {
        eth: ethBalanceFormatted,
        wei: ethBalance.toString(),
      },
      tokens: tokenMetadata,
      nfts,
    };
  } catch (error) {
    logger.error(`Error getting portfolio for ${address}:`, error);
    throw new Error("Failed to get portfolio");
  }
}

/**
 * Get token transfers for a wallet
 * @param address Wallet address
 * @param limit Number of transfers to retrieve
 * @returns Token transfer history
 */
export async function getTokenTransfers(address: string, limit = 100) {
  try {
    // Get token transfers (both sent and received)
    const sentTransfers = await alchemy.core.getAssetTransfers({
      fromAddress: address,
      category: [
        AssetTransfersCategory.ERC20,
        AssetTransfersCategory.ERC721,
        AssetTransfersCategory.ERC1155,
      ],
      maxCount: limit,
    });

    const receivedTransfers = await alchemy.core.getAssetTransfers({
      toAddress: address,
      category: [
        AssetTransfersCategory.ERC20,
        AssetTransfersCategory.ERC721,
        AssetTransfersCategory.ERC1155,
      ],
      maxCount: limit,
    });

    // Combine and sort transfers by timestamp (descending)
    const allTransfers = [
      ...sentTransfers.transfers.map(t => ({ ...t, direction: "out" })),
      ...receivedTransfers.transfers.map(t => ({ ...t, direction: "in" })),
    ].sort((a, b) => {
      // Use blockNum for sorting if metadata is not available
      const dateA = parseInt(a.blockNum || '0', 10);
      const dateB = parseInt(b.blockNum || '0', 10);
      return dateB - dateA;
    }).slice(0, limit);

    logger.info(`Retrieved ${allTransfers.length} token transfers for ${address}`);

    return allTransfers;
  } catch (error) {
    logger.error(`Error getting token transfers for ${address}:`, error);
    throw new Error("Failed to get token transfers");
  }
}

/**
 * Get gas price estimates
 * @returns Gas price estimates in different speeds
 */
export async function getGasPrices() {
  try {
    const feeData = await alchemy.core.getFeeData();

    // Convert to gwei (divide by 10^9)
    const gweiDivisor = 1000000000n;

    return {
      slow: feeData.gasPrice ? (Number(feeData.gasPrice) / Number(gweiDivisor)).toFixed(2) : "0",
      average: feeData.maxFeePerGas ? (Number(feeData.maxFeePerGas) / Number(gweiDivisor)).toFixed(2) : "0",
      fast: feeData.maxPriorityFeePerGas && feeData.maxFeePerGas ?
        ((Number(feeData.maxPriorityFeePerGas) + Number(feeData.maxFeePerGas)) / Number(gweiDivisor)).toFixed(2) : "0",
    };
  } catch (error) {
    logger.error("Error getting gas prices:", error);
    throw new Error("Failed to get gas prices");
  }
}

/**
 * Send tokens from a smart wallet
 * @param encryptedOwnerKey Encrypted private key of the wallet owner
 * @param smartWalletAddress Smart wallet address
 * @param to Recipient address
 * @param tokenAddress Token contract address (use 'ETH' for native ETH)
 * @param amount Amount to send
 * @param network Blockchain network
 * @param useTestnet Whether to use testnet
 * @returns Transaction details
 */
export async function sendTokens(
  encryptedOwnerKey: string,
  smartWalletAddress: string,
  to: string,
  tokenAddress: string,
  amount: string,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  try {
    // Decrypt the owner's private key
    const wallet = await ethers.Wallet.fromEncryptedJson(
      encryptedOwnerKey,
      process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
    );

    // Get the viem chain for the network
    const chain = getViemChain(network, useTestnet);

    // Create a signer from the owner's private key
    const signer = LocalAccountSigner.privateKeyToAccountSigner(wallet.privateKey);

    // Create an Alchemy Provider
    const provider = new AlchemyProvider({
      apiKey: process.env.ALCHEMY_API_KEY!,
      chain,
    }).connect(
      (rpcClient: any) =>
        new LightSmartContractAccount({
          rpcClient,
          owner: signer,
          chain,
          factoryAddress: getDefaultLightAccountFactoryAddress(chain),
        } as any)
    );

    let tx;

    // Verify the smart wallet address matches what we expect
    const actualAddress = await provider.getAddress();
    if (actualAddress.toLowerCase() !== smartWalletAddress.toLowerCase()) {
      throw new Error("Smart wallet address mismatch");
    }

    // Send ETH or ERC-20 tokens
    if (tokenAddress === 'ETH') {
      // Send native ETH
      const { hash } = await provider.sendTransaction({
        to,
        value: ethers.parseEther(amount).toString(),
        data: "0x",
      });

      tx = { hash };
    } else {
      // Send ERC-20 tokens
      // Get token metadata to determine decimals
      const tokenMetadata = await alchemy.core.getTokenMetadata(tokenAddress);
      const decimals = tokenMetadata.decimals || 18;

      // Create ERC20 transfer data
      const erc20Interface = new ethers.Interface([
        "function transfer(address to, uint amount) returns (bool)",
      ]);

      const data = erc20Interface.encodeFunctionData("transfer", [
        to,
        ethers.parseUnits(amount, decimals).toString()
      ]);

      // Send the transaction
      const { hash } = await provider.sendTransaction({
        to: tokenAddress,
        data,
        value: "0",
      });

      tx = { hash };
    }

    // Wait for transaction receipt
    let receipt = null;
    let retries = 0;
    const maxRetries = 10;

    while (!receipt && retries < maxRetries) {
      try {
        receipt = await provider.waitForTransactionReceipt({ hash: tx.hash });
      } catch (error) {
        retries++;
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
      }
    }

    logger.info(`Token transfer successful: ${tx.hash}`);

    return {
      hash: tx.hash,
      from: smartWalletAddress,
      to,
      tokenAddress: tokenAddress === 'ETH' ? 'ETH' : tokenAddress,
      amount,
      blockNumber: receipt?.blockNumber,
      status: receipt?.status === "success" ? "success" : "failed",
      network,
      chainId: chain.id,
    };
  } catch (error) {
    logger.error("Error sending tokens:", error);
    throw new Error("Failed to send tokens");
  }
}
