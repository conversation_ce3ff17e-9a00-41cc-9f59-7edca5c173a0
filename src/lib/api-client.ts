/**
 * API Client for communicating with the Express.js backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    
    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('accessToken');
    }
  }

  setToken(token: string | null) {
    this.token = token;
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('accessToken', token);
      } else {
        localStorage.removeItem('accessToken');
      }
    }
  }

  getToken(): string | null {
    return this.token;
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Authentication endpoints
  async login(email: string, password: string) {
    const response = await this.request<{
      user: any;
      accessToken: string;
      refreshToken: string;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    if (response.accessToken) {
      this.setToken(response.accessToken);
      // Store refresh token separately
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.refreshToken);
      }
    }

    return response;
  }

  async register(email: string, password: string, name: string) {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, password, name }),
    });
  }

  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } finally {
      this.setToken(null);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('refreshToken');
      }
    }
  }

  async refreshToken() {
    if (typeof window === 'undefined') return null;
    
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) return null;

    try {
      const response = await this.request<{ accessToken: string }>('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken }),
      });

      if (response.accessToken) {
        this.setToken(response.accessToken);
        return response.accessToken;
      }
    } catch (error) {
      // Refresh failed, clear tokens
      this.setToken(null);
      localStorage.removeItem('refreshToken');
      throw error;
    }

    return null;
  }

  // User endpoints
  async getCurrentUser() {
    return this.request<{ user: any }>('/user/me');
  }

  async updateUser(data: any) {
    return this.request<{ user: any; message: string }>('/user/me', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async getUserOrganization() {
    return this.request<{ organization: any }>('/user/organization');
  }

  async getNotificationPreferences() {
    return this.request<{ notificationPreferences: any }>('/user/notification-preferences');
  }

  async updateNotificationPreferences(preferences: any) {
    return this.request<{ notificationPreferences: any; message: string }>('/user/notification-preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences),
    });
  }

  // Organization endpoints
  async createOrganization(data: any) {
    return this.request<{ organization: any; message: string }>('/organizations', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getOrganizations(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const query = searchParams.toString();
    return this.request<{
      organizations: any[];
      pagination: any;
    }>(`/organizations${query ? `?${query}` : ''}`);
  }

  // Carbon Credit endpoints
  async getCarbonCredits(params?: {
    page?: number;
    limit?: number;
    status?: string;
    vintage?: number;
    standard?: string;
    methodology?: string;
    minPrice?: number;
    maxPrice?: number;
    search?: string;
    organizationId?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const query = searchParams.toString();
    return this.request<{
      carbonCredits: any[];
      pagination: any;
    }>(`/carbon-credits${query ? `?${query}` : ''}`);
  }

  async getOrganizationCarbonCredits() {
    return this.request<{ carbonCredits: any[] }>('/carbon-credits/organization');
  }

  async createCarbonCredit(data: any) {
    return this.request<{ carbonCredit: any; message: string }>('/carbon-credits', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();

// Export the class for creating custom instances if needed
export default ApiClient;
