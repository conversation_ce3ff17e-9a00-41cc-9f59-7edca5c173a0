/**
 * Financial Validation Schemas
 *
 * This module provides standardized validation schemas for financial-related forms.
 */

import { z } from "zod";
import { priceSchema, quantitySchema } from "./schemas";

/**
 * Platform fee settings validation schema
 * Validates all fields required for platform fee settings
 */
export const platformFeeSchema = z.object({
  defaultListingFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  defaultTransactionFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  defaultSubscriptionFee: z
    .number()
    .min(0, "Fee must be at least 0"),
});

/**
 * Organization fee settings validation schema
 * Validates all fields required for organization-specific fee settings
 */
export const organizationFeeSchema = z.object({
  organizationId: z.string().min(1, "Organization is required"),
  listingFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  transactionFeeRate: z
    .number()
    .min(0, "Fee rate must be at least 0")
    .max(100, "Fee rate cannot exceed 100%"),
  subscriptionFee: z
    .number()
    .min(0, "Fee must be at least 0"),
  effectiveFrom: z.date(),
  notes: z.string().optional(),
});

/**
 * Currency validation schema
 * Validates that the currency is one of the allowed values
 */
export const currencySchema = z.enum(["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CNY"], {
  errorMap: () => ({ message: "Please select a valid currency" }),
});

/**
 * Payment method validation schema
 * Validates that the payment method is one of the allowed values
 */
export const paymentMethodSchema = z.enum(["CREDIT_CARD", "BANK_TRANSFER", "CRYPTO", "PAYPAL"], {
  errorMap: () => ({ message: "Please select a valid payment method" }),
});

/**
 * Credit card validation schema
 * Validates all fields required for a credit card payment
 */
export const creditCardSchema = z.object({
  cardNumber: z.string()
    .min(13, "Card number must be at least 13 digits")
    .max(19, "Card number cannot exceed 19 digits")
    .regex(/^\d+$/, "Card number must contain only digits"),
  cardholderName: z.string().min(1, "Cardholder name is required"),
  expiryMonth: z.string()
    .min(1, "Expiry month is required")
    .max(2, "Expiry month cannot exceed 2 digits")
    .regex(/^(0?[1-9]|1[0-2])$/, "Expiry month must be between 1 and 12"),
  expiryYear: z.string()
    .min(2, "Expiry year is required")
    .max(4, "Expiry year cannot exceed 4 digits")
    .regex(/^\d+$/, "Expiry year must contain only digits"),
  cvv: z.string()
    .min(3, "CVV must be at least 3 digits")
    .max(4, "CVV cannot exceed 4 digits")
    .regex(/^\d+$/, "CVV must contain only digits"),
  billingAddress: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
  }),
});

/**
 * Bank transfer validation schema
 * Validates all fields required for a bank transfer
 */
export const bankTransferSchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  routingNumber: z.string().min(1, "Routing number is required"),
  bankName: z.string().min(1, "Bank name is required"),
  bankAddress: z.string().min(1, "Bank address is required"),
  swiftCode: z.string().optional(),
  ibanNumber: z.string().optional(),
});

/**
 * PayPal validation schema
 * Validates all fields required for a PayPal payment
 */
export const paypalSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

/**
 * Invoice validation schema
 * Validates all fields required for an invoice
 */
export const invoiceSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  customerName: z.string().min(1, "Customer name is required"),
  customerEmail: z.string().email("Please enter a valid email address"),
  customerAddress: z.string().min(1, "Customer address is required"),
  items: z.array(
    z.object({
      description: z.string().min(1, "Description is required"),
      quantity: z.number().positive("Quantity must be positive"),
      unitPrice: z.number().positive("Unit price must be positive"),
      amount: z.number().positive("Amount must be positive"),
    })
  ).min(1, "At least one item is required"),
  subtotal: z.number().positive("Subtotal must be positive"),
  tax: z.number().min(0, "Tax must be at least 0"),
  total: z.number().positive("Total must be positive"),
  dueDate: z.string().min(1, "Due date is required"),
  paymentTerms: z.string().min(1, "Payment terms are required"),
  notes: z.string().optional(),
});

/**
 * Payment validation schema
 * Validates all fields required for a payment
 */
export const paymentSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  currency: currencySchema,
  paymentMethod: paymentMethodSchema,
  description: z.string().optional(),
  reference: z.string().optional(),
  paymentDetails: z.union([
    creditCardSchema,
    bankTransferSchema,
    paypalSchema,
    z.object({}).optional(),
  ]).optional(),
});

/**
 * Subscription validation schema
 * Validates all fields required for a subscription
 */
export const subscriptionSchema = z.object({
  planId: z.string().min(1, "Plan ID is required"),
  planName: z.string().min(1, "Plan name is required"),
  price: z.number().positive("Price must be positive"),
  currency: currencySchema,
  billingCycle: z.enum(["MONTHLY", "QUARTERLY", "ANNUAL"], {
    errorMap: () => ({ message: "Please select a valid billing cycle" }),
  }),
  autoRenew: z.boolean(),
  paymentMethod: paymentMethodSchema,
  paymentDetails: z.union([
    creditCardSchema,
    bankTransferSchema,
    paypalSchema,
    z.object({}).optional(),
  ]).optional(),
  startDate: z.string().min(1, "Start date is required"),
  promoCode: z.string().optional(),
});

/**
 * Marketplace listing validation schema
 * Validates all fields required for a marketplace listing
 */
export const marketplaceListingSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  price: priceSchema,
  quantity: quantitySchema,
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").default(1),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"], {
    errorMap: () => ({ message: "Please select a valid pricing strategy" }),
  }),
  auctionEndTime: z.string().optional(),
  auctionReservePrice: z.number().positive("Reserve price must be positive").optional(),
  auctionMinIncrement: z.number().positive("Minimum increment must be positive").optional(),
  dynamicPricingRules: z.string().optional(),
  tieredPricingRules: z.string().optional(),
  allowPartialFills: z.boolean().default(true),
  allowCounterOffers: z.boolean().default(false),
  visibility: z.enum(["PUBLIC", "PRIVATE"], {
    errorMap: () => ({ message: "Please select a valid visibility" }),
  }),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  images: z.array(z.instanceof(File, { message: "Image is required" })).optional(),
  documents: z.array(z.instanceof(File, { message: "Document is required" })).optional(),
}).refine(
  (data) => {
    if (data.pricingStrategy === "FIXED" && !data.price) return false;
    if (data.pricingStrategy === "AUCTION" && (!data.auctionEndTime || !data.auctionReservePrice)) return false;
    if (data.pricingStrategy === "DYNAMIC" && !data.dynamicPricingRules) return false;
    if (data.pricingStrategy === "TIERED" && !data.tieredPricingRules) return false;
    return true;
  },
  {
    message: "Please provide the required pricing information",
    path: ["pricingStrategy"],
  }
);

/**
 * Order validation schema
 * Validates all fields required for an order
 */
export const orderSchema = z.object({
  listingId: z.string().min(1, "Listing ID is required"),
  quantity: z.number().positive("Quantity must be positive"),
  price: z.number().positive("Price must be positive"),
  total: z.number().positive("Total must be positive"),
  paymentMethod: paymentMethodSchema,
  paymentDetails: z.union([
    creditCardSchema,
    bankTransferSchema,
    paypalSchema,
    z.object({}).optional(),
  ]).optional(),
  shippingAddress: z.object({
    name: z.string().min(1, "Name is required"),
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().min(1, "Postal code is required"),
    country: z.string().min(1, "Country is required"),
    phone: z.string().min(1, "Phone is required"),
  }).optional(),
  notes: z.string().optional(),
});
