import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Truncate an Ethereum address for display
 * @param address The Ethereum address to truncate
 * @param startLength Number of characters to show at the start
 * @param endLength Number of characters to show at the end
 * @returns Truncated address string
 */
export function truncateAddress(address: string, startLength: number = 6, endLength: number = 4): string {
  if (!address) return '';
  if (address.length <= startLength + endLength) return address;
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * Show an animated toast notification
 * @param message The message to display in the toast
 * @param type The type of toast (success, error, info, warning)
 * @param duration How long the toast should be displayed (in ms)
 */
export function showAnimatedToast(
  message: string, 
  type: 'success' | 'error' | 'info' | 'warning' = 'info',
  duration: number = 5000
) {
  // Import toast dynamically to avoid issues with SSR
  const { toast } = require("sonner");
  
  switch (type) {
    case 'success':
      toast.success(message, { duration });
      break;
    case 'error':
      toast.error(message, { duration });
      break;
    case 'warning':
      toast.warning(message, { duration });
      break;
    case 'info':
    default:
      toast.info(message, { duration });
      break;
  }
}
