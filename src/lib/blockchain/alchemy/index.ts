/**
 * Alchemy Account Abstraction SDK Integration
 *
 * This module provides a unified interface for working with the Alchemy Account Abstraction SDK.
 * It handles the differences between older and newer versions of the SDK to ensure compatibility
 * with the existing codebase while leveraging the latest features.
 */

import {
  createSmartAccountClient,
  getDefaultSimpleAccountFactoryAddress,
  LocalAccountSigner,
  type SmartAccountSigner,
  type SmartAccountClient,
  type CreateSmartAccountClientOptions,
  type SendUserOperationParameters,
  type SendUserOperationResult,
} from "@alchemy/aa-core";

import {
  alchemyGasManagerMiddleware,
  type AlchemyGasManagerConfig,
} from "@alchemy/aa-alchemy";

import { Chain } from "viem";
import { logger } from "@/lib/logger";

// Re-export core components
export { LocalAccountSigner };
export type { SmartAccountSigner, SmartAccountClient };

// Export the default factory address getter
export const getDefaultLightAccountFactoryAddress = getDefaultSimpleAccountFactoryAddress;

/**
 * Create a smart account client with the Alchemy provider
 * @param options Options for creating the smart account client
 * @returns Smart account client
 */
export function createAlchemySmartAccountClient(
  options: {
    apiKey: string;
    chain: Chain;
    signer: SmartAccountSigner;
    factoryAddress?: string;
    useGasManager?: boolean;
    gasManagerConfig?: AlchemyGasManagerConfig;
  }
): SmartAccountClient {
  try {
    const { apiKey, chain, signer, factoryAddress, useGasManager, gasManagerConfig } = options;

    // Create client options
    const clientOptions: CreateSmartAccountClientOptions = {
      chain,
      transport: options.useGasManager
        ? alchemyGasManagerMiddleware({
            policyId: gasManagerConfig?.policyId,
            entryPoint: gasManagerConfig?.entryPoint || "0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789",
            apiKey,
          })
        : undefined,
      account: {
        accountAddress: "", // Will be set by the client
        entryPoint: "0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789", // Standard ERC-4337 EntryPoint
        factoryAddress: factoryAddress || getDefaultSimpleAccountFactoryAddress(chain),
        signer,
      },
    };

    // Create the smart account client
    const client = createSmartAccountClient(clientOptions);

    logger.info(`Created Alchemy smart account client for chain ${chain.name}`);

    return client;
  } catch (error) {
    logger.error("Error creating Alchemy smart account client:", error);
    throw new Error(`Failed to create Alchemy smart account client: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Send a user operation with the smart account client
 * @param client Smart account client
 * @param params User operation parameters
 * @returns User operation result
 */
export async function sendUserOperation(
  client: SmartAccountClient,
  params: SendUserOperationParameters
): Promise<SendUserOperationResult> {
  try {
    logger.info("Sending user operation with smart account client");

    // Send the user operation
    const result = await client.sendUserOperation(params);

    logger.info(`User operation sent: ${result.hash}`);

    // Wait for the operation to be mined
    const receipt = await client.waitForUserOperationReceipt({ hash: result.hash });

    logger.info(`User operation mined in block ${receipt.receipt.blockNumber}`);

    return {
      ...result,
      receipt,
    };
  } catch (error) {
    logger.error("Error sending user operation:", error);
    throw new Error(`Failed to send user operation: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Estimate gas for a user operation
 * @param client Smart account client
 * @param params User operation parameters
 * @returns Estimated gas
 */
export async function estimateUserOperationGas(
  client: SmartAccountClient,
  params: SendUserOperationParameters
): Promise<{
  callGasLimit: bigint;
  verificationGasLimit: bigint;
  preVerificationGas: bigint;
}> {
  try {
    logger.info("Estimating gas for user operation");

    // Estimate gas for the user operation
    const gasEstimate = await client.estimateUserOperationGas(params);

    logger.info("Gas estimation successful");

    return gasEstimate;
  } catch (error) {
    logger.error("Error estimating user operation gas:", error);
    throw new Error(`Failed to estimate user operation gas: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get the smart account address
 * @param client Smart account client
 * @returns Smart account address
 */
export async function getSmartAccountAddress(client: SmartAccountClient): Promise<string> {
  try {
    const address = await client.getAddress();
    logger.info(`Smart account address: ${address}`);
    return address;
  } catch (error) {
    logger.error("Error getting smart account address:", error);
    throw new Error(`Failed to get smart account address: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Provide compatibility with older code that uses these classes directly
export const LightSmartContractAccount = {
  // This is a placeholder for the LightSmartContractAccount class
  // The actual implementation uses createSmartAccountClient
};

export const AlchemyProvider = {
  // This is a placeholder for the AlchemyProvider class
  // The actual implementation uses createSmartAccountClient
};
