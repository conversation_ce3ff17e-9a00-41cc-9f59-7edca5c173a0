/**
 * Wallet Manager Module
 * 
 * This module provides functionality for managing wallets, including
 * creating, funding, and managing smart wallets and traditional wallets.
 */

import { ethers } from "ethers";
import { db } from "../db";
import { logger } from "../logger";
import { SupportedNetwork, getNetworkConfig } from "../blockchain-config";
import { createSmartAccountProvider } from "./providers/alchemy";
import { BlockchainClient } from "../blockchain-client";

/**
 * Create a new wallet for a user
 * @param userId User ID
 * @param organizationId Organization ID (optional)
 * @param isSmartWallet Whether to create a smart wallet
 * @param network Network to use
 * @param useTestnet Whether to use testnet
 * @returns Created wallet
 */
export async function createWallet(
  userId: string,
  organizationId: string | null = null,
  isSmartWallet: boolean = true,
  network: SupportedNetwork = SupportedNetwork.ETHEREUM,
  useTestnet: boolean = true
) {
  try {
    logger.info(`Creating ${isSmartWallet ? "smart" : "standard"} wallet for user ${userId} on ${network} ${useTestnet ? "testnet" : "mainnet"}`);

    // Get network configuration
    const networkConfig = getNetworkConfig(network, useTestnet);

    if (isSmartWallet) {
      // Create a smart wallet using the smart-wallet module
      const { smartWalletAddress, ownerAddress, encryptedOwnerKey, factoryAddress, implementationAddress } =
        await import("../smart-wallet").then(module => 
          module.createSmartWallet(userId, network, useTestnet)
        );

      // Save the wallet to the database
      const wallet = await db.wallet.create({
        data: {
          address: smartWalletAddress,
          network: network,
          chainId: networkConfig.chainId,
          isTestnet: useTestnet,
          isSmartWallet: true,
          ownerAddress,
          encryptedKey: encryptedOwnerKey,
          factoryAddress,
          implementationAddress,
          balance: 0,
          lastSyncedAt: new Date(),
          user: userId ? { connect: { id: userId } } : undefined,
          organization: organizationId ? { connect: { id: organizationId } } : undefined,
        },
      });

      logger.info(`Created smart wallet ${wallet.id} with address ${wallet.address} for user ${userId}`);

      return wallet;
    } else {
      // Create a standard EOA wallet
      const newWallet = ethers.Wallet.createRandom();
      
      // Encrypt the private key
      const encryptedKey = await newWallet.encrypt(
        process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
      );

      // Save the wallet to the database
      const wallet = await db.wallet.create({
        data: {
          address: newWallet.address,
          network: network,
          chainId: networkConfig.chainId,
          isTestnet: useTestnet,
          isSmartWallet: false,
          encryptedKey,
          balance: 0,
          lastSyncedAt: new Date(),
          user: userId ? { connect: { id: userId } } : undefined,
          organization: organizationId ? { connect: { id: organizationId } } : undefined,
        },
      });

      logger.info(`Created standard wallet ${wallet.id} with address ${wallet.address} for user ${userId}`);

      return wallet;
    }
  } catch (error) {
    logger.error(`Error creating wallet:`, error);
    throw new Error(`Failed to create wallet: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get wallet details
 * @param walletId Wallet ID
 * @returns Wallet details
 */
export async function getWalletDetails(walletId: string) {
  try {
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
      include: {
        tokens: true,
        nfts: true,
      },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${walletId}`);
    }

    return wallet;
  } catch (error) {
    logger.error(`Error getting wallet details:`, error);
    throw new Error(`Failed to get wallet details: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Update wallet balance
 * @param walletId Wallet ID
 * @returns Updated wallet
 */
export async function updateWalletBalance(walletId: string) {
  try {
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${walletId}`);
    }

    // Create blockchain client
    const client = new BlockchainClient(wallet.network as SupportedNetwork, wallet.isTestnet);
    
    // Get balance
    const balance = await client.getBalance(wallet.address);
    
    // Update wallet
    const updatedWallet = await db.wallet.update({
      where: { id: walletId },
      data: {
        balance: parseFloat(balance),
        lastSyncedAt: new Date(),
      },
    });

    return updatedWallet;
  } catch (error) {
    logger.error(`Error updating wallet balance:`, error);
    throw new Error(`Failed to update wallet balance: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Fund a wallet with test tokens
 * @param walletId Wallet ID
 * @returns Transaction hash
 */
export async function fundWalletWithTestTokens(walletId: string) {
  try {
    const wallet = await db.wallet.findUnique({
      where: { id: walletId },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${walletId}`);
    }

    if (!wallet.isTestnet) {
      throw new Error("Cannot fund wallet with test tokens on mainnet");
    }

    // Import the fundSmartWallet function from smart-wallet module
    const { fundSmartWallet } = await import("../smart-wallet");
    
    // Fund the wallet
    const txHash = await fundSmartWallet(wallet.address, wallet.network as SupportedNetwork);
    
    // Update wallet balance
    await updateWalletBalance(walletId);
    
    // Create a transaction record
    await db.transaction.create({
      data: {
        amount: 0.1, // Standard test amount
        fee: 0,
        type: "DEPOSIT",
        status: "COMPLETED",
        transactionHash: txHash,
        network: wallet.network,
        chainId: wallet.chainId,
        wallet: {
          connect: { id: walletId },
        },
      },
    });

    return txHash;
  } catch (error) {
    logger.error(`Error funding wallet with test tokens:`, error);
    throw new Error(`Failed to fund wallet: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Transfer tokens from one wallet to another
 * @param fromWalletId Source wallet ID
 * @param toAddress Destination address
 * @param amount Amount to transfer
 * @returns Transaction hash
 */
export async function transferTokens(
  fromWalletId: string,
  toAddress: string,
  amount: string
) {
  try {
    const wallet = await db.wallet.findUnique({
      where: { id: fromWalletId },
    });

    if (!wallet) {
      throw new Error(`Wallet not found: ${fromWalletId}`);
    }

    // Check if wallet has sufficient balance
    if (wallet.balance < parseFloat(amount)) {
      throw new Error("Insufficient balance");
    }

    let txHash: string;

    if (wallet.isSmartWallet) {
      // For smart wallets, use the smart account provider
      if (!wallet.encryptedKey) {
        throw new Error("Wallet private key not found");
      }

      // Decrypt the private key
      const decryptedWallet = await ethers.Wallet.fromEncryptedJson(
        wallet.encryptedKey,
        process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
      );

      // Create a smart account provider
      const provider = await createSmartAccountProvider(
        decryptedWallet.privateKey,
        wallet.network as SupportedNetwork,
        wallet.isTestnet
      );

      // Send the transaction
      const tx = await provider.sendTransaction({
        to: toAddress,
        value: ethers.parseEther(amount).toString(),
      });

      txHash = tx.hash;
    } else {
      // For standard wallets, use ethers
      if (!wallet.encryptedKey) {
        throw new Error("Wallet private key not found");
      }

      // Decrypt the private key
      const decryptedWallet = await ethers.Wallet.fromEncryptedJson(
        wallet.encryptedKey,
        process.env.WALLET_ENCRYPTION_KEY || "carbon-exchange-secret"
      );

      // Get network configuration
      const networkConfig = getNetworkConfig(wallet.network as SupportedNetwork, wallet.isTestnet);

      // Create a provider
      const provider = new ethers.JsonRpcProvider(networkConfig.rpcUrl);

      // Connect the wallet to the provider
      const connectedWallet = decryptedWallet.connect(provider);

      // Send the transaction
      const tx = await connectedWallet.sendTransaction({
        to: toAddress,
        value: ethers.parseEther(amount),
      });

      txHash = tx.hash;
    }

    // Create a transaction record
    await db.transaction.create({
      data: {
        amount: parseFloat(amount),
        fee: 0, // Will be updated when the transaction is confirmed
        type: "WITHDRAWAL",
        status: "PENDING",
        transactionHash: txHash,
        network: wallet.network,
        chainId: wallet.chainId,
        wallet: {
          connect: { id: fromWalletId },
        },
      },
    });

    return txHash;
  } catch (error) {
    logger.error(`Error transferring tokens:`, error);
    throw new Error(`Failed to transfer tokens: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Get wallet transaction history
 * @param walletId Wallet ID
 * @param limit Number of transactions to return
 * @param offset Offset for pagination
 * @returns Transaction history
 */
export async function getWalletTransactionHistory(
  walletId: string,
  limit: number = 10,
  offset: number = 0
) {
  try {
    const transactions = await db.transaction.findMany({
      where: {
        walletId,
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    });

    const total = await db.transaction.count({
      where: {
        walletId,
      },
    });

    return {
      transactions,
      pagination: {
        total,
        limit,
        offset,
        hasMore: total > offset + limit,
      },
    };
  } catch (error) {
    logger.error(`Error getting wallet transaction history:`, error);
    throw new Error(`Failed to get transaction history: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}
