'use client';

import { useState, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  HelpCircle, 
  Info, 
  X, 
  ExternalLink, 
  ChevronRight, 
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Help content type
export type HelpContentType = 'info' | 'tip' | 'warning' | 'success';

// Help content interface
export interface HelpContent {
  title: string;
  description: string;
  type?: HelpContentType;
  learnMoreLink?: string;
  learnMoreText?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
}

interface ContextualHelpProps {
  content: HelpContent;
  children?: ReactNode;
  className?: string;
  iconClassName?: string;
  triggerAsChild?: boolean;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  inline?: boolean;
  persistent?: boolean;
}

export function ContextualHelp({
  content,
  children,
  className,
  iconClassName,
  triggerAsChild = false,
  side = 'right',
  align = 'center',
  inline = false,
  persistent = false,
}: ContextualHelpProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  
  if (isDismissed && content.dismissible) {
    return null;
  }
  
  const handleDismiss = () => {
    setIsDismissed(true);
    setIsOpen(false);
    if (content.onDismiss) {
      content.onDismiss();
    }
  };
  
  const getIcon = () => {
    switch (content.type) {
      case 'warning':
        return <AlertCircle className={cn("h-4 w-4 text-amber-500", iconClassName)} />;
      case 'success':
        return <CheckCircle className={cn("h-4 w-4 text-green-500", iconClassName)} />;
      case 'tip':
        return <Info className={cn("h-4 w-4 text-blue-500", iconClassName)} />;
      case 'info':
      default:
        return <HelpCircle className={cn("h-4 w-4 text-muted-foreground", iconClassName)} />;
    }
  };
  
  const getContentStyles = () => {
    switch (content.type) {
      case 'warning':
        return 'border-amber-200 bg-amber-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'tip':
        return 'border-blue-200 bg-blue-50';
      case 'info':
      default:
        return '';
    }
  };
  
  return (
    <Popover open={persistent ? true : isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild={triggerAsChild}>
        {children ? (
          <span className={cn("cursor-help", inline ? "inline-flex items-center" : "", className)}>
            {children}
            {inline && getIcon()}
          </span>
        ) : (
          <Button 
            variant="ghost" 
            size="icon" 
            className={cn("h-6 w-6 rounded-full p-0", className)}
          >
            {getIcon()}
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent 
        side={side} 
        align={align} 
        className={cn("w-80", getContentStyles())}
      >
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <h4 className="font-medium">{content.title}</h4>
            {!persistent && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6 rounded-full p-0" 
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <p className="text-sm text-muted-foreground">{content.description}</p>
          
          {(content.learnMoreLink || content.dismissible) && (
            <div className="flex justify-between items-center pt-2">
              {content.learnMoreLink && (
                <Button 
                  variant="link" 
                  size="sm" 
                  className="p-0 h-auto text-xs"
                  onClick={() => window.open(content.learnMoreLink, '_blank')}
                >
                  {content.learnMoreText || 'Learn more'}
                  <ExternalLink className="ml-1 h-3 w-3" />
                </Button>
              )}
              
              {content.dismissible && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs"
                  onClick={handleDismiss}
                >
                  Don't show again
                </Button>
              )}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Inline help text with icon
export function InlineHelp({
  content,
  children,
  className,
}: {
  content: HelpContent;
  children: ReactNode;
  className?: string;
}) {
  return (
    <ContextualHelp content={content} inline={true} className={className}>
      {children}
    </ContextualHelp>
  );
}

// Help card for more detailed explanations
export function HelpCard({
  content,
  className,
  onClose,
}: {
  content: HelpContent;
  className?: string;
  onClose?: () => void;
}) {
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            {content.type === 'warning' && <AlertCircle className="mr-2 h-5 w-5 text-amber-500" />}
            {content.type === 'success' && <CheckCircle className="mr-2 h-5 w-5 text-green-500" />}
            {content.type === 'tip' && <Info className="mr-2 h-5 w-5 text-blue-500" />}
            {(content.type === 'info' || !content.type) && <HelpCircle className="mr-2 h-5 w-5 text-muted-foreground" />}
            <CardTitle>{content.title}</CardTitle>
          </div>
          {onClose && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-8 w-8 rounded-full" 
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <CardDescription>{content.description}</CardDescription>
      </CardHeader>
      
      {content.learnMoreLink && (
        <CardFooter>
          <Button 
            variant="outline" 
            size="sm" 
            className="ml-auto"
            onClick={() => window.open(content.learnMoreLink, '_blank')}
          >
            {content.learnMoreText || 'Learn more'}
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}

// Guided tour step
export interface TourStep {
  target: string; // CSS selector for the target element
  title: string;
  content: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
}

// Guided tour component (to be implemented)
// This is a placeholder for a more complex guided tour component
export function GuidedTour({
  steps,
  isOpen,
  onClose,
  currentStep = 0,
  onStepChange,
}: {
  steps: TourStep[];
  isOpen: boolean;
  onClose: () => void;
  currentStep?: number;
  onStepChange?: (step: number) => void;
}) {
  // This would be implemented with a more complex tour library
  // For now, it's just a placeholder
  return null;
}
