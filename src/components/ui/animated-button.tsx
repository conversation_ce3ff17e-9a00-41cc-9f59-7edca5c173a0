"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedButtonProps extends React.ComponentProps<typeof Button> {
  animationVariant?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
}

const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ className, variant, size, animationVariant = "buttonTap", disableAnimation = false, ...props }, ref) => {
    const buttonAnimation = useAnimationVariant(animationVariant);
    
    // If animations are disabled, render the regular button
    if (disableAnimation) {
      return <Button ref={ref} className={className} variant={variant} size={size} {...props} />;
    }
    
    // Create a motion button with the specified animation
    const MotionButton = motion(Button);
    
    return (
      <MotionButton
        ref={ref}
        className={cn(className)}
        variant={variant}
        size={size}
        initial="initial"
        whileHover="whileHover"
        whileTap="whileTap"
        animate="animate"
        exit="exit"
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        {...buttonAnimation}
        {...props}
      />
    );
  }
);

AnimatedButton.displayName = "AnimatedButton";

export { AnimatedButton };
