"use client";

// Export all animated components from a single file for easier imports

// Animation utilities
export * from "@/lib/animations";
export * from "@/hooks/use-animations";

// Animated components
export * from "@/components/ui/animated-button";
export * from "@/components/ui/animated-card";
export * from "@/components/ui/animated-form";
export * from "@/components/ui/animated-list";
export * from "@/components/ui/animated-icon";
export * from "@/components/ui/animated-tooltip";
export * from "@/components/ui/animated-badge";
export * from "@/components/ui/animated-tabs";
// Explicitly rename the conflicting export to resolve ambiguity
export { AnimatedSpinner as LoadingSpinner } from "@/components/ui/animated-loading";
export * from "@/components/ui/animated-feedback";
export * from "@/components/ui/page-transition";
// Export select components
export * from "@/components/ui/animated/select";
// Export skeleton component
export * from "@/components/ui/animated-skeleton";
// Export loading dots component
export * from "@/components/ui/animated-loading-dots";
// Export progress bar component
export * from "@/components/ui/animated-progress-bar";
// Export notification bell component
export * from "@/components/ui/animated-notification-bell";
// Export status indicator component
export * from "@/components/ui/animated-status-indicator";
// Export success and error components
export * from "@/components/ui/animated-success-error";
// Export grid component
export * from "@/components/ui/animated-grid";
// Export staggered list component
export * from "@/components/ui/staggered-list";
// Export form components
export { Form } from "@/components/ui/form";
