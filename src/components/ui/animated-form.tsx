"use client";

import * as React from "react";
import { motion } from "framer-motion";
import {
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
  FormControl,
  FormField,
  useFormField
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";
import { cn } from "@/lib/utils";
import { FormProvider, useFormContext, type ControllerProps, type FieldPath, type FieldValues } from "react-hook-form";

// Animated versions of form components
const AnimatedFormItem = motion(FormItem);
const AnimatedFormLabel = motion(FormLabel);
const AnimatedFormDescription = motion(FormDescription);
const AnimatedFormMessage = motion(FormMessage);

// Animated FormControl component
const AnimatedFormControl = React.forwardRef<
  HTMLElement,
  React.ComponentPropsWithoutRef<typeof FormControl> & {
    animationVariant?: keyof typeof VARIANTS;
  }
>(({ animationVariant = "fadeIn", ...props }, ref) => {
  const controlAnimation = useAnimationVariant(animationVariant);
  const MotionControl = motion(FormControl);

  return (
    <MotionControl
      ref={ref}
      initial="initial"
      animate="animate"
      exit="exit"
      {...controlAnimation}
      {...props}
    />
  );
});

AnimatedFormControl.displayName = "AnimatedFormControl";

// Animated FormField component
function AnimatedFormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  animationVariant = "fadeIn",
  ...props
}: ControllerProps<TFieldValues, TName> & {
  animationVariant?: keyof typeof VARIANTS;
}) {
  const fieldAnimation = useAnimationVariant(animationVariant);

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      {...fieldAnimation}
    >
      <FormField {...props} />
    </motion.div>
  );
}

// Animated input component
interface AnimatedInputProps extends React.ComponentProps<typeof Input> {
  animationVariant?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
}

const AnimatedInput = React.forwardRef<HTMLInputElement, AnimatedInputProps>(
  ({ className, animationVariant = "fadeIn", disableAnimation = false, ...props }, ref) => {
    // Import the optimized AnimatedInput from the dedicated component
    // This ensures consistent behavior and focus handling across the application
    const { AnimatedInput: OptimizedAnimatedInput } = require("@/components/ui/animated/input");

    // If animations are disabled, render the regular input
    if (disableAnimation) {
      return <Input ref={ref} className={className} {...props} />;
    }

    // Use the optimized AnimatedInput component that properly handles focus
    return (
      <OptimizedAnimatedInput
        ref={ref}
        className={className}
        animationVariant={animationVariant}
        {...props}
      />
    );
  }
);

AnimatedInput.displayName = "AnimatedInput";

// Animated form component that staggers the animation of its children
interface AnimatedFormProps {
  staggerDelay?: number;
  initialDelay?: number;
  animationVariant?: keyof typeof VARIANTS;
  className?: string;
  children?: React.ReactNode;
  methods?: any; // This will hold the form methods from useForm
  [key: string]: any;
}

const AnimatedForm = ({
  children,
  staggerDelay = 0.05,
  initialDelay = 0.1,
  animationVariant = "fadeIn",
  className,
  methods,
  ...props
}: AnimatedFormProps) => {
  const formAnimation = useAnimationVariant(animationVariant);

  const containerAnimation = {
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
  };

  // If methods are provided, use FormProvider, otherwise just render the content
  if (methods) {
    return (
      <div className={cn(className)}>
        <FormProvider {...methods}>
          <motion.div
            initial="initial"
            animate="animate"
            exit="exit"
            {...containerAnimation}
            {...formAnimation}
          >
            {children}
          </motion.div>
        </FormProvider>
      </div>
    );
  }

  // If no methods provided, just render the animated content
  return (
    <div className={cn(className)}>
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        {...containerAnimation}
        {...formAnimation}
        {...props}
      >
        {children}
      </motion.div>
    </div>
  );
};

AnimatedForm.displayName = "AnimatedForm";

// Export a hook for using animated form fields
const useAnimatedFormField = () => {
  const fieldContext = useFormField();
  const { formState } = useFormContext();

  if (!fieldContext) {
    throw new Error("useAnimatedFormField should be used within <AnimatedFormField>");
  }

  return {
    ...fieldContext,
    formState,
  };
};

export {
  AnimatedForm,
  AnimatedFormItem,
  AnimatedFormLabel,
  AnimatedFormDescription,
  AnimatedFormMessage,
  AnimatedFormControl,
  AnimatedFormField,
  AnimatedInput,
  useAnimatedFormField
};
