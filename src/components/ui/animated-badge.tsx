"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";
import { VariantProps } from "class-variance-authority";

// Define custom badge variants
const customBadgeVariants = {
  success: "bg-green-100 text-green-800 hover:bg-green-200 border-green-200",
  warning: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-200",
};

interface AnimatedBadgeProps extends React.ComponentProps<typeof Badge> {
  animationVariant?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
  pulse?: boolean;
  variant?: "success" | "warning" | VariantProps<typeof badgeVariants>["variant"];
}

/**
 * Animated badge component with optional pulse effect
 */
const AnimatedBadge = React.forwardRef<HTMLDivElement, AnimatedBadgeProps>(
  ({ className, animationVariant = "fadeIn", disableAnimation = false, pulse = false, ...props }, ref) => {
    const badgeAnimation = useAnimationVariant(animationVariant);

    // Handle custom variants
    const { variant, ...otherProps } = props;
    let badgeClassName = className;

    if (variant === "success" || variant === "warning") {
      badgeClassName = cn(customBadgeVariants[variant as keyof typeof customBadgeVariants], className);

      // If animations are disabled, render the regular badge with custom variant
      if (disableAnimation) {
        return <Badge ref={ref} className={badgeClassName} variant="outline" {...otherProps} />;
      }

      // Create a motion badge with the specified animation
      const MotionBadge = motion(Badge);

      // Add pulse animation if requested
      const pulseAnimation = pulse ? useAnimationVariant("pulse") : {};

      return (
        <MotionBadge
          ref={ref}
          className={badgeClassName}
          variant="outline"
          initial="initial"
          animate="animate"
          exit="exit"
          {...badgeAnimation}
          {...pulseAnimation}
          {...otherProps}
        />
      );
    }

    // If animations are disabled, render the regular badge
    if (disableAnimation) {
      return <Badge ref={ref} className={className} variant={variant} {...otherProps} />;
    }

    // Create a motion badge with the specified animation
    const MotionBadge = motion(Badge);

    // Add pulse animation if requested
    const pulseAnimation = pulse ? useAnimationVariant("pulse") : {};

    return (
      <MotionBadge
        ref={ref}
        className={className}
        variant={variant}
        initial="initial"
        animate="animate"
        exit="exit"
        {...badgeAnimation}
        {...pulseAnimation}
        {...otherProps}
      />
    );
  }
);

AnimatedBadge.displayName = "AnimatedBadge";

/**
 * Animated status indicator component
 */
function AnimatedStatusIndicator({
  status = "idle",
  size = "md",
  className,
  pulseEffect = true,
}: {
  status?: "idle" | "loading" | "success" | "error" | "warning";
  size?: "sm" | "md" | "lg";
  className?: string;
  pulseEffect?: boolean;
}) {
  const statusColors = {
    idle: "bg-gray-400",
    loading: "bg-blue-500",
    success: "bg-green-500",
    error: "bg-red-500",
    warning: "bg-yellow-500",
  };

  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3",
    lg: "w-4 h-4",
  };

  // Define pulse animation based on status
  const pulseAnimation = pulseEffect && status !== "idle"
    ? status === "loading"
      ? useAnimationVariant("loadingPulse")
      : useAnimationVariant("pulse")
    : {};

  return (
    <motion.div
      className={cn(
        "rounded-full",
        statusColors[status],
        sizeClasses[size],
        className
      )}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      exit={{ scale: 0 }}
      {...pulseAnimation}
    />
  );
}

export { AnimatedBadge, AnimatedStatusIndicator };
