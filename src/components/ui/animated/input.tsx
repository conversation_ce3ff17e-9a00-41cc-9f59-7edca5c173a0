"use client";

import * as React from "react";
import { useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { useFocusPreservation } from "@/hooks/use-focus-preservation";
import { getFormAnimationProps } from "@/lib/animation-optimizations";
import { Input } from "@/components/ui/input";
import { VARIANTS } from "@/lib/animations";

interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  animationVariant?: keyof typeof VARIANTS;
}

export const AnimatedInput = React.forwardRef<
  HTMLInputElement,
  AnimatedInputProps
>(({ className, animationVariant = "fadeIn", onFocus, onBlur, ...props }, forwardedRef) => {
  const inputAnimation = useAnimationVariant(animationVariant);

  // Track if the input has a value to prevent animations during typing
  const [hasValue, setHasValue] = useState(Boolean(props.value));

  // Track if we're actively typing to completely disable animations
  const [isTyping, setIsTyping] = useState(false);

  // Use our enhanced focus preservation hook
  const { focusProps, isFocused, updateSelectionState } = useFocusPreservation<HTMLInputElement>();

  // Update hasValue when props.value changes
  React.useEffect(() => {
    setHasValue(Boolean(props.value));
    // Update selection state when value changes
    if (isFocused) {
      // Use setTimeout to ensure we update after React has updated the DOM
      setTimeout(updateSelectionState, 0);
    }
  }, [props.value, isFocused, updateSelectionState]);

  // Set up a timer to track typing activity
  React.useEffect(() => {
    let typingTimer: NodeJS.Timeout;

    if (isFocused && hasValue) {
      setIsTyping(true);
      // Clear typing state after a delay of inactivity
      typingTimer = setTimeout(() => {
        setIsTyping(false);
      }, 1000);
    } else if (!isFocused) {
      setIsTyping(false);
    }

    return () => {
      if (typingTimer) clearTimeout(typingTimer);
    };
  }, [isFocused, hasValue, props.value]);

  // Combine the refs (forwarded ref and focus preservation ref)
  const handleRef = React.useCallback(
    (element: HTMLInputElement | null) => {
      // Update the focus preservation ref
      if (focusProps.ref.current !== element) {
        focusProps.ref.current = element;
      }

      // Update the forwarded ref if it exists
      if (typeof forwardedRef === 'function') {
        forwardedRef(element);
      } else if (forwardedRef) {
        forwardedRef.current = element;
      }
    },
    [forwardedRef, focusProps.ref]
  );

  // Combine focus handlers
  const handleFocus = React.useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      focusProps.onFocus(e);
      if (onFocus) onFocus(e);
    },
    [focusProps.onFocus, onFocus]
  );

  // Combine blur handlers
  const handleBlur = React.useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      focusProps.onBlur(e);
      if (onBlur) onBlur(e);
    },
    [focusProps.onBlur, onBlur]
  );

  // Handle input changes to track typing activity
  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setIsTyping(true);
      // Call the original onChange handler
      if (props.onChange) props.onChange(e);
      // Update selection state after the change
      setTimeout(updateSelectionState, 0);
    },
    [props.onChange, updateSelectionState]
  );

  // Get optimized animation properties based on focus state
  const formAnimationProps = React.useMemo(() => {
    // Use our form-specific animation properties that respect focus state
    return getFormAnimationProps(isFocused);
  }, [isFocused]);

  // Combine the variant-based animations with our form-specific optimizations
  const animationProps = React.useMemo(() => {
    // If we're typing, use minimal animations to prevent focus loss
    if (isTyping) {
      return {
        initial: { opacity: 1 },
        animate: { opacity: 1 },
        exit: { opacity: 1 },
        transition: { duration: 0 },
        layout: false,
      };
    }

    return {
      ...inputAnimation,
      ...formAnimationProps,
      // Ensure transitions are properly merged
      transition: {
        ...inputAnimation.transition,
        ...formAnimationProps.transition,
      }
    };
  }, [inputAnimation, formAnimationProps, isTyping]);

  // If we're typing, render a simple div instead of motion.div to prevent any animation issues
  if (isTyping) {
    return (
      <div className="w-full">
        <Input
          className={cn("", className)}
          ref={handleRef}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          data-focused={isFocused}
          data-typing={isTyping}
          {...props}
        />
      </div>
    );
  }

  return (
    <motion.div
      className="w-full"
      initial="initial"
      animate={isFocused ? { opacity: 1 } : "animate"}
      exit="exit"
      // Apply our optimized animation properties
      {...animationProps}
      // Completely disable layout animations when focused
      layout={isFocused ? false : "position"}
    >
      <Input
        className={cn("", className)}
        ref={handleRef}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onChange={handleChange}
        // Add data attributes for potential CSS targeting
        data-focused={isFocused}
        data-typing={isTyping}
        {...props}
      />
    </motion.div>
  );
});

AnimatedInput.displayName = "AnimatedInput";
