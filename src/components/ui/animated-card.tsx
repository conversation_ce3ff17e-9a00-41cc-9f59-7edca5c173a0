"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useAnimationVariant } from "@/hooks/use-animations";
import { VARIANTS } from "@/lib/animations";

interface AnimatedCardProps extends React.ComponentProps<typeof Card> {
  animationVariant?: keyof typeof VARIANTS;
  disableAnimation?: boolean;
  interactive?: boolean;
}

const AnimatedCard = React.forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ className, animationVariant = "hoverLift", disableAnimation = false, interactive = true, ...props }, ref) => {
    const cardAnimation = useAnimationVariant(animationVariant);
    
    // If animations are disabled, render the regular card
    if (disableAnimation) {
      return <Card ref={ref} className={className} {...props} />;
    }
    
    // Create a motion card with the specified animation
    const MotionCard = motion(Card);
    
    return (
      <MotionCard
        ref={ref}
        className={cn(className, interactive && "cursor-pointer")}
        initial="initial"
        whileHover={interactive ? "whileHover" : undefined}
        whileTap={interactive ? "whileTap" : undefined}
        animate="animate"
        exit="exit"
        {...cardAnimation}
        {...props}
      />
    );
  }
);

AnimatedCard.displayName = "AnimatedCard";

// Create animated versions of all card components
const AnimatedCardHeader = motion(CardHeader);
const AnimatedCardFooter = motion(CardFooter);
const AnimatedCardTitle = motion(CardTitle);
const AnimatedCardDescription = motion(CardDescription);
const AnimatedCardContent = motion(CardContent);

export { 
  AnimatedCard, 
  AnimatedCardHeader, 
  AnimatedCardFooter, 
  AnimatedCardTitle, 
  AnimatedCardDescription, 
  AnimatedCardContent 
};
