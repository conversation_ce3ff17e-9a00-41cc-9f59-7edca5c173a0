"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  <PERSON><PERSON><PERSON>,
  Pie<PERSON>hart,
  LineChart,
  Calendar,
  Download,
  Share2,
  Filter,
  ArrowUpDown,
  Search,
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Wallet,
  CreditCard,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  RotateCc<PERSON>
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

// Transaction interface
interface Transaction {
  id: string;
  hash: string;
  type: TransactionType;
  status: TransactionStatus;
  timestamp: string;
  from: string;
  to: string;
  value: number;
  gasUsed: number;
  gasPrice: number;
  chainId: number;
  blockNumber?: number;
  nonce: number;
  data?: string;
  error?: string;
}

// Transaction type enum
enum TransactionType {
  TRANSFER = "TRANSFER",
  MINT = "MINT",
  BURN = "BURN",
  APPROVE = "APPROVE",
  SWAP = "SWAP",
  STAKE = "STAKE",
  UNSTAKE = "UNSTAKE",
  CLAIM = "CLAIM",
  CONTRACT_INTERACTION = "CONTRACT_INTERACTION",
  OTHER = "OTHER",
}

// Transaction status enum
enum TransactionStatus {
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  FAILED = "FAILED",
}

// Chain interface
interface Chain {
  id: number;
  name: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  blockExplorerUrl: string;
}

// Gas price recommendation interface
interface GasPriceRecommendation {
  slow: number;
  standard: number;
  fast: number;
  rapid: number;
}

// Transaction dashboard props
interface TransactionDashboardProps {
  transactions: Transaction[];
  chains: Chain[];
  gasPriceRecommendations: Record<number, GasPriceRecommendation>;
  onViewTransaction: (transactionId: string) => void;
  onSpeedUp: (transactionId: string) => void;
  onCancel: (transactionId: string) => void;
  onExport: () => void;
}

export function TransactionDashboard({
  transactions,
  chains,
  gasPriceRecommendations,
  onViewTransaction,
  onSpeedUp,
  onCancel,
  onExport
}: TransactionDashboardProps) {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [chainFilter, setChainFilter] = useState<string>("");
  const [timeFilter, setTimeFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("timestamp");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [expandedTransactions, setExpandedTransactions] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState<string>("all");

  // Filter transactions
  const filteredTransactions = transactions.filter(tx => {
    // Search term filter
    if (searchTerm &&
        !tx.hash.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !tx.from.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !tx.to.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Type filter
    if (typeFilter && tx.type !== typeFilter) {
      return false;
    }

    // Status filter
    if (statusFilter && tx.status !== statusFilter) {
      return false;
    }

    // Chain filter
    if (chainFilter && tx.chainId.toString() !== chainFilter) {
      return false;
    }

    // Time filter
    if (timeFilter !== "all") {
      const txDate = new Date(tx.timestamp);
      const now = new Date();

      switch (timeFilter) {
        case "day":
          const dayAgo = new Date();
          dayAgo.setDate(now.getDate() - 1);
          return txDate >= dayAgo;
        case "week":
          const weekAgo = new Date();
          weekAgo.setDate(now.getDate() - 7);
          return txDate >= weekAgo;
        case "month":
          const monthAgo = new Date();
          monthAgo.setMonth(now.getMonth() - 1);
          return txDate >= monthAgo;
        default:
          return true;
      }
    }

    return true;
  }).sort((a, b) => {
    // Sort by selected field
    let comparison = 0;

    switch (sortBy) {
      case "timestamp":
        comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
        break;
      case "value":
        comparison = a.value - b.value;
        break;
      case "gas":
        comparison = a.gasUsed * a.gasPrice - b.gasUsed * b.gasPrice;
        break;
      case "type":
        comparison = a.type.localeCompare(b.type);
        break;
      default:
        comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    }

    // Apply sort order
    return sortOrder === "asc" ? comparison : -comparison;
  });

  // Get pending transactions
  const pendingTransactions = filteredTransactions.filter(tx => tx.status === TransactionStatus.PENDING);

  // Get confirmed transactions
  const confirmedTransactions = filteredTransactions.filter(tx => tx.status === TransactionStatus.CONFIRMED);

  // Get failed transactions
  const failedTransactions = filteredTransactions.filter(tx => tx.status === TransactionStatus.FAILED);

  // Toggle transaction expansion
  const toggleTransactionExpansion = (transactionId: string) => {
    setExpandedTransactions(prev => ({
      ...prev,
      [transactionId]: !prev[transactionId]
    }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
    });
  };

  // Format address
  const formatAddress = (address: string) => {
    if (!address) return "";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // Format value
  const formatValue = (value: number, decimals: number = 18) => {
    return (value / 10 ** decimals).toFixed(6);
  };

  // Format gas cost
  const formatGasCost = (gasUsed: number, gasPrice: number, decimals: number = 18) => {
    const cost = (gasUsed * gasPrice) / 10 ** decimals;
    return cost.toFixed(6);
  };

  // Get chain by ID
  const getChainById = (chainId: number) => {
    return chains.find(chain => chain.id === chainId);
  };

  // Get transaction type icon
  const getTransactionTypeIcon = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return <CreditCard className="h-4 w-4" />;
      case TransactionType.MINT:
        return <Zap className="h-4 w-4" />;
      case TransactionType.BURN:
        return <Zap className="h-4 w-4" />;
      case TransactionType.APPROVE:
        return <CheckCircle className="h-4 w-4" />;
      case TransactionType.SWAP:
        return <RefreshCw className="h-4 w-4" />;
      case TransactionType.STAKE:
        return <Wallet className="h-4 w-4" />;
      case TransactionType.UNSTAKE:
        return <Wallet className="h-4 w-4" />;
      case TransactionType.CLAIM:
        return <Download className="h-4 w-4" />;
      case TransactionType.CONTRACT_INTERACTION:
        return <Share2 className="h-4 w-4" />;
      default:
        return <Share2 className="h-4 w-4" />;
    }
  };

  // Get transaction status icon
  const getTransactionStatusIcon = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.PENDING:
        return <Clock className="h-4 w-4 text-amber-500" />;
      case TransactionStatus.CONFIRMED:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case TransactionStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Get transaction status badge variant
  const getTransactionStatusBadgeVariant = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.PENDING:
        return "outline";
      case TransactionStatus.CONFIRMED:
        return "outline";
      case TransactionStatus.FAILED:
        return "destructive";
      default:
        return "outline";
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setTypeFilter("");
    setStatusFilter("");
    setChainFilter("");
    setTimeFilter("all");
    setSortBy("timestamp");
    setSortOrder("desc");
  };

  // Calculate gas statistics
  const calculateGasStatistics = () => {
    if (filteredTransactions.length === 0) {
      return {
        totalGasCost: 0,
        averageGasCost: 0,
        highestGasCost: 0,
        lowestGasCost: 0,
      };
    }

    const gasCosts = filteredTransactions.map(tx => tx.gasUsed * tx.gasPrice);
    const totalGasCost = gasCosts.reduce((sum, cost) => sum + cost, 0);
    const averageGasCost = totalGasCost / gasCosts.length;
    const highestGasCost = Math.max(...gasCosts);
    const lowestGasCost = Math.min(...gasCosts);

    return {
      totalGasCost,
      averageGasCost,
      highestGasCost,
      lowestGasCost,
    };
  };

  const gasStatistics = calculateGasStatistics();

  // Render transaction card
  const renderTransactionCard = (transaction: Transaction) => {
    const chain = getChainById(transaction.chainId);
    const isExpanded = expandedTransactions[transaction.id];

    return (
      <Card key={transaction.id} className="overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-2">
                {getTransactionStatusIcon(transaction.status)}
              </div>
              <div>
                <CardTitle className="text-base flex items-center">
                  {getTransactionTypeIcon(transaction.type)}
                  <span className="ml-2">{transaction.type}</span>
                </CardTitle>
                <CardDescription className="line-clamp-1">
                  {formatDate(transaction.timestamp)}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={getTransactionStatusBadgeVariant(transaction.status)}>
                {transaction.status}
              </Badge>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => toggleTransactionExpansion(transaction.id)}
                >
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">From</div>
              <div className="font-medium">{formatAddress(transaction.from)}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">To</div>
              <div className="font-medium">{formatAddress(transaction.to)}</div>
            </div>
          </div>

          <CollapsibleContent className="pt-4">
            <Separator className="mb-4" />

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">Value</div>
                  <div className="font-medium">
                    {formatValue(transaction.value)} {chain?.nativeCurrency.symbol}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Gas Cost</div>
                  <div className="font-medium">
                    {formatGasCost(transaction.gasUsed, transaction.gasPrice)} {chain?.nativeCurrency.symbol}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Chain</div>
                  <div className="font-medium">{chain?.name || `Chain ID: ${transaction.chainId}`}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Nonce</div>
                  <div className="font-medium">{transaction.nonce}</div>
                </div>
              </div>

              <div>
                <div className="text-sm text-muted-foreground">Transaction Hash</div>
                <div className="flex items-center">
                  <div className="font-mono text-xs truncate flex-1">
                    {transaction.hash}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => navigator.clipboard.writeText(transaction.hash)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {transaction.blockNumber && (
                <div>
                  <div className="text-sm text-muted-foreground">Block Number</div>
                  <div className="font-medium">{transaction.blockNumber}</div>
                </div>
              )}

              {transaction.error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {transaction.error}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewTransaction(transaction.id)}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Details
                </Button>

                {transaction.status === TransactionStatus.PENDING && (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onSpeedUp(transaction.id)}
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Speed Up
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => onCancel(transaction.id)}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </CollapsibleContent>
        </CardContent>
      </Card>
    );
  };

  // Render gas optimization section
  const renderGasOptimizationSection = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Gas Price Recommendations</CardTitle>
          <CardDescription>
            Current gas price recommendations for each chain
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(gasPriceRecommendations).map(([chainId, recommendation]) => {
              const chain = getChainById(parseInt(chainId));

              return (
                <div key={chainId} className="space-y-2">
                  <div className="font-medium">{chain?.name || `Chain ID: ${chainId}`}</div>

                  <div className="grid grid-cols-4 gap-2">
                    <div className="p-2 border rounded-md text-center">
                      <div className="text-xs text-muted-foreground">Slow</div>
                      <div className="font-medium">{recommendation.slow} Gwei</div>
                    </div>
                    <div className="p-2 border rounded-md text-center">
                      <div className="text-xs text-muted-foreground">Standard</div>
                      <div className="font-medium">{recommendation.standard} Gwei</div>
                    </div>
                    <div className="p-2 border rounded-md text-center">
                      <div className="text-xs text-muted-foreground">Fast</div>
                      <div className="font-medium">{recommendation.fast} Gwei</div>
                    </div>
                    <div className="p-2 border rounded-md text-center">
                      <div className="text-xs text-muted-foreground">Rapid</div>
                      <div className="font-medium">{recommendation.rapid} Gwei</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render gas statistics section
  const renderGasStatisticsSection = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Gas Statistics</CardTitle>
          <CardDescription>
            Gas usage statistics for filtered transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">Total Gas Cost</div>
              <div className="font-medium">
                {formatGasCost(1, gasStatistics.totalGasCost)} ETH
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Average Gas Cost</div>
              <div className="font-medium">
                {formatGasCost(1, gasStatistics.averageGasCost)} ETH
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Highest Gas Cost</div>
              <div className="font-medium">
                {formatGasCost(1, gasStatistics.highestGasCost)} ETH
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Lowest Gas Cost</div>
              <div className="font-medium">
                {formatGasCost(1, gasStatistics.lowestGasCost)} ETH
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render transaction list
  const renderTransactionList = (transactions: Transaction[]) => {
    if (transactions.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <Info className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
          <p className="text-muted-foreground text-center max-w-md mb-6">
            {searchTerm || typeFilter || statusFilter || chainFilter || timeFilter !== "all" ?
              "No transactions match your current filters. Try adjusting your search criteria." :
              "You don't have any transactions yet."}
          </p>
          {(searchTerm || typeFilter || statusFilter || chainFilter || timeFilter !== "all") && (
            <Button
              variant="outline"
              onClick={resetFilters}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset Filters
            </Button>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {transactions.map(transaction => (
          <Collapsible
            key={transaction.id}
            open={expandedTransactions[transaction.id]}
            onOpenChange={() => toggleTransactionExpansion(transaction.id)}
          >
            {renderTransactionCard(transaction)}
          </Collapsible>
        ))}
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Transaction Dashboard</CardTitle>
            <CardDescription>
              Manage and monitor your blockchain transactions
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by hash, address..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                {Object.values(TransactionType).map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                {Object.values(TransactionStatus).map(status => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={chainFilter} onValueChange={setChainFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Chains" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Chains</SelectItem>
                {chains.map(chain => (
                  <SelectItem key={chain.id} value={chain.id.toString()}>{chain.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSortOrder(sortOrder === "asc" ? "desc" : "asc");
              }}
            >
              <ArrowUpDown className="h-4 w-4 mr-2" />
              {sortOrder === "asc" ? "Ascending" : "Descending"}
            </Button>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="timestamp">Timestamp</SelectItem>
                <SelectItem value="value">Value</SelectItem>
                <SelectItem value="gas">Gas Cost</SelectItem>
                <SelectItem value="type">Type</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="day">Past 24 Hours</SelectItem>
                <SelectItem value="week">Past Week</SelectItem>
                <SelectItem value="month">Past Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="text-sm text-muted-foreground">
          Showing {filteredTransactions.length} of {transactions.length} transactions
          {(searchTerm || typeFilter || statusFilter || chainFilter || timeFilter !== "all") && (
            <Button
              variant="link"
              size="sm"
              className="ml-2 h-auto p-0"
              onClick={resetFilters}
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset Filters
            </Button>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="flex-1">
              All ({filteredTransactions.length})
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex-1">
              Pending ({pendingTransactions.length})
            </TabsTrigger>
            <TabsTrigger value="confirmed" className="flex-1">
              Confirmed ({confirmedTransactions.length})
            </TabsTrigger>
            <TabsTrigger value="failed" className="flex-1">
              Failed ({failedTransactions.length})
            </TabsTrigger>
          </TabsList>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <TabsContent value="all" className="mt-0 space-y-6">
                {renderTransactionList(filteredTransactions)}
              </TabsContent>

              <TabsContent value="pending" className="mt-0 space-y-6">
                {renderTransactionList(pendingTransactions)}
              </TabsContent>

              <TabsContent value="confirmed" className="mt-0 space-y-6">
                {renderTransactionList(confirmedTransactions)}
              </TabsContent>

              <TabsContent value="failed" className="mt-0 space-y-6">
                {renderTransactionList(failedTransactions)}
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderGasOptimizationSection()}
          {renderGasStatisticsSection()}
        </div>
      </CardContent>
    </AnimatedCard>
  );
