"use client";

import { useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { toast } from "@/components/ui/use-toast";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Loader2, ArrowLeft, ArrowRight, Save, Building, Mail, MapPin, FileText, HelpCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoTooltip } from "@/components/ui/info-tooltip";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  Animated<PERSON>ardDescription,
  Animated<PERSON><PERSON>Footer,
  AnimatedButton,
} from "@/components/ui/animated";
import { BasicInfoStep } from "./organization-steps/basic-info-step";
import { ContactInfoStep } from "./organization-steps/contact-info-step";
import { LegalInfoStep } from "./organization-steps/legal-info-step";
import { AddressInfoStep } from "./organization-steps/address-info-step";
import { OnboardingTour } from "./onboarding-tour";

// Schema for organization creation
const organizationSchema = z.object({
  // Basic Information
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  industry: z.string().min(1, "Industry is required"),
  size: z.enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"]),
  description: z.string().optional(),

  // Contact Information
  website: z.string().url("Please enter a valid URL").or(z.string().length(0)),
  phoneNumber: z.string().optional(),
  email: z.string().email("Please enter a valid email address"),

  // Legal Information
  legalName: z.string().min(1, "Legal name is required"),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()),

  // Address Information
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().optional(),
  postalCode: z.string().min(1, "Postal code is required"),
  country: z.string().min(1, "Country is required"),
});

type OrganizationFormValues = z.infer<typeof organizationSchema>;

interface MultiStepOrganizationFormProps {
  onSuccess: (organizationId: string) => void;
}

export function MultiStepOrganizationForm({ onSuccess }: MultiStepOrganizationFormProps) {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formProgress, setFormProgress] = useState(0);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [draftId, setDraftId] = useState<string | null>(null);
  const totalSteps = 4;

  // Initialize form
  const methods = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      size: "SMALL",
      foundedYear: new Date().getFullYear(),
    },
    mode: "onChange",
  });

  // Load saved draft on initial render
  useEffect(() => {
    const loadDraft = async () => {
      try {
        const response = await fetch("/api/organizations/draft");

        if (response.ok) {
          const data = await response.json();

          if (data.draft) {
            // Set form values from draft
            const draft = data.draft;
            setDraftId(draft.id);

            // Set current step if saved
            if (draft.currentStep) {
              setStep(draft.currentStep);
            }

            // Set form values
            Object.keys(draft).forEach(key => {
              if (key !== 'id' && key !== 'userId' && key !== 'createdAt' && key !== 'updatedAt' && key !== 'currentStep') {
                if (draft[key] !== null && draft[key] !== undefined) {
                  methods.setValue(key as any, draft[key]);
                }
              }
            });

            setLastSaved(new Date(draft.updatedAt));
            toast({
              title: "Draft loaded",
              description: "Your previously saved information has been loaded.",
            });
          }
        }
      } catch (err) {
        console.error("Error loading draft:", err);
      }
    };

    loadDraft();
  }, []);

  // Auto-save draft when form values change
  useEffect(() => {
    if (!autoSaveEnabled) return;

    const autoSaveTimeout = setTimeout(() => {
      if (Object.keys(methods.formState.dirtyFields).length > 0) {
        saveDraft(false);
      }
    }, 5000); // Auto-save after 5 seconds of inactivity

    return () => clearTimeout(autoSaveTimeout);
  }, [watchAllFields, autoSaveEnabled]);

  // Update progress when form values change
  const watchAllFields = methods.watch();

  // Calculate form progress
  const calculateProgress = () => {
    const fields = Object.keys(watchAllFields);
    const filledFields = fields.filter(field => {
      const value = watchAllFields[field as keyof OrganizationFormValues];
      return value !== undefined && value !== "" && value !== null;
    });

    return Math.round((filledFields.length / fields.length) * 100);
  };

  // Save draft
  const saveDraft = async (showToast = true) => {
    try {
      setIsSaving(showToast);
      const values = methods.getValues();

      const response = await fetch("/api/organizations/draft", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          currentStep: step,
          draftId: draftId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save draft");
      }

      if (showToast) {
        toast({
          title: "Draft saved",
          description: "Your progress has been saved. You can continue later.",
        });
      }

      setLastSaved(new Date());

      if (data.draft && data.draft.id) {
        setDraftId(data.draft.id);
      }
    } catch (err) {
      console.error("Error saving draft:", err);

      if (showToast) {
        toast({
          variant: "destructive",
          title: "Error",
          description: err instanceof Error ? err.message : "Failed to save draft",
        });
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Handle step navigation
  const nextStep = async () => {
    const fieldsToValidate = getFieldsForStep(step);

    const result = await methods.trigger(fieldsToValidate as any);

    if (result) {
      // Save draft before moving to next step
      await saveDraft(false);

      if (step < totalSteps) {
        setStep(step + 1);
        setFormProgress(calculateProgress());
      } else {
        await handleSubmit();
      }
    }
  };

  const prevStep = () => {
    if (step > 1) {
      // Save draft before moving to previous step
      saveDraft(false);
      setStep(step - 1);
    }
  };

  // Get fields for current step
  const getFieldsForStep = (currentStep: number) => {
    switch (currentStep) {
      case 1:
        return ["name", "industry", "size", "description"];
      case 2:
        return ["website", "phoneNumber", "email"];
      case 3:
        return ["legalName", "registrationNumber", "taxId", "foundedYear"];
      case 4:
        return ["address", "city", "state", "postalCode", "country"];
      default:
        return [];
    }
  };

  // Get step title
  const getStepTitle = (currentStep: number) => {
    switch (currentStep) {
      case 1:
        return "Basic Information";
      case 2:
        return "Contact Information";
      case 3:
        return "Legal Information";
      case 4:
        return "Address Information";
      default:
        return "";
    }
  };

  // Get step icon
  const getStepIcon = (currentStep: number) => {
    switch (currentStep) {
      case 1:
        return <Building className="h-5 w-5" />;
      case 2:
        return <Mail className="h-5 w-5" />;
      case 3:
        return <FileText className="h-5 w-5" />;
      case 4:
        return <MapPin className="h-5 w-5" />;
      default:
        return null;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = methods.getValues();
      setIsSubmitting(true);
      setError(null);

      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create organization");
      }

      // Delete the draft after successful submission
      try {
        await fetch("/api/organizations/draft", {
          method: "DELETE",
        });
      } catch (deleteErr) {
        console.error("Error deleting draft:", deleteErr);
        // Continue even if draft deletion fails
      }

      toast({
        title: "Organization Created",
        description: "Your organization has been created successfully.",
      });

      onSuccess(data.organization.id);
    } catch (err) {
      console.error("Error creating organization:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");

      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to create organization",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return <BasicInfoStep />;
      case 2:
        return <ContactInfoStep />;
      case 3:
        return <LegalInfoStep />;
      case 4:
        return <AddressInfoStep />;
      default:
        return null;
    }
  };

  return (
    <FormProvider {...methods}>
      <AnimatedCard className="w-full max-w-4xl multi-step-organization-form">
        <AnimatedCardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStepIcon(step)}
              <AnimatedCardTitle>{getStepTitle(step)}</AnimatedCardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full contextual-help"
                onClick={() => toast({
                  title: "Help",
                  description: "Need help? Click the help icons throughout the form for guidance, or take the tour for a walkthrough.",
                })}
              >
                <HelpCircle className="h-5 w-5" />
              </Button>
              <InfoTooltip
                content={
                  <div className="space-y-2">
                    <p>Complete all required fields to create your organization.</p>
                    <p>You can edit these details later from your organization settings.</p>
                  </div>
                }
              />
            </div>
          </div>
          <AnimatedCardDescription>
            Step {step} of {totalSteps}: {getStepTitle(step)}
          </AnimatedCardDescription>
          <Progress value={(step / totalSteps) * 100} className="h-2 mt-2 progress-indicator" />
        </AnimatedCardHeader>

        <AnimatedCardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="animate-in fade-in duration-300">
            {renderStepContent()}
          </div>
        </AnimatedCardContent>

        <AnimatedCardFooter className="flex flex-col space-y-4">
          <div className="flex justify-between items-center w-full">
            <AnimatedButton
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={step === 1 || isSubmitting || isSaving}
              animationVariant="buttonTap"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </AnimatedButton>

            <div className="flex space-x-2">
              <AnimatedButton
                type="button"
                variant="outline"
                onClick={() => saveDraft()}
                disabled={isSubmitting || isSaving}
                animationVariant="buttonTap"
                className="save-continue-button"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save & Continue Later
                  </>
                )}
              </AnimatedButton>

              <AnimatedButton
                type="button"
                onClick={nextStep}
                disabled={isSubmitting || isSaving}
                animationVariant="buttonTap"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {step === totalSteps ? "Creating..." : "Saving..."}
                  </>
                ) : (
                  <>
                    {step === totalSteps ? "Create Organization" : "Continue"}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </AnimatedButton>
            </div>
          </div>

          {lastSaved && (
            <div className="text-xs text-muted-foreground text-center">
              {autoSaveEnabled ? "Auto-save enabled · " : ""}
              Last saved: {lastSaved.toLocaleTimeString()}
              {autoSaveEnabled ? (
                <button
                  className="ml-2 underline hover:text-primary"
                  onClick={() => setAutoSaveEnabled(false)}
                >
                  Disable auto-save
                </button>
              ) : (
                <button
                  className="ml-2 underline hover:text-primary"
                  onClick={() => setAutoSaveEnabled(true)}
                >
                  Enable auto-save
                </button>
              )}
            </div>
          )}
        </AnimatedCardFooter>
      </AnimatedCard>

      {/* Add the guided tour */}
      <OnboardingTour page="organization" autoStart={!lastSaved} />
    </FormProvider>
  );
}
