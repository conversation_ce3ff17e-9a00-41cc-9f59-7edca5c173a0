'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AlertCircle, X, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ProfileCompletionIndicator } from '@/components/profile/profile-completion-indicator';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { useProfileCompletion } from '@/hooks/use-profile-completion';
import { cn } from '@/lib/utils';

interface ProfileCompletionBannerProps {
  organizationId: string;
  className?: string;
}

export function ProfileCompletionBanner({ organizationId, className }: ProfileCompletionBannerProps) {
  const router = useRouter();
  const { completionPercentage, loading } = useProfileCompletion({ organizationId });
  const [dismissed, setDismissed] = useLocalStorage<boolean>(`profile-banner-dismissed-${organizationId}`, false);

  // Don't show if loading, dismissed, or 100% complete
  if (loading || dismissed || completionPercentage >= 100) {
    return null;
  }

  // Navigate to organization settings
  const handleCompleteProfile = () => {
    router.push(`/settings/organization?organizationId=${organizationId}`);
  };

  // Dismiss the banner
  const handleDismiss = () => {
    setDismissed(true);
  };

  return (
    <Alert
      className={cn(
        "relative border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50 mb-6",
        className
      )}
    >
      <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
      <div className="flex-1">
        <AlertTitle>Complete your organization profile</AlertTitle>
        <AlertDescription className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex-1">
            Your organization profile is {completionPercentage}% complete.
            Complete your profile to unlock all platform features.
          </div>
          <div className="flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCompleteProfile}
              className="bg-white dark:bg-amber-900/30 border-amber-200 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/50"
            >
              Complete Profile
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </AlertDescription>
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-1 right-1 h-6 w-6 text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-900/50"
        onClick={handleDismiss}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Dismiss</span>
      </Button>
    </Alert>
  );
}
