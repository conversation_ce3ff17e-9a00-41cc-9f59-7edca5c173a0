"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Leaf, Plus, ArrowRight } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  AnimatedC<PERSON>Header,
  <PERSON><PERSON>ard<PERSON>ontent,
  AnimatedCardFooter,
  AnimatedCardT<PERSON>le,
  AnimatedButton,
  AnimatedSkeleton,
  AnimatedIcon,
  AnimatedList,
  PageTransition
} from "@/components/ui/animated";

interface CarbonCredit {
  id: string;
  name: string;
  vintage: number;
  quantity: number;
  availableQuantity: number;
  price: number;
  status: string;
}

export function CarbonCreditsWidget() {
  const router = useRouter();
  const [credits, setCredits] = useState<CarbonCredit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCarbonCredits = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/carbon-credits?limit=3");

      if (!response.ok) {
        throw new Error("Failed to fetch carbon credits");
      }

      const data = await response.json();
      setCredits(data.carbonCredits || []);
    } catch (err) {
      console.error("Error fetching carbon credits:", err);
      setError("Could not load carbon credits");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCarbonCredits();
  }, []);

  if (loading) {
    return (
      <AnimatedCard className="col-span-full min-h-[200px]" animationVariant="fadeIn">
        <AnimatedCardHeader className="flex flex-row items-center justify-between">
          <AnimatedCardTitle>My Carbon Credits</AnimatedCardTitle>
          <AnimatedSkeleton className="h-9 w-24" />
        </AnimatedCardHeader>
        <AnimatedCardContent>
          <div className="space-y-2">
            <AnimatedSkeleton className="h-12 w-full" />
            <AnimatedSkeleton className="h-12 w-full" />
            <AnimatedSkeleton className="h-12 w-full" />
          </div>
        </AnimatedCardContent>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className="col-span-full min-h-[200px]" animationVariant="fadeIn">
      <AnimatedCardHeader className="flex flex-row items-center justify-between">
        <AnimatedCardTitle>My Carbon Credits</AnimatedCardTitle>
        <AnimatedButton
          size="sm"
          onClick={() => router.push("/dashboard/carbon-credits/new")}
          animationVariant="buttonTap"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add New
        </AnimatedButton>
      </AnimatedCardHeader>
      <AnimatedCardContent>
        {error ? (
          <div className="flex items-center justify-center h-32">
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
        ) : credits.length === 0 ? (
          <PageTransition>
            <div className="text-center py-6">
              <AnimatedIcon
                icon={<Leaf className="h-8 w-8" />}
                size="xl"
                color="muted-foreground"
                className="mx-auto mb-2"
                animationVariant="fadeIn"
              />
              <h3 className="text-lg font-medium mb-1">No Carbon Credits Yet</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Start by adding your first carbon credit to the marketplace
              </p>
              <AnimatedButton
                onClick={() => router.push("/dashboard/carbon-credits/new")}
                animationVariant="buttonTap"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Carbon Credit
              </AnimatedButton>
            </div>
          </PageTransition>
        ) : (
          <AnimatedList
            items={credits}
            keyExtractor={(credit) => credit.id}
            className="space-y-2"
            staggerDelay={0.08}
            renderItem={(credit) => (
              <div
                className="flex items-center justify-between p-3 border rounded-md hover:bg-accent cursor-pointer"
                onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}`)}
              >
                <div>
                  <h3 className="font-medium">{credit.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {credit.vintage} • {credit.availableQuantity} of {credit.quantity} tons available
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium">${credit.price.toFixed(2)}/ton</p>
                  <p className="text-xs text-muted-foreground">
                    Status: {credit.status}
                  </p>
                </div>
              </div>
            )}
          />
        )}
      </AnimatedCardContent>
      <AnimatedCardFooter className="flex justify-end">
        <AnimatedButton
          variant="link"
          size="sm"
          onClick={() => router.push("/dashboard/carbon-credits")}
          animationVariant="buttonTap"
        >
          View All Carbon Credits
          <ArrowRight className="h-4 w-4 ml-1" />
        </AnimatedButton>
      </AnimatedCardFooter>
    </AnimatedCard>
  );
}
