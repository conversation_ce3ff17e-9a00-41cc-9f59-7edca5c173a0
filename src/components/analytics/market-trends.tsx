"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Cell 
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";

type MarketTrends = {
  period: number;
  totalVolume: number;
  totalValue: number;
  orderCount: number;
  dailyStats: {
    date: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  standardStats: {
    standard: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  methodologyStats: {
    methodology: string;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
  vintageStats: {
    vintage: number;
    volume: number;
    value: number;
    averagePrice: number;
    orderCount: number;
  }[];
};

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82CA9D", "#FFC658", "#8DD1E1"];

export function MarketTrends() {
  const { data: session } = useSession();
  const [marketTrends, setMarketTrends] = useState<MarketTrends | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState("30");
  const { toast } = useToast();

  const fetchMarketTrends = async (days: string) => {
    if (!session?.user) return;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/analytics/market?period=${days}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch market trends");
      }
      
      const data = await response.json();
      setMarketTrends(data);
    } catch (error) {
      console.error("Error fetching market trends:", error);
      toast({
        title: "Error",
        description: "Failed to load market trends",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchMarketTrends(period);
    }
  }, [session, period]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Market Trends</h2>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
            <SelectItem value="180">Last 6 months</SelectItem>
            <SelectItem value="365">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="h-80">
              <Skeleton className="h-full w-full" />
            </CardContent>
          </Card>
          <div className="grid gap-6 md:grid-cols-2">
            {Array.from({ length: 2 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                </CardHeader>
                <CardContent className="h-80">
                  <Skeleton className="h-full w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : marketTrends ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Daily Trading Volume</CardTitle>
              <CardDescription>
                Trading volume and average price over the last {marketTrends.period} days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={marketTrends.dailyStats}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value: any, name: string) => {
                        if (name === "volume") return [formatNumber(value), "Volume"];
                        if (name === "averagePrice") return [formatCurrency(value), "Avg Price"];
                        return [value, name];
                      }}
                    />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="volume"
                      stroke="#8884d8"
                      name="Volume"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="averagePrice"
                      stroke="#82ca9d"
                      name="Avg Price"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="standard">
            <TabsList className="mb-4">
              <TabsTrigger value="standard">By Standard</TabsTrigger>
              <TabsTrigger value="methodology">By Methodology</TabsTrigger>
              <TabsTrigger value="vintage">By Vintage</TabsTrigger>
            </TabsList>

            <TabsContent value="standard" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Volume by Standard</CardTitle>
                    <CardDescription>
                      Distribution of trading volume by carbon credit standard
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={marketTrends.standardStats}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="volume"
                            nameKey="standard"
                          >
                            {marketTrends.standardStats.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value: any) => [formatNumber(value), "Volume"]}
                          />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Average Price by Standard</CardTitle>
                    <CardDescription>
                      Average price comparison across different standards
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={marketTrends.standardStats}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="standard" />
                          <YAxis />
                          <Tooltip
                            formatter={(value: any) => [formatCurrency(value), "Avg Price"]}
                          />
                          <Legend />
                          <Bar
                            dataKey="averagePrice"
                            name="Avg Price"
                            fill="#8884d8"
                          >
                            {marketTrends.standardStats.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="methodology" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Volume by Methodology</CardTitle>
                    <CardDescription>
                      Distribution of trading volume by carbon credit methodology
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={marketTrends.methodologyStats}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="volume"
                            nameKey="methodology"
                          >
                            {marketTrends.methodologyStats.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value: any) => [formatNumber(value), "Volume"]}
                          />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Average Price by Methodology</CardTitle>
                    <CardDescription>
                      Average price comparison across different methodologies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={marketTrends.methodologyStats}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="methodology" />
                          <YAxis />
                          <Tooltip
                            formatter={(value: any) => [formatCurrency(value), "Avg Price"]}
                          />
                          <Legend />
                          <Bar
                            dataKey="averagePrice"
                            name="Avg Price"
                            fill="#82ca9d"
                          >
                            {marketTrends.methodologyStats.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="vintage" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Volume by Vintage</CardTitle>
                    <CardDescription>
                      Distribution of trading volume by carbon credit vintage year
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={marketTrends.vintageStats.sort((a, b) => a.vintage - b.vintage)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="vintage" />
                          <YAxis />
                          <Tooltip
                            formatter={(value: any) => [formatNumber(value), "Volume"]}
                          />
                          <Legend />
                          <Bar
                            dataKey="volume"
                            name="Volume"
                            fill="#8884d8"
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Average Price by Vintage</CardTitle>
                    <CardDescription>
                      Average price comparison across different vintage years
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={marketTrends.vintageStats.sort((a, b) => a.vintage - b.vintage)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="vintage" />
                          <YAxis />
                          <Tooltip
                            formatter={(value: any) => [formatCurrency(value), "Avg Price"]}
                          />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="averagePrice"
                            name="Avg Price"
                            stroke="#FF8042"
                            activeDot={{ r: 8 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <p className="text-muted-foreground">No market data available</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
