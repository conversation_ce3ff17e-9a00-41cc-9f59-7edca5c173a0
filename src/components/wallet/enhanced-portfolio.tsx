"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AnimatedBadge } from "@/components/ui/animated-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/charts";
import { truncateAddress } from "@/lib/utils";
import {
  Wallet,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Search,
  Filter,
  Download,
  Send,
  Plus,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ert<PERSON><PERSON>cle,
  <PERSON>Circle,
  Coins,
  Banknote,
  Image
} from "lucide-react";
// Import animated components
import {
  <PERSON><PERSON><PERSON>on,
  Animated<PERSON>ard,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedCardFooter,
} from "@/components/ui/animated";

interface Token {
  contractAddress: string;
  name: string | null;
  symbol: string | null;
  decimals: number | null;
  formattedBalance: string;
  value?: number; // USD value
  change24h?: number; // 24h change percentage
}

interface NFT {
  contractAddress: string;
  tokenId: string;
  name: string | null;
  description: string | null;
  tokenType: string | null;
  media: any[];
  estimatedValue?: number; // USD value estimate
}

interface Transfer {
  hash: string;
  from: string;
  to: string;
  asset: string;
  value: number;
  direction: "in" | "out";
  metadata: {
    blockTimestamp: string;
  };
  status?: "PENDING" | "COMPLETED" | "FAILED";
  gasUsed?: number;
  gasPrice?: number;
}

interface Portfolio {
  nativeBalance: {
    eth: string;
    wei: string;
  };
  tokens: Token[];
  nfts: NFT[];
  transfers?: Transfer[];
  totalValue?: number; // Total portfolio value in USD
  performance?: {
    change24h: number; // 24h change percentage
    change7d: number; // 7d change percentage
    change30d: number; // 30d change percentage
  };
  history?: {
    date: string;
    value: number;
  }[];
  composition?: {
    name: string;
    value: number;
  }[];
}

export default function EnhancedPortfolioComponent() {
  const { data: session } = useSession();
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("7d");
  const [searchQuery, setSearchQuery] = useState("");
  const [transactionFilter, setTransactionFilter] = useState("all");

  useEffect(() => {
    if (session?.user) {
      fetchPortfolio();
    }
  }, [session]);

  const fetchPortfolio = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/wallet/portfolio?includeTransfers=true");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch portfolio");
      }

      const data = await response.json();

      // Enhance the portfolio data with mock values for demonstration
      const enhancedPortfolio = enhancePortfolioData(data.portfolio);
      setPortfolio(enhancedPortfolio);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
      console.error("Error fetching portfolio:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to enhance portfolio data with mock values for demonstration
  const enhancePortfolioData = (rawPortfolio: Portfolio): Portfolio => {
    // Add mock token values and changes
    const enhancedTokens = rawPortfolio.tokens.map(token => ({
      ...token,
      value: parseFloat(token.formattedBalance) * (Math.random() * 1000 + 100), // Random USD value
      change24h: (Math.random() * 20) - 10, // Random 24h change between -10% and +10%
    }));

    // Add mock NFT values
    const enhancedNFTs = rawPortfolio.nfts.map(nft => ({
      ...nft,
      estimatedValue: Math.random() * 1000 + 50, // Random USD value between 50 and 1050
    }));

    // Add mock transfer statuses and gas info
    const enhancedTransfers = rawPortfolio.transfers?.map(transfer => ({
      ...transfer,
      status: Math.random() > 0.1 ? "COMPLETED" : (Math.random() > 0.5 ? "PENDING" : "FAILED"),
      gasUsed: Math.floor(Math.random() * 100000) + 21000,
      gasPrice: Math.floor(Math.random() * 100) + 10,
    }));

    // Calculate total portfolio value
    const ethValue = parseFloat(rawPortfolio.nativeBalance.eth) * 3000; // Assuming 1 ETH = $3000
    const tokenValue = enhancedTokens.reduce((sum, token) => sum + (token.value || 0), 0);
    const nftValue = enhancedNFTs.reduce((sum, nft) => sum + (nft.estimatedValue || 0), 0);
    const totalValue = ethValue + tokenValue + nftValue;

    // Generate mock historical data
    const history = generateMockHistoricalData(totalValue, timeRange);

    // Generate mock composition data
    const composition = [
      { name: "ETH", value: ethValue },
      { name: "Tokens", value: tokenValue },
      { name: "NFTs", value: nftValue },
    ];

    return {
      ...rawPortfolio,
      tokens: enhancedTokens,
      nfts: enhancedNFTs,
      transfers: enhancedTransfers,
      totalValue,
      performance: {
        change24h: (Math.random() * 20) - 10,
        change7d: (Math.random() * 30) - 15,
        change30d: (Math.random() * 50) - 20,
      },
      history,
      composition,
    };
  };

  // Generate mock historical data
  const generateMockHistoricalData = (currentValue: number, range: string) => {
    const data = [];
    let days = 7;

    if (range === "1m") days = 30;
    if (range === "3m") days = 90;
    if (range === "1y") days = 365;

    let value = currentValue * 0.8; // Start at 80% of current value

    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Add some randomness to create a realistic chart
      value = value * (1 + (Math.random() * 0.04) - 0.02);

      data.push({
        date: date.toISOString().split('T')[0],
        value: Math.round(value),
      });
    }

    return data;
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (loading) {
    return <PortfolioSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={fetchPortfolio}>Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!portfolio) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="mb-4">No portfolio data available.</p>
            <Button onClick={fetchPortfolio}>Refresh</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold">Portfolio</h2>
          <p className="text-muted-foreground">Manage and track your digital assets</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={fetchPortfolio}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm">
            <Send className="mr-2 h-4 w-4" />
            Send
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AnimatedCard>
          <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <AnimatedCardTitle className="text-sm font-medium">Total Value</AnimatedCardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="text-2xl font-bold">{formatCurrency(portfolio.totalValue || 0)}</div>
            <div className="flex items-center pt-1">
              {portfolio.performance?.change24h && portfolio.performance.change24h > 0 ? (
                <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
              )}
              <p className={`text-xs ${portfolio.performance?.change24h && portfolio.performance.change24h > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {formatPercentage(portfolio.performance?.change24h || 0)} (24h)
              </p>
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <AnimatedCardTitle className="text-sm font-medium">ETH Balance</AnimatedCardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="text-2xl font-bold">{portfolio.nativeBalance.eth} ETH</div>
            <div className="flex items-center pt-1">
              <p className="text-xs text-muted-foreground">
                ≈ {formatCurrency(parseFloat(portfolio.nativeBalance.eth) * 3000)}
              </p>
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <AnimatedCardTitle className="text-sm font-medium">Token Value</AnimatedCardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(portfolio.tokens.reduce((sum, token) => sum + (token.value || 0), 0))}
            </div>
            <div className="flex items-center pt-1">
              <p className="text-xs text-muted-foreground">
                {portfolio.tokens.length} different tokens
              </p>
            </div>
          </AnimatedCardContent>
        </AnimatedCard>

        <AnimatedCard>
          <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <AnimatedCardTitle className="text-sm font-medium">NFT Value</AnimatedCardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(portfolio.nfts.reduce((sum, nft) => sum + (nft.estimatedValue || 0), 0))}
            </div>
            <div className="flex items-center pt-1">
              <p className="text-xs text-muted-foreground">
                {portfolio.nfts.length} collectibles
              </p>
            </div>
          </AnimatedCardContent>
        </AnimatedCard>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-5 md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assets">Assets</TabsTrigger>
          <TabsTrigger value="nfts">NFTs</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="md:col-span-2">
              <AnimatedCard>
                <AnimatedCardHeader>
                  <div className="flex items-center justify-between">
                    <AnimatedCardTitle>Portfolio Performance</AnimatedCardTitle>
                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Time Range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7d">7 Days</SelectItem>
                        <SelectItem value="1m">1 Month</SelectItem>
                        <SelectItem value="3m">3 Months</SelectItem>
                        <SelectItem value="1y">1 Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </AnimatedCardHeader>
                <AnimatedCardContent>
                  <div className="h-[300px]">
                    {portfolio.history && (
                      <LineChart
                        data={portfolio.history}
                        xAxisKey="date"
                        yAxisKey="value"
                        valueFormatter={(value) => formatCurrency(value)}
                      />
                    )}
                  </div>
                </AnimatedCardContent>
              </AnimatedCard>
            </div>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Asset Allocation</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="h-[300px]">
                  {portfolio.composition && (
                    <PieChart
                      data={portfolio.composition}
                      nameKey="name"
                      dataKey="value"
                      valueFormatter={(value) => formatCurrency(value)}
                    />
                  )}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Top Assets</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  {portfolio.tokens.slice(0, 5).map((token) => (
                    <div key={token.contractAddress} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{token.name || "Unknown Token"}</p>
                        <p className="text-sm text-muted-foreground">{token.symbol || truncateAddress(token.contractAddress)}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(token.value || 0)}</p>
                        <p className={`text-xs ${token.change24h && token.change24h > 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {formatPercentage(token.change24h || 0)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCardContent>
              <AnimatedCardFooter>
                <Button variant="ghost" size="sm" className="w-full" onClick={() => setActiveTab("assets")}>
                  View All Assets
                </Button>
              </AnimatedCardFooter>
            </AnimatedCard>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Recent Activity</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  {portfolio.transfers?.slice(0, 5).map((transfer) => (
                    <div key={transfer.hash} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">
                          {transfer.direction === "in" ? "Received" : "Sent"} {transfer.asset}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(transfer.metadata.blockTimestamp).toLocaleString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${transfer.direction === "in" ? "text-green-600" : "text-red-600"}`}>
                          {transfer.direction === "in" ? "+" : "-"}{transfer.value}
                        </p>
                        <AnimatedBadge variant={
                          transfer.status === "COMPLETED" ? "success" :
                          transfer.status === "PENDING" ? "outline" : "destructive"
                        } className="text-xs">
                          {transfer.status}
                        </AnimatedBadge>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCardContent>
              <AnimatedCardFooter>
                <Button variant="ghost" size="sm" className="w-full" onClick={() => setActiveTab("activity")}>
                  View All Activity
                </Button>
              </AnimatedCardFooter>
            </AnimatedCard>
          </div>
        </TabsContent>

        <TabsContent value="assets" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search assets..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Token
            </Button>
          </div>

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Your Assets</AnimatedCardTitle>
              <AnimatedCardDescription>
                Manage your tokens and cryptocurrencies
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="space-y-4">
                <div className="rounded-md border">
                  <div className="grid grid-cols-5 gap-4 p-4 font-medium border-b">
                    <div className="col-span-2">Asset</div>
                    <div className="text-right">Balance</div>
                    <div className="text-right">Value</div>
                    <div className="text-right">24h Change</div>
                  </div>
                  <div className="divide-y">
                    <div className="grid grid-cols-5 gap-4 p-4 items-center">
                      <div className="col-span-2">
                        <div className="font-medium">Ethereum</div>
                        <div className="text-sm text-muted-foreground">ETH</div>
                      </div>
                      <div className="text-right">{portfolio.nativeBalance.eth} ETH</div>
                      <div className="text-right">{formatCurrency(parseFloat(portfolio.nativeBalance.eth) * 3000)}</div>
                      <div className="text-right text-green-500">+2.34%</div>
                    </div>

                    {portfolio.tokens
                      .filter(token =>
                        !searchQuery ||
                        (token.name && token.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
                        (token.symbol && token.symbol.toLowerCase().includes(searchQuery.toLowerCase()))
                      )
                      .map((token) => (
                        <div key={token.contractAddress} className="grid grid-cols-5 gap-4 p-4 items-center">
                          <div className="col-span-2">
                            <div className="font-medium">{token.name || "Unknown Token"}</div>
                            <div className="text-sm text-muted-foreground">{token.symbol || truncateAddress(token.contractAddress)}</div>
                          </div>
                          <div className="text-right">{token.formattedBalance}</div>
                          <div className="text-right">{formatCurrency(token.value || 0)}</div>
                          <div className={`text-right ${token.change24h && token.change24h > 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {formatPercentage(token.change24h || 0)}
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="nfts" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search NFTs..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Your NFTs</AnimatedCardTitle>
              <AnimatedCardDescription>
                Manage your digital collectibles
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {portfolio.nfts.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {portfolio.nfts
                    .filter(nft =>
                      !searchQuery ||
                      (nft.name && nft.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
                      (nft.description && nft.description.toLowerCase().includes(searchQuery.toLowerCase()))
                    )
                    .map((nft) => (
                      <AnimatedCard key={`${nft.contractAddress}-${nft.tokenId}`} className="overflow-hidden">
                        <div className="aspect-square bg-gray-100 flex items-center justify-center">
                          {nft.media && nft.media.length > 0 ? (
                            <img
                              src={nft.media[0].gateway || nft.media[0].raw}
                              alt={nft.name || "NFT"}
                              className="object-cover w-full h-full"
                            />
                          ) : (
                            <div className="text-4xl">🖼️</div>
                          )}
                        </div>
                        <AnimatedCardContent className="p-4">
                          <h3 className="font-medium truncate">{nft.name || `NFT #${nft.tokenId}`}</h3>
                          <p className="text-sm text-muted-foreground truncate">{nft.tokenType || "NFT"}</p>
                          <div className="mt-2 flex justify-between items-center">
                            <p className="text-sm">Estimated Value</p>
                            <p className="font-medium">{formatCurrency(nft.estimatedValue || 0)}</p>
                          </div>
                        </AnimatedCardContent>
                      </AnimatedCard>
                    ))
                  }
                </div>
              ) : (
                <p className="text-center py-4 text-muted-foreground">No NFTs found in your wallet.</p>
              )}
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search transactions..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={transactionFilter} onValueChange={setTransactionFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Transactions</SelectItem>
                  <SelectItem value="in">Received</SelectItem>
                  <SelectItem value="out">Sent</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Transaction History</AnimatedCardTitle>
              <AnimatedCardDescription>
                View and track all your transactions
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {portfolio.transfers && portfolio.transfers.length > 0 ? (
                <div className="rounded-md border">
                  <div className="grid grid-cols-6 gap-4 p-4 font-medium border-b">
                    <div className="col-span-2">Transaction</div>
                    <div className="text-right">Amount</div>
                    <div className="text-right">Status</div>
                    <div className="text-right">Gas Fee</div>
                    <div className="text-right">Date</div>
                  </div>
                  <div className="divide-y">
                    {portfolio.transfers
                      .filter(transfer => {
                        if (searchQuery && !transfer.asset.toLowerCase().includes(searchQuery.toLowerCase())) {
                          return false;
                        }
                        if (transactionFilter === "in" && transfer.direction !== "in") {
                          return false;
                        }
                        if (transactionFilter === "out" && transfer.direction !== "out") {
                          return false;
                        }
                        if (transactionFilter === "pending" && transfer.status !== "PENDING") {
                          return false;
                        }
                        if (transactionFilter === "completed" && transfer.status !== "COMPLETED") {
                          return false;
                        }
                        if (transactionFilter === "failed" && transfer.status !== "FAILED") {
                          return false;
                        }
                        return true;
                      })
                      .map((transfer) => (
                        <div key={transfer.hash} className="grid grid-cols-6 gap-4 p-4 items-center">
                          <div className="col-span-2">
                            <div className="font-medium">
                              {transfer.direction === "in" ? "Received" : "Sent"} {transfer.asset}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {transfer.direction === "in"
                                ? `From: ${truncateAddress(transfer.from)}`
                                : `To: ${truncateAddress(transfer.to)}`}
                            </div>
                          </div>
                          <div className={`text-right ${transfer.direction === "in" ? "text-green-600" : "text-red-600"}`}>
                            {transfer.direction === "in" ? "+" : "-"}{transfer.value}
                          </div>
                          <div className="text-right">
                            <AnimatedBadge variant={
                              transfer.status === "COMPLETED" ? "success" :
                              transfer.status === "PENDING" ? "outline" : "destructive"
                            }>
                              {transfer.status}
                            </AnimatedBadge>
                          </div>
                          <div className="text-right text-muted-foreground">
                            {transfer.gasUsed && transfer.gasPrice
                              ? `${((transfer.gasUsed * transfer.gasPrice) / 1e9).toFixed(6)} ETH`
                              : "-"}
                          </div>
                          <div className="text-right text-muted-foreground">
                            {new Date(transfer.metadata.blockTimestamp).toLocaleString()}
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              ) : (
                <p className="text-center py-4 text-muted-foreground">No transaction history found.</p>
              )}
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Portfolio Growth</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Track your portfolio performance over time
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="h-[300px]">
                  {portfolio.history && (
                    <LineChart
                      data={portfolio.history}
                      xAxisKey="date"
                      yAxisKey="value"
                      valueFormatter={(value) => formatCurrency(value)}
                    />
                  )}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>

            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Asset Distribution</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Breakdown of your portfolio by asset type
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="h-[300px]">
                  {portfolio.composition && (
                    <PieChart
                      data={portfolio.composition}
                      nameKey="name"
                      dataKey="value"
                      valueFormatter={(value) => formatCurrency(value)}
                    />
                  )}
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Performance Metrics</AnimatedCardTitle>
              <AnimatedCardDescription>
                Key performance indicators for your portfolio
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">24h Change</h3>
                  <p className={`text-2xl font-bold ${portfolio.performance?.change24h && portfolio.performance.change24h > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {formatPercentage(portfolio.performance?.change24h || 0)}
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">7d Change</h3>
                  <p className={`text-2xl font-bold ${portfolio.performance?.change7d && portfolio.performance.change7d > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {formatPercentage(portfolio.performance?.change7d || 0)}
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">30d Change</h3>
                  <p className={`text-2xl font-bold ${portfolio.performance?.change30d && portfolio.performance.change30d > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {formatPercentage(portfolio.performance?.change30d || 0)}
                  </p>
                </div>
              </div>
            </AnimatedCardContent>
          </AnimatedCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function PortfolioSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24" />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-28" />
        <Skeleton className="h-28" />
        <Skeleton className="h-28" />
        <Skeleton className="h-28" />
      </div>

      <Skeleton className="h-80" />
    </div>
  );
}
