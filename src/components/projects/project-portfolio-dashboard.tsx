"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  <PERSON><PERSON>hart,
  PieChart,
  Folder,
  Search,
  Filter,
  Grid,
  List,
  ArrowUpDown,
  ChevronDown,
  ChevronUp,
  Plus,
  FileText,
  Calendar,
  MapPin,
  Tag,
  Leaf,
  Zap,
  Droplets,
  Truck,
  Factory,
  Wind,
  TreePine,
  Recycle,
  Building,
  RotateCcw,
  Link,
  Link2Off,
  ExternalLink
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedCard } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

// Project type icons
const PROJECT_TYPE_ICONS: Record<string, React.ReactNode> = {
  RENEWABLE_ENERGY: <Wind className="h-4 w-4" />,
  FORESTRY: <TreePine className="h-4 w-4" />,
  METHANE_REDUCTION: <Factory className="h-4 w-4" />,
  ENERGY_EFFICIENCY: <Zap className="h-4 w-4" />,
  WASTE_MANAGEMENT: <Recycle className="h-4 w-4" />,
  AGRICULTURE: <Leaf className="h-4 w-4" />,
  TRANSPORTATION: <Truck className="h-4 w-4" />,
  INDUSTRIAL: <Factory className="h-4 w-4" />,
  OTHER: <Building className="h-4 w-4" />,
};

// Project status colors
const PROJECT_STATUS_COLORS: Record<string, string> = {
  PENDING: "bg-yellow-500",
  ACTIVE: "bg-green-500",
  COMPLETED: "bg-blue-500",
  SUSPENDED: "bg-red-500",
};

// Project verification status colors
const VERIFICATION_STATUS_COLORS: Record<string, string> = {
  PENDING: "bg-yellow-500",
  IN_PROGRESS: "bg-blue-500",
  VERIFIED: "bg-green-500",
  REJECTED: "bg-red-500",
};

// Project interface
interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  verificationStatus: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  country?: string;
  standard?: string;
  methodology?: string;
  estimatedReductions?: number;
  actualReductions?: number;
  createdAt: string;
  updatedAt: string;
  carbonCredits: CarbonCredit[];
  organization: {
    id: string;
    name: string;
  };
}

// Carbon credit interface
interface CarbonCredit {
  id: string;
  name: string;
  quantity: number;
  availableQuantity: number;
  retiredQuantity: number;
  price: number;
  vintage: number;
  standard: string;
  methodology: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Project portfolio dashboard props
interface ProjectPortfolioDashboardProps {
  projects: Project[];
  onProjectSelect?: (projectId: string) => void;
  onCreditSelect?: (creditId: string) => void;
  onLinkCredits?: (projectId: string) => void;
}

export function ProjectPortfolioDashboard({
  projects,
  onProjectSelect,
  onCreditSelect,
  onLinkCredits
}: ProjectPortfolioDashboardProps) {
  const [activeTab, setActiveTab] = useState<string>("grid");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [expandedProjects, setExpandedProjects] = useState<Record<string, boolean>>({});

  // Get unique project types
  const projectTypes = [...new Set(projects.map(project => project.type))];

  // Get unique project statuses
  const projectStatuses = [...new Set(projects.map(project => project.status))];

  // Filter and sort projects
  const filteredProjects = projects.filter(project => {
    // Search term filter
    if (searchTerm && !project.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !project.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Type filter
    if (typeFilter && project.type !== typeFilter) {
      return false;
    }

    // Status filter
    if (statusFilter && project.status !== statusFilter) {
      return false;
    }

    return true;
  }).sort((a, b) => {
    // Sort by selected field
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "type":
        comparison = a.type.localeCompare(b.type);
        break;
      case "status":
        comparison = a.status.localeCompare(b.status);
        break;
      case "credits":
        comparison = b.carbonCredits.length - a.carbonCredits.length;
        break;
      case "date":
        comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        break;
      default:
        comparison = a.name.localeCompare(b.name);
    }

    // Apply sort order
    return sortOrder === "asc" ? comparison : -comparison;
  });

  // Toggle project expansion
  const toggleProjectExpansion = (projectId: string) => {
    setExpandedProjects(prev => ({
      ...prev,
      [projectId]: !prev[projectId]
    }));
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";

    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setTypeFilter("");
    setStatusFilter("");
    setSortBy("name");
    setSortOrder("asc");
  };

  // Calculate total credits for a project
  const calculateTotalCredits = (project: Project) => {
    return project.carbonCredits.reduce((total, credit) => total + credit.quantity, 0);
  };

  // Calculate available credits for a project
  const calculateAvailableCredits = (project: Project) => {
    return project.carbonCredits.reduce((total, credit) => total + credit.availableQuantity, 0);
  };

  // Calculate retired credits for a project
  const calculateRetiredCredits = (project: Project) => {
    return project.carbonCredits.reduce((total, credit) => total + credit.retiredQuantity, 0);
  };

  // Calculate credit utilization percentage
  const calculateUtilizationPercentage = (project: Project) => {
    const total = calculateTotalCredits(project);
    if (total === 0) return 0;

    const retired = calculateRetiredCredits(project);
    return Math.round((retired / total) * 100);
  };

  // Get project type icon
  const getProjectTypeIcon = (type: string) => {
    return PROJECT_TYPE_ICONS[type] || <Building className="h-4 w-4" />;
  };

  // Render grid view
  const renderGridView = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredProjects.map(project => (
          <Card key={project.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full ${PROJECT_STATUS_COLORS[project.status] || "bg-gray-500"} flex items-center justify-center mr-2`}>
                    {getProjectTypeIcon(project.type)}
                  </div>
                  <CardTitle className="text-base truncate">{project.name}</CardTitle>
                </div>
                <Badge variant="outline">{project.type.replace(/_/g, " ")}</Badge>
              </div>
              <CardDescription className="line-clamp-2 mt-1">
                {project.description || "No description available"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span>{formatDate(project.startDate)}</span>
                  </div>
                  {project.location && (
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1 text-muted-foreground" />
                      <span>{project.location}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Carbon Credits</span>
                    <span className="font-medium">{project.carbonCredits.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Volume</span>
                    <span className="font-medium">{calculateTotalCredits(project).toLocaleString()} tons</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Utilization</span>
                    <span className="font-medium">{calculateUtilizationPercentage(project)}%</span>
                  </div>
                  <Progress value={calculateUtilizationPercentage(project)} className="h-2 mt-1" />
                </div>

                <div className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onProjectSelect && onProjectSelect(project.id)}
                  >
                    <Folder className="h-4 w-4 mr-2" />
                    Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onLinkCredits && onLinkCredits(project.id)}
                  >
                    <Link className="h-4 w-4 mr-2" />
                    Manage Credits
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Render list view
  const renderListView = () => {
    return (
      <div className="space-y-4">
        {filteredProjects.map(project => (
          <Collapsible
            key={project.id}
            open={expandedProjects[project.id]}
            onOpenChange={() => toggleProjectExpansion(project.id)}
          >
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full ${PROJECT_STATUS_COLORS[project.status] || "bg-gray-500"} flex items-center justify-center mr-2`}>
                      {getProjectTypeIcon(project.type)}
                    </div>
                    <div>
                      <CardTitle className="text-base">{project.name}</CardTitle>
                      <CardDescription className="line-clamp-1">
                        {project.description || "No description available"}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{project.type.replace(/_/g, " ")}</Badge>
                    <Badge
                      variant="outline"
                      className={`${PROJECT_STATUS_COLORS[project.status] ? `bg-${PROJECT_STATUS_COLORS[project.status].split('-')[1]}-50 text-${PROJECT_STATUS_COLORS[project.status].split('-')[1]}-700 border-${PROJECT_STATUS_COLORS[project.status].split('-')[1]}-200` : ""}`}
                    >
                      {project.status}
                    </Badge>
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        {expandedProjects[project.id] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Start Date</div>
                    <div className="font-medium">{formatDate(project.startDate)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Location</div>
                    <div className="font-medium">{project.location || "N/A"}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Carbon Credits</div>
                    <div className="font-medium">{project.carbonCredits.length}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Total Volume</div>
                    <div className="font-medium">{calculateTotalCredits(project).toLocaleString()} tons</div>
                  </div>
                </div>

                <CollapsibleContent className="pt-4">
                  <Separator className="mb-4" />

                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">Standard</div>
                        <div className="font-medium">{project.standard || "N/A"}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Methodology</div>
                        <div className="font-medium">{project.methodology || "N/A"}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Verification Status</div>
                        <Badge
                          variant="outline"
                          className={`${VERIFICATION_STATUS_COLORS[project.verificationStatus] ? `bg-${VERIFICATION_STATUS_COLORS[project.verificationStatus].split('-')[1]}-50 text-${VERIFICATION_STATUS_COLORS[project.verificationStatus].split('-')[1]}-700 border-${VERIFICATION_STATUS_COLORS[project.verificationStatus].split('-')[1]}-200` : ""}`}
                        >
                          {project.verificationStatus}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="text-sm font-medium">Credit Utilization</div>
                      <div className="flex items-center space-x-2">
                        <Progress value={calculateUtilizationPercentage(project)} className="h-2 flex-1" />
                        <span className="text-sm font-medium">{calculateUtilizationPercentage(project)}%</span>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Total: </span>
                          <span className="font-medium">{calculateTotalCredits(project).toLocaleString()} tons</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Available: </span>
                          <span className="font-medium">{calculateAvailableCredits(project).toLocaleString()} tons</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Retired: </span>
                          <span className="font-medium">{calculateRetiredCredits(project).toLocaleString()} tons</span>
                        </div>
                      </div>
                    </div>

                    {project.carbonCredits.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Associated Carbon Credits</div>
                        <div className="grid grid-cols-1 gap-2">
                          {project.carbonCredits.slice(0, 3).map(credit => (
                            <div
                              key={credit.id}
                              className="flex items-center justify-between p-2 border rounded-md hover:bg-muted/50 cursor-pointer"
                              onClick={() => onCreditSelect && onCreditSelect(credit.id)}
                            >
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                                <div>
                                  <div className="font-medium">{credit.name}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {credit.vintage} • {credit.standard} • {credit.quantity.toLocaleString()} tons
                                  </div>
                                </div>
                              </div>
                              <Badge variant="outline">{credit.status}</Badge>
                            </div>
                          ))}

                          {project.carbonCredits.length > 3 && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-1"
                              onClick={() => onProjectSelect && onProjectSelect(project.id)}
                            >
                              View All {project.carbonCredits.length} Credits
                            </Button>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onProjectSelect && onProjectSelect(project.id)}
                      >
                        <Folder className="h-4 w-4 mr-2" />
                        Project Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onLinkCredits && onLinkCredits(project.id)}
                      >
                        <Link className="h-4 w-4 mr-2" />
                        Manage Credits
                      </Button>
                    </div>
                  </div>
                </CollapsibleContent>
              </CardContent>
            </Card>
          </Collapsible>
        ))}
      </div>
    );
  };

  // Render analytics view
  const renderAnalyticsView = () => {
    // Calculate total credits by type
    const creditsByType = projectTypes.reduce((acc, type) => {
      const projectsOfType = projects.filter(p => p.type === type);
      const totalCredits = projectsOfType.reduce((sum, p) => sum + calculateTotalCredits(p), 0);
      acc[type] = totalCredits;
      return acc;
    }, {} as Record<string, number>);

    // Calculate total credits by status
    const creditsByStatus = projectStatuses.reduce((acc, status) => {
      const projectsOfStatus = projects.filter(p => p.status === status);
      const totalCredits = projectsOfStatus.reduce((sum, p) => sum + calculateTotalCredits(p), 0);
      acc[status] = totalCredits;
      return acc;
    }, {} as Record<string, number>);

    // Calculate total, available, and retired credits
    const totalCredits = projects.reduce((sum, p) => sum + calculateTotalCredits(p), 0);
    const availableCredits = projects.reduce((sum, p) => sum + calculateAvailableCredits(p), 0);
    const retiredCredits = projects.reduce((sum, p) => sum + calculateRetiredCredits(p), 0);

    // Calculate utilization percentage
    const utilizationPercentage = totalCredits > 0 ? Math.round((retiredCredits / totalCredits) * 100) : 0;

    // Get top projects by credit volume
    const topProjects = [...projects]
      .sort((a, b) => calculateTotalCredits(b) - calculateTotalCredits(a))
      .slice(0, 5);

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Total Carbon Credits</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{totalCredits.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">tons CO₂e</div>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Available</span>
                  <span className="font-medium">{availableCredits.toLocaleString()} tons</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Retired</span>
                  <span className="font-medium">{retiredCredits.toLocaleString()} tons</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Utilization</span>
                  <span className="font-medium">{utilizationPercentage}%</span>
                </div>
                <Progress value={utilizationPercentage} className="h-2 mt-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Credits by Project Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(creditsByType)
                  .sort(([, a], [, b]) => b - a)
                  .map(([type, credits]) => (
                    <div key={type} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          {getProjectTypeIcon(type)}
                          <span className="ml-2">{type.replace(/_/g, " ")}</span>
                        </div>
                        <span className="font-medium">{credits.toLocaleString()} tons</span>
                      </div>
                      <Progress
                        value={(credits / totalCredits) * 100}
                        className="h-2"
                      />
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Credits by Project Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(creditsByStatus)
                  .sort(([, a], [, b]) => b - a)
                  .map(([status, credits]) => (
                    <div key={status} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${PROJECT_STATUS_COLORS[status] || "bg-gray-500"} mr-2`}></div>
                          <span>{status}</span>
                        </div>
                        <span className="font-medium">{credits.toLocaleString()} tons</span>
                      </div>
                      <Progress
                        value={(credits / totalCredits) * 100}
                        className={`h-2 ${PROJECT_STATUS_COLORS[status] || "bg-gray-500"}`}
                      />
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Top Projects by Credit Volume</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProjects.map(project => (
                <div
                  key={project.id}
                  className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50 cursor-pointer"
                  onClick={() => onProjectSelect && onProjectSelect(project.id)}
                >
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full ${PROJECT_STATUS_COLORS[project.status] || "bg-gray-500"} flex items-center justify-center mr-3`}>
                      {getProjectTypeIcon(project.type)}
                    </div>
                    <div>
                      <div className="font-medium">{project.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {project.type.replace(/_/g, " ")} • {project.location || "No location"} • {project.carbonCredits.length} credits
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{calculateTotalCredits(project).toLocaleString()} tons</div>
                    <div className="text-xs text-muted-foreground">
                      {calculateUtilizationPercentage(project)}% utilized
                    </div>
                  </div>
                </div>
              ))}

              {projects.length > 5 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => setActiveTab("list")}
                >
                  View All {projects.length} Projects
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Project Status Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center py-4">
                <div className="grid grid-cols-2 gap-4">
                  {projectStatuses.map(status => {
                    const count = projects.filter(p => p.status === status).length;
                    const percentage = Math.round((count / projects.length) * 100);

                    return (
                      <div key={status} className="flex items-center">
                        <div className={`w-4 h-4 rounded-full ${PROJECT_STATUS_COLORS[status] || "bg-gray-500"} mr-2`}></div>
                        <div>
                          <div className="text-sm font-medium">{status}</div>
                          <div className="text-xs text-muted-foreground">
                            {count} projects ({percentage}%)
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Project Type Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center py-4">
                <div className="grid grid-cols-2 gap-4">
                  {projectTypes.map(type => {
                    const count = projects.filter(p => p.type === type).length;
                    const percentage = Math.round((count / projects.length) * 100);

                    return (
                      <div key={type} className="flex items-center">
                        {getProjectTypeIcon(type)}
                        <div className="ml-2">
                          <div className="text-sm font-medium">{type.replace(/_/g, " ")}</div>
                          <div className="text-xs text-muted-foreground">
                            {count} projects ({percentage}%)
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Folder className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No Projects Found</h3>
        <p className="text-muted-foreground text-center max-w-md mb-6">
          {searchTerm || typeFilter || statusFilter ?
            "No projects match your current filters. Try adjusting your search criteria." :
            "You don't have any projects yet. Create your first project to get started."}
        </p>
        {(searchTerm || typeFilter || statusFilter) && (
          <Button
            variant="outline"
            onClick={resetFilters}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset Filters
          </Button>
        )}
      </div>
    );
  };

  return (
    <AnimatedCard>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Project Portfolio</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onLinkCredits && onLinkCredits("new")}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </div>
        </div>
        <CardDescription>
          Manage your projects and associated carbon credits
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                {projectTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.replace(/_/g, " ")}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                {projectStatuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Sort and view options */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSortOrder(sortOrder === "asc" ? "desc" : "asc");
              }}
            >
              <ArrowUpDown className="h-4 w-4 mr-2" />
              {sortOrder === "asc" ? "Ascending" : "Descending"}
            </Button>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="type">Type</SelectItem>
                <SelectItem value="status">Status</SelectItem>
                <SelectItem value="credits">Credits</SelectItem>
                <SelectItem value="date">Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-1">
            <Button
              variant={activeTab === "grid" ? "default" : "outline"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setActiveTab("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={activeTab === "list" ? "default" : "outline"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setActiveTab("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={activeTab === "analytics" ? "default" : "outline"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setActiveTab("analytics")}
            >
              <BarChart className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Results count */}
        <div className="text-sm text-muted-foreground">
          Showing {filteredProjects.length} of {projects.length} projects
          {(searchTerm || typeFilter || statusFilter) && (
            <Button
              variant="link"
              size="sm"
              className="ml-2 h-auto p-0"
              onClick={resetFilters}
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset Filters
            </Button>
          )}
        </div>

        {/* Content based on active tab */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {filteredProjects.length === 0 ? (
              renderEmptyState()
            ) : (
              <>
                {activeTab === "grid" && renderGridView()}
                {activeTab === "list" && renderListView()}
                {activeTab === "analytics" && renderAnalyticsView()}
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </CardContent>
    </AnimatedCard>
  );
