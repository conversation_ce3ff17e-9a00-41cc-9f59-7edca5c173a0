"use client";

import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { HelpCard } from "@/components/ui/contextual-help";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  AlertCircle,
  Edit,
  FileCheck,
  MapPin,
  Calendar,
  DollarSign,
  Wind,
  Trees,
  Recycle,
  Factory,
  Droplets,
  Leaf,
  Building,
  Info
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Project type icons
const PROJECT_TYPE_ICONS: Record<string, JSX.Element> = {
  "RENEWABLE_ENERGY": <Wind className="h-5 w-5" />,
  "FORESTRY": <Trees className="h-5 w-5" />,
  "WASTE_MANAGEMENT": <Recycle className="h-5 w-5" />,
  "INDUSTRIAL": <Factory className="h-5 w-5" />,
  "WATER": <Droplets className="h-5 w-5" />,
  "AGRICULTURE": <Leaf className="h-5 w-5" />,
  "COMMUNITY": <Building className="h-5 w-5" />
};

// Project standards
const PROJECT_STANDARDS: Record<string, string> = {
  "VERRA": "Verra VCS",
  "GOLD_STANDARD": "Gold Standard",
  "CDM": "Clean Development Mechanism",
  "AMERICAN_CARBON_REGISTRY": "American Carbon Registry",
  "CLIMATE_ACTION_RESERVE": "Climate Action Reserve",
  "PLAN_VIVO": "Plan Vivo",
  "OTHER": "Other"
};

interface ReviewStepProps {
  formValues: any;
  onEdit: (step: number) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  error: string | null;
}

export function ReviewStep({ formValues, onEdit, onSubmit, isSubmitting, error }: ReviewStepProps) {
  const { formState } = useFormContext();
  const [activeTab, setActiveTab] = useState("summary");

  const { errors } = formState;

  // Check if each section is valid
  const isProjectTypeValid = !errors.projectType;
  const isBasicInfoValid = !errors.name && !errors.description && !errors.summary;
  const isLocationValid = !errors.country && !errors.coordinates;
  const isMethodologyValid = !errors.standard && !errors.methodology;
  const isTimelineValid = !errors.startDate && !errors.endDate && !errors.creditingPeriod;
  const isFinancialsValid = !errors.estimatedCredits && !errors.estimatedPrice && !errors.investmentRequired;

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Review Your Project",
          description: "Review all the information you've provided for your project before submitting. You can go back to any section to make changes. Once submitted, your project will be created and you can continue to manage it from your dashboard.",
          type: "info",
          tips: [
            "Check all sections for accuracy before submitting",
            "Ensure all validation issues are resolved",
            "Review the financial projections to confirm viability",
            "You can edit your project after submission, but some changes may require re-verification"
          ]
        }}
        className="mb-6"
      />

      <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="summary" className="flex items-center">
            <FileCheck className="h-4 w-4 mr-2" />
            Project Summary
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center">
            <Info className="h-4 w-4 mr-2" />
            Full Details
          </TabsTrigger>
          <TabsTrigger value="validation" className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            Validation Status
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary">
          <motion.div
            className="space-y-6"
            variants={staggeredListVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Project Type & Basic Info</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(1)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      {PROJECT_TYPE_ICONS[formValues.projectType] || <Info className="h-5 w-5" />}
                      <span className="ml-2 font-medium">{formValues.projectType?.replace('_', ' ')}</span>
                    </div>

                    <div>
                      <h3 className="font-medium">{formValues.name}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{formValues.summary}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Location</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(3)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-muted-foreground" />
                    <div className="ml-2">
                      <p>{formValues.country}{formValues.region ? `, ${formValues.region}` : ''}</p>
                      {formValues.coordinates?.latitude && formValues.coordinates?.longitude && (
                        <p className="text-sm text-muted-foreground">
                          Lat: {formValues.coordinates.latitude}, Long: {formValues.coordinates.longitude}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Methodology</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(4)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Standard</p>
                      <p className="font-medium">{PROJECT_STANDARDS[formValues.standard] || formValues.standard}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Methodology</p>
                      <p className="font-medium">{formValues.methodology}</p>
                    </div>
                    {formValues.validationBody && (
                      <div>
                        <p className="text-sm text-muted-foreground">Validation Body</p>
                        <p className="font-medium">{formValues.validationBody}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Timeline</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(5)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Start Date</p>
                      <p className="font-medium">{formatDate(formValues.startDate)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">End Date</p>
                      <p className="font-medium">{formatDate(formValues.endDate)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Crediting Period</p>
                      <p className="font-medium">{formValues.creditingPeriod} years</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Financials</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(6)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Estimated Credits</p>
                      <p className="font-medium">{formValues.estimatedCredits?.toLocaleString() || 0} tCO2e</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Price per Credit</p>
                      <p className="font-medium">{formatCurrency(formValues.estimatedPrice || 0)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Revenue</p>
                      <p className="font-medium">{formatCurrency((formValues.estimatedCredits || 0) * (formValues.estimatedPrice || 0))}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Investment Required</p>
                      <p className="font-medium">{formatCurrency(formValues.investmentRequired || 0)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">ROI</p>
                      <p className="font-medium">{formValues.roi || 0}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </TabsContent>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Project Details</CardTitle>
              <CardDescription>
                Comprehensive view of all project information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Project Type Section */}
              <div>
                <h3 className="text-base font-medium flex items-center mb-4">
                  {PROJECT_TYPE_ICONS[formValues.projectType] || <Info className="h-5 w-5 mr-2" />}
                  Project Type & Basic Information
                  <Button variant="ghost" size="sm" className="ml-2" onClick={() => onEdit(1)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Project Type</h4>
                    <Badge className="mb-2">
                      {formValues.projectType?.replace('_', ' ')}
                    </Badge>
                    <p className="text-sm text-muted-foreground">
                      {formValues.projectType === 'RENEWABLE_ENERGY' && "Projects that generate renewable energy and reduce greenhouse gas emissions by displacing fossil fuel-based electricity."}
                      {formValues.projectType === 'FORESTRY' && "Projects that protect, restore, or establish forests to sequester carbon and preserve biodiversity."}
                      {formValues.projectType === 'WASTE_MANAGEMENT' && "Projects that reduce methane emissions from waste through improved waste management practices."}
                      {formValues.projectType === 'INDUSTRIAL' && "Projects that reduce emissions from industrial processes through efficiency improvements or technology changes."}
                      {formValues.projectType === 'WATER' && "Projects that improve water quality, conservation, or access while reducing emissions."}
                      {formValues.projectType === 'AGRICULTURE' && "Projects that implement sustainable agricultural practices to reduce emissions and sequester carbon in soils."}
                      {formValues.projectType === 'COMMUNITY' && "Projects that benefit communities while reducing emissions, such as clean cookstoves or water purification."}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Basic Information</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Project Name</p>
                        <p className="font-medium">{formValues.name}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Summary</p>
                        <p>{formValues.summary}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Description</p>
                        <p className="text-sm">{formValues.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Location Section */}
              <div>
                <h3 className="text-base font-medium flex items-center mb-4">
                  <MapPin className="h-5 w-5 mr-2" />
                  Location Information
                  <Button variant="ghost" size="sm" className="ml-2" onClick={() => onEdit(3)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Geographic Location</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Country</p>
                        <p className="font-medium">{formValues.country}</p>
                      </div>
                      {formValues.region && (
                        <div>
                          <p className="text-xs text-muted-foreground">Region/State</p>
                          <p className="font-medium">{formValues.region}</p>
                        </div>
                      )}
                      {formValues.city && (
                        <div>
                          <p className="text-xs text-muted-foreground">City/Locality</p>
                          <p className="font-medium">{formValues.city}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Coordinates</h4>
                    {formValues.coordinates?.latitude && formValues.coordinates?.longitude ? (
                      <div className="space-y-3">
                        <div>
                          <p className="text-xs text-muted-foreground">Latitude</p>
                          <p className="font-medium">{formValues.coordinates.latitude}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Longitude</p>
                          <p className="font-medium">{formValues.coordinates.longitude}</p>
                        </div>
                        <div className="pt-2">
                          <Badge variant="outline" className="text-xs">
                            <MapPin className="h-3 w-3 mr-1" />
                            Location Pinned
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No coordinates specified</p>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Methodology Section */}
              <div>
                <h3 className="text-base font-medium flex items-center mb-4">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Methodology & Standards
                  <Button variant="ghost" size="sm" className="ml-2" onClick={() => onEdit(4)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Carbon Standard</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Standard</p>
                        <p className="font-medium">{PROJECT_STANDARDS[formValues.standard] || formValues.standard}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Standard Description</p>
                        <p className="text-sm">
                          {formValues.standard === 'VERRA' && "Verra's Verified Carbon Standard (VCS) is the world's most widely used voluntary GHG program."}
                          {formValues.standard === 'GOLD_STANDARD' && "Gold Standard certifies projects that reduce carbon emissions while contributing to sustainable development."}
                          {formValues.standard === 'CDM' && "The Clean Development Mechanism allows emission-reduction projects in developing countries to earn certified emission reduction credits."}
                          {formValues.standard === 'AMERICAN_CARBON_REGISTRY' && "The American Carbon Registry (ACR) is the first private voluntary GHG registry in the United States."}
                          {formValues.standard === 'CLIMATE_ACTION_RESERVE' && "The Climate Action Reserve is a national offsets program focused on ensuring environmental integrity of GHG emission reduction projects."}
                          {formValues.standard === 'PLAN_VIVO' && "Plan Vivo is a standard for community land use projects that focuses on sustainable development and improving rural livelihoods."}
                          {formValues.standard === 'OTHER' && "Custom or other carbon standard."}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Methodology</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Methodology Name</p>
                        <p className="font-medium">{formValues.methodology}</p>
                      </div>
                      {formValues.validationBody && (
                        <div>
                          <p className="text-xs text-muted-foreground">Validation Body</p>
                          <p className="font-medium">{formValues.validationBody}</p>
                        </div>
                      )}
                      {formValues.methodologyVersion && (
                        <div>
                          <p className="text-xs text-muted-foreground">Methodology Version</p>
                          <p className="font-medium">{formValues.methodologyVersion}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Timeline Section */}
              <div>
                <h3 className="text-base font-medium flex items-center mb-4">
                  <Calendar className="h-5 w-5 mr-2" />
                  Project Timeline
                  <Button variant="ghost" size="sm" className="ml-2" onClick={() => onEdit(5)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Key Dates</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Start Date</p>
                        <p className="font-medium">{formatDate(formValues.startDate)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">End Date</p>
                        <p className="font-medium">{formatDate(formValues.endDate)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Project Duration</p>
                        <p className="font-medium">
                          {formValues.startDate && formValues.endDate ? (
                            `${Math.ceil((new Date(formValues.endDate).getTime() - new Date(formValues.startDate).getTime()) / (1000 * 60 * 60 * 24 * 365))} years`
                          ) : (
                            "Not specified"
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Crediting Period</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Crediting Period Length</p>
                        <p className="font-medium">{formValues.creditingPeriod} years</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Crediting Period End</p>
                        <p className="font-medium">
                          {formValues.startDate && formValues.creditingPeriod ? (
                            formatDate(new Date(new Date(formValues.startDate).setFullYear(new Date(formValues.startDate).getFullYear() + formValues.creditingPeriod)).toISOString().split('T')[0])
                          ) : (
                            "Not specified"
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Verification Frequency</p>
                        <p className="font-medium">Every 2 years (typical)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Financials Section */}
              <div>
                <h3 className="text-base font-medium flex items-center mb-4">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Financial Information
                  <Button variant="ghost" size="sm" className="ml-2" onClick={() => onEdit(6)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Carbon Credits</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Total Estimated Credits</p>
                        <p className="font-medium">{formValues.estimatedCredits?.toLocaleString() || 0} tCO2e</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Price per Credit</p>
                        <p className="font-medium">{formatCurrency(formValues.estimatedPrice || 0)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Annual Credits (Average)</p>
                        <p className="font-medium">
                          {formValues.estimatedCredits && formValues.creditingPeriod ? (
                            `${(formValues.estimatedCredits / formValues.creditingPeriod).toFixed(0)} tCO2e/year`
                          ) : (
                            "Not specified"
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Financial Metrics</h4>
                    <div className="space-y-3">
                      <div>
                        <p className="text-xs text-muted-foreground">Total Revenue</p>
                        <p className="font-medium">{formatCurrency((formValues.estimatedCredits || 0) * (formValues.estimatedPrice || 0))}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Investment Required</p>
                        <p className="font-medium">{formatCurrency(formValues.investmentRequired || 0)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Return on Investment</p>
                        <p className="font-medium">{formValues.roi || 0}%</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Net Profit</p>
                        <p className="font-medium">
                          {formatCurrency(((formValues.estimatedCredits || 0) * (formValues.estimatedPrice || 0)) - (formValues.investmentRequired || 0))}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validation">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Validation Status</CardTitle>
              <CardDescription>
                Check if all sections of your project are complete and valid.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isProjectTypeValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Project Type</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(1)}
                    disabled={isProjectTypeValid}
                  >
                    {isProjectTypeValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isBasicInfoValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Basic Information</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(2)}
                    disabled={isBasicInfoValid}
                  >
                    {isBasicInfoValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isLocationValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Location</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(3)}
                    disabled={isLocationValid}
                  >
                    {isLocationValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isMethodologyValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Methodology</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(4)}
                    disabled={isMethodologyValid}
                  >
                    {isMethodologyValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isTimelineValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Timeline</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(5)}
                    disabled={isTimelineValid}
                  >
                    {isTimelineValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isFinancialsValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
                    )}
                    <span>Financials</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(6)}
                    disabled={isFinancialsValid}
                  >
                    {isFinancialsValid ? "Complete" : "Fix Issues"}
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                {Object.keys(errors).length > 0 && (
                  <p className="text-sm text-amber-500 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    Please fix all validation issues before submitting
                  </p>
                )}
              </div>
              <Button
                onClick={() => setActiveTab("summary")}
                variant="outline"
              >
                View Summary
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="space-y-4 mt-6">
        {error && (
          <Card className="border-red-500 bg-red-50 dark:bg-red-950/20">
            <CardContent className="pt-6">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-red-700 dark:text-red-400">Error Submitting Project</h3>
                  <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={() => onEdit(6)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Back to Edit
          </Button>

          <div className="flex items-center gap-2">
            {Object.keys(errors).length > 0 && (
              <p className="text-sm text-amber-500 hidden md:flex items-center mr-2">
                <AlertCircle className="h-4 w-4 mr-1" />
                Fix validation issues before submitting
              </p>
            )}

            <Button
              onClick={onSubmit}
              disabled={isSubmitting || Object.keys(errors).length > 0}
              className="min-w-[160px]"
            >
              {isSubmitting ? (
                <>
                  <span className="mr-2">Submitting...</span>
                  <span className="animate-spin">
                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                </>
              ) : (
                <>
                  <FileCheck className="mr-2 h-4 w-4" />
                  Submit Project
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
