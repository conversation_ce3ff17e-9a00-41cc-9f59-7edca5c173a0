"use client";

import { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import { AnimatedInput } from "@/components/ui/animated";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import {
  DollarSign,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Calculator,
  Info,
  AlertCircle,
  RefreshCw,
  PieChart,
  LineChart,
  BarChart2,
  ArrowRight,
  Coins,
  Banknote,
  Landmark,
  Percent,
  CreditCard,
  Wallet
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

export function FinancialsStep() {
  const { control, watch, setValue } = useFormContext();
  const estimatedCredits = watch("estimatedCredits");
  const estimatedPrice = watch("estimatedPrice");
  const investmentRequired = watch("investmentRequired");
  const roi = watch("roi");
  const creditingPeriod = watch("creditingPeriod");

  // State for calculator dialog
  const [calculatorOpen, setCalculatorOpen] = useState(false);
  const [calculatorValues, setCalculatorValues] = useState({
    developmentCosts: 0,
    implementationCosts: 0,
    operationalCosts: 0,
    verificationCosts: 0,
    registrationFees: 0,
    otherCosts: 0
  });

  // State for chart data
  const [yearlyData, setYearlyData] = useState<Array<{year: number, credits: number, revenue: number, costs: number, profit: number}>>([]);

  // Calculate ROI
  useEffect(() => {
    if (estimatedCredits && estimatedPrice && investmentRequired && investmentRequired > 0) {
      const totalRevenue = estimatedCredits * estimatedPrice;
      const calculatedRoi = ((totalRevenue - investmentRequired) / investmentRequired) * 100;
      setValue("roi", parseFloat(calculatedRoi.toFixed(2)), { shouldValidate: true });
    }
  }, [estimatedCredits, estimatedPrice, investmentRequired, setValue]);

  // Calculate annual values
  const calculateAnnualValues = () => {
    if (!creditingPeriod || creditingPeriod <= 0) return null;

    const annualCredits = estimatedCredits / creditingPeriod;
    const annualRevenue = annualCredits * estimatedPrice;
    const annualInvestment = investmentRequired / creditingPeriod;

    return {
      credits: annualCredits.toFixed(2),
      revenue: annualRevenue.toFixed(2),
      investment: annualInvestment.toFixed(2)
    };
  };

  const annualValues = calculateAnnualValues();

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Calculate total from calculator values
  const calculateTotalInvestment = () => {
    return Object.values(calculatorValues).reduce((sum, value) => sum + value, 0);
  };

  // Handle calculator value change
  const handleCalculatorValueChange = (key: string, value: number) => {
    setCalculatorValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Apply calculator values to form
  const applyCalculatorValues = () => {
    const total = calculateTotalInvestment();
    setValue("investmentRequired", total, { shouldValidate: true });
    setCalculatorOpen(false);
  };

  // Generate yearly data for charts
  useEffect(() => {
    if (estimatedCredits && estimatedPrice && investmentRequired && creditingPeriod) {
      const data = [];
      const annualCredits = estimatedCredits / creditingPeriod;

      // Assume 60% of investment is upfront, 40% spread over the crediting period
      const upfrontInvestment = investmentRequired * 0.6;
      const annualOperatingCosts = (investmentRequired * 0.4) / creditingPeriod;

      for (let year = 1; year <= creditingPeriod; year++) {
        const yearCredits = annualCredits;
        const yearRevenue = yearCredits * estimatedPrice;
        const yearCosts = year === 1
          ? upfrontInvestment + annualOperatingCosts
          : annualOperatingCosts;
        const yearProfit = yearRevenue - yearCosts;

        data.push({
          year,
          credits: yearCredits,
          revenue: yearRevenue,
          costs: yearCosts,
          profit: yearProfit
        });
      }

      setYearlyData(data);
    }
  }, [estimatedCredits, estimatedPrice, investmentRequired, creditingPeriod]);

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Project Financials",
          description: "Provide financial estimates for your project, including expected carbon credit generation, pricing, and investment requirements. These estimates will help you evaluate the financial viability of your project.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/financial-planning",
          learnMoreText: "Learn more about financial planning for carbon projects",
          tips: [
            "Use the investment calculator to break down your costs",
            "Higher quality projects typically command higher credit prices",
            "Consider both upfront and ongoing operational costs",
            "Review the financial projections to understand your ROI over time"
          ]
        }}
        className="mb-6"
      />

      {/* Investment Calculator Dialog */}
      <Dialog open={calculatorOpen} onOpenChange={setCalculatorOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Investment Calculator</DialogTitle>
            <DialogDescription>
              Break down your project investment into different cost categories to get a more accurate total.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <Landmark className="h-4 w-4 mr-2" />
                    Development Costs
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.developmentCosts)}</span>
                </div>
                <Slider
                  value={[calculatorValues.developmentCosts]}
                  min={0}
                  max={500000}
                  step={1000}
                  onValueChange={(value) => handleCalculatorValueChange('developmentCosts', value[0])}
                />
                <p className="text-xs text-muted-foreground">Project design, feasibility studies, methodology development</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <Banknote className="h-4 w-4 mr-2" />
                    Implementation Costs
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.implementationCosts)}</span>
                </div>
                <Slider
                  value={[calculatorValues.implementationCosts]}
                  min={0}
                  max={1000000}
                  step={5000}
                  onValueChange={(value) => handleCalculatorValueChange('implementationCosts', value[0])}
                />
                <p className="text-xs text-muted-foreground">Equipment, infrastructure, initial setup costs</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Operational Costs
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.operationalCosts)}</span>
                </div>
                <Slider
                  value={[calculatorValues.operationalCosts]}
                  min={0}
                  max={500000}
                  step={1000}
                  onValueChange={(value) => handleCalculatorValueChange('operationalCosts', value[0])}
                />
                <p className="text-xs text-muted-foreground">Ongoing maintenance, staff, monitoring over project lifetime</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Verification Costs
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.verificationCosts)}</span>
                </div>
                <Slider
                  value={[calculatorValues.verificationCosts]}
                  min={0}
                  max={200000}
                  step={1000}
                  onValueChange={(value) => handleCalculatorValueChange('verificationCosts', value[0])}
                />
                <p className="text-xs text-muted-foreground">Validation, verification, and auditing costs</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Registration Fees
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.registrationFees)}</span>
                </div>
                <Slider
                  value={[calculatorValues.registrationFees]}
                  min={0}
                  max={100000}
                  step={500}
                  onValueChange={(value) => handleCalculatorValueChange('registrationFees', value[0])}
                />
                <p className="text-xs text-muted-foreground">Standard registry fees, issuance fees, listing fees</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium flex items-center">
                    <Wallet className="h-4 w-4 mr-2" />
                    Other Costs
                  </label>
                  <span className="text-sm font-medium">{formatCurrency(calculatorValues.otherCosts)}</span>
                </div>
                <Slider
                  value={[calculatorValues.otherCosts]}
                  min={0}
                  max={200000}
                  step={1000}
                  onValueChange={(value) => handleCalculatorValueChange('otherCosts', value[0])}
                />
                <p className="text-xs text-muted-foreground">Contingency, legal, insurance, and other miscellaneous costs</p>
              </div>
            </div>

            <div className="mt-4 p-4 bg-muted rounded-lg">
              <div className="flex items-center justify-between">
                <span className="font-medium">Total Investment:</span>
                <span className="font-bold text-lg">{formatCurrency(calculateTotalInvestment())}</span>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCalculatorOpen(false)}>Cancel</Button>
            <Button onClick={applyCalculatorValues}>Apply to Project</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <motion.div
        className="space-y-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="estimatedCredits"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Estimated Carbon Credits (Total)
                  <InlineHelp
                    content={{
                      title: "Estimated Carbon Credits",
                      description: "The total number of carbon credits (in tCO2e) you expect your project to generate over its crediting period.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="number"
                      min={0}
                      step={100}
                      className="pl-10"
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      <BarChart className="h-4 w-4" />
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  Total carbon credits (tCO2e) expected over the crediting period.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="estimatedPrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Estimated Price per Credit (USD)
                  <InlineHelp
                    content={{
                      title: "Estimated Price per Credit",
                      description: "The expected selling price for each carbon credit in USD. This varies by project type, standard, and market conditions.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="number"
                      min={0}
                      step={0.5}
                      className="pl-10"
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      <DollarSign className="h-4 w-4" />
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  Expected selling price per carbon credit (USD).
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="investmentRequired"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Investment Required (USD)
                  <InlineHelp
                    content={{
                      title: "Investment Required",
                      description: "The total investment needed to implement and maintain your project over its lifetime, including development, verification, and operational costs.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="number"
                      min={0}
                      step={1000}
                      className="pl-10 pr-10"
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      <DollarSign className="h-4 w-4" />
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2"
                      onClick={() => setCalculatorOpen(true)}
                    >
                      <Calculator className="h-4 w-4" />
                    </Button>
                  </div>
                </FormControl>
                <FormDescription className="flex items-center justify-between">
                  <span>Total investment required for project implementation and operation.</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-7 text-xs"
                    onClick={() => setCalculatorOpen(true)}
                  >
                    <Calculator className="h-3 w-3 mr-1" />
                    Use Calculator
                  </Button>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <FormField
            control={control}
            name="roi"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Return on Investment (%)
                  <InlineHelp
                    content={{
                      title: "Return on Investment",
                      description: "The expected ROI for your project, calculated based on the estimated credits, price, and investment required.",
                      type: "info",
                    }}
                  />
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <AnimatedInput
                      {...field}
                      type="number"
                      readOnly
                      className="pl-10 bg-muted"
                    />
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      <TrendingUp className="h-4 w-4" />
                    </div>
                  </div>
                </FormControl>
                <FormDescription className="flex items-center">
                  <span>Calculated automatically based on your inputs.</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 ml-1"
                    onClick={() => {
                      if (estimatedCredits && estimatedPrice && investmentRequired && investmentRequired > 0) {
                        const totalRevenue = estimatedCredits * estimatedPrice;
                        const calculatedRoi = ((totalRevenue - investmentRequired) / investmentRequired) * 100;
                        setValue("roi", parseFloat(calculatedRoi.toFixed(2)), { shouldValidate: true });
                      }
                    }}
                  >
                    <RefreshCw className="h-3 w-3" />
                  </Button>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>
      </motion.div>

      <Tabs defaultValue="summary" className="mt-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="summary" className="flex items-center">
            <PieChart className="h-4 w-4 mr-2" />
            Financial Summary
          </TabsTrigger>
          <TabsTrigger value="annual" className="flex items-center">
            <BarChart2 className="h-4 w-4 mr-2" />
            Annual Breakdown
          </TabsTrigger>
          <TabsTrigger value="projections" className="flex items-center">
            <LineChart className="h-4 w-4 mr-2" />
            Projections
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Coins className="h-5 w-5 mr-2" />
                Project Financial Summary
              </CardTitle>
              <CardDescription>
                Overview of your project's financial metrics based on current estimates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-muted/50">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium">Credits & Revenue</h3>
                      <Badge variant="outline" className="bg-green-500/10 text-green-600">
                        Income
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Total Credits</span>
                        <span className="font-medium">{estimatedCredits?.toLocaleString() || 0} tCO2e</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Credit Price</span>
                        <span className="font-medium">{formatCurrency(estimatedPrice || 0)}/credit</span>
                      </div>
                      <div className="flex justify-between items-center border-t pt-2 mt-2">
                        <span className="text-sm font-medium">Total Revenue</span>
                        <span className="text-lg font-bold text-green-600">{formatCurrency((estimatedCredits || 0) * (estimatedPrice || 0))}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/50">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium">Investment</h3>
                      <Badge variant="outline" className="bg-red-500/10 text-red-600">
                        Costs
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Development</span>
                        <span className="font-medium">{formatCurrency((investmentRequired || 0) * 0.2)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Implementation</span>
                        <span className="font-medium">{formatCurrency((investmentRequired || 0) * 0.4)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Operations</span>
                        <span className="font-medium">{formatCurrency((investmentRequired || 0) * 0.4)}</span>
                      </div>
                      <div className="flex justify-between items-center border-t pt-2 mt-2">
                        <span className="text-sm font-medium">Total Investment</span>
                        <span className="text-lg font-bold text-red-600">{formatCurrency(investmentRequired || 0)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/50">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium">Profitability</h3>
                      <Badge variant="outline" className="bg-blue-500/10 text-blue-600">
                        Returns
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Net Profit</span>
                        <span className="font-medium">
                          {formatCurrency(((estimatedCredits || 0) * (estimatedPrice || 0)) - (investmentRequired || 0))}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Profit Margin</span>
                        <span className="font-medium">
                          {(estimatedCredits && estimatedPrice)
                            ? (((estimatedCredits * estimatedPrice) - (investmentRequired || 0)) / (estimatedCredits * estimatedPrice) * 100).toFixed(1)
                            : 0}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center border-t pt-2 mt-2">
                        <span className="text-sm font-medium">ROI</span>
                        <span className="text-lg font-bold text-blue-600">{roi || 0}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Visual representation of financial breakdown */}
              <div className="mt-6 p-4 border rounded-lg">
                <h3 className="text-sm font-medium mb-4">Financial Breakdown</h3>
                <div className="relative h-8 bg-muted rounded-full overflow-hidden">
                  {/* Revenue bar */}
                  <div
                    className="absolute top-0 left-0 h-full bg-green-500 rounded-l-full"
                    style={{ width: '100%' }}
                  ></div>

                  {/* Investment bar */}
                  <div
                    className="absolute top-0 left-0 h-full bg-red-500"
                    style={{
                      width: `${investmentRequired && estimatedCredits && estimatedPrice
                        ? Math.min(100, (investmentRequired / (estimatedCredits * estimatedPrice)) * 100)
                        : 0}%`
                    }}
                  ></div>

                  {/* Profit indicator */}
                  <div
                    className="absolute top-0 h-full border-r-2 border-white"
                    style={{
                      left: `${investmentRequired && estimatedCredits && estimatedPrice
                        ? Math.min(100, (investmentRequired / (estimatedCredits * estimatedPrice)) * 100)
                        : 0}%`,
                      display: investmentRequired && estimatedCredits && estimatedPrice ? 'block' : 'none'
                    }}
                  ></div>
                </div>

                <div className="flex justify-between mt-2 text-xs">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                    <span>Investment: {formatCurrency(investmentRequired || 0)}</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                    <span>Revenue: {formatCurrency((estimatedCredits || 0) * (estimatedPrice || 0))}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="annual">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <BarChart2 className="h-5 w-5 mr-2" />
                Annual Financial Breakdown
              </CardTitle>
              <CardDescription>
                Year-by-year breakdown of credits, revenue, and costs
              </CardDescription>
            </CardHeader>
            <CardContent>
              {annualValues ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center">
                          <BarChart className="h-5 w-5 mr-2 text-primary" />
                          <h4 className="text-sm font-medium">Annual Credits</h4>
                        </div>
                        <p className="text-lg font-medium mt-2">{annualValues.credits} <span className="text-sm text-muted-foreground">tCO2e/year</span></p>
                      </CardContent>
                    </Card>

                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center">
                          <Coins className="h-5 w-5 mr-2 text-green-600" />
                          <h4 className="text-sm font-medium">Annual Revenue</h4>
                        </div>
                        <p className="text-lg font-medium mt-2">{formatCurrency(parseFloat(annualValues.revenue))} <span className="text-sm text-muted-foreground">/year</span></p>
                      </CardContent>
                    </Card>

                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center">
                          <Wallet className="h-5 w-5 mr-2 text-red-600" />
                          <h4 className="text-sm font-medium">Annual Investment</h4>
                        </div>
                        <p className="text-lg font-medium mt-2">{formatCurrency(parseFloat(annualValues.investment))} <span className="text-sm text-muted-foreground">/year</span></p>
                      </CardContent>
                    </Card>

                    <Card className="bg-muted/50">
                      <CardContent className="pt-4">
                        <div className="flex items-center">
                          <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                          <h4 className="text-sm font-medium">Annual Net</h4>
                        </div>
                        <p className="text-lg font-medium mt-2">
                          {formatCurrency(parseFloat(annualValues.revenue) - parseFloat(annualValues.investment))} <span className="text-sm text-muted-foreground">/year</span>
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Annual bar chart visualization */}
                  <div className="mt-4 p-4 border rounded-lg">
                    <h3 className="text-sm font-medium mb-4">Annual Comparison</h3>
                    <div className="flex items-end h-40 space-x-2">
                      <div className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-green-500 rounded-t-sm" style={{ height: '100%' }}></div>
                        <p className="text-xs mt-1">Credits</p>
                        <p className="text-xs font-medium">{annualValues.credits}</p>
                      </div>

                      <div className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-blue-500 rounded-t-sm" style={{ height: '80%' }}></div>
                        <p className="text-xs mt-1">Revenue</p>
                        <p className="text-xs font-medium">{formatCurrency(parseFloat(annualValues.revenue))}</p>
                      </div>

                      <div className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-red-500 rounded-t-sm" style={{ height: `${parseFloat(annualValues.investment) / parseFloat(annualValues.revenue) * 80}%` }}></div>
                        <p className="text-xs mt-1">Costs</p>
                        <p className="text-xs font-medium">{formatCurrency(parseFloat(annualValues.investment))}</p>
                      </div>

                      <div className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-purple-500 rounded-t-sm" style={{ height: `${(parseFloat(annualValues.revenue) - parseFloat(annualValues.investment)) / parseFloat(annualValues.revenue) * 80}%` }}></div>
                        <p className="text-xs mt-1">Profit</p>
                        <p className="text-xs font-medium">{formatCurrency(parseFloat(annualValues.revenue) - parseFloat(annualValues.investment))}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-dashed rounded-md">
                  <Calculator className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                    Complete all financial fields and set a crediting period to see the annual breakdown of your project's finances.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projections">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <LineChart className="h-5 w-5 mr-2" />
                Financial Projections
              </CardTitle>
              <CardDescription>
                Year-by-year projections over the crediting period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {yearlyData.length > 0 ? (
                <div className="space-y-6">
                  {/* Cumulative chart visualization */}
                  <div className="p-4 border rounded-lg">
                    <h3 className="text-sm font-medium mb-4">Cumulative Financial Projection</h3>
                    <div className="relative h-60">
                      {/* X-axis */}
                      <div className="absolute bottom-0 left-0 w-full h-px bg-border"></div>

                      {/* Y-axis */}
                      <div className="absolute top-0 left-0 h-full w-px bg-border"></div>

                      {/* Investment line */}
                      <div className="absolute bottom-0 left-0 w-full border-t border-dashed border-red-500"></div>

                      {/* Break-even point */}
                      {yearlyData.findIndex(d => {
                        const yearIndex = yearlyData.indexOf(d);
                        const cumulativeRevenue = yearlyData
                          .slice(0, yearIndex + 1)
                          .reduce((sum, item) => sum + item.revenue, 0);
                        const cumulativeCosts = yearlyData
                          .slice(0, yearIndex + 1)
                          .reduce((sum, item) => sum + item.costs, 0);
                        return cumulativeRevenue >= cumulativeCosts;
                      }) > -1 && (
                        <div
                          className="absolute bottom-0 h-full border-l border-dashed border-green-500"
                          style={{
                            left: `${(yearlyData.findIndex(d => {
                              const yearIndex = yearlyData.indexOf(d);
                              const cumulativeRevenue = yearlyData
                                .slice(0, yearIndex + 1)
                                .reduce((sum, item) => sum + item.revenue, 0);
                              const cumulativeCosts = yearlyData
                                .slice(0, yearIndex + 1)
                                .reduce((sum, item) => sum + item.costs, 0);
                              return cumulativeRevenue >= cumulativeCosts;
                            }) + 1) / yearlyData.length * 100}%`
                          }}
                        >
                          <div className="absolute -top-6 -translate-x-1/2 text-xs text-green-600 font-medium">
                            Break-even
                          </div>
                        </div>
                      )}

                      {/* Revenue curve */}
                      <svg className="absolute inset-0 h-full w-full" preserveAspectRatio="none">
                        <path
                          d={`M 0,${60 * 0.9} ${yearlyData.map((d, i) => {
                            const x = (i / (yearlyData.length - 1)) * 100;
                            const cumulativeRevenue = yearlyData
                              .slice(0, i + 1)
                              .reduce((sum, item) => sum + item.revenue, 0);
                            const y = 60 * 0.9 - (cumulativeRevenue / (investmentRequired * 2)) * 60 * 0.9;
                            return `L ${x},${y}`;
                          }).join(' ')}`}
                          fill="none"
                          stroke="hsl(142.1 76.2% 36.3%)"
                          strokeWidth="2"
                        />
                      </svg>

                      {/* Costs curve */}
                      <svg className="absolute inset-0 h-full w-full" preserveAspectRatio="none">
                        <path
                          d={`M 0,${60 * 0.9 - (yearlyData[0]?.costs / (investmentRequired * 2)) * 60 * 0.9} ${yearlyData.map((d, i) => {
                            const x = (i / (yearlyData.length - 1)) * 100;
                            const cumulativeCosts = yearlyData
                              .slice(0, i + 1)
                              .reduce((sum, item) => sum + item.costs, 0);
                            const y = 60 * 0.9 - (cumulativeCosts / (investmentRequired * 2)) * 60 * 0.9;
                            return `L ${x},${y}`;
                          }).join(' ')}`}
                          fill="none"
                          stroke="hsl(0 84.2% 60.2%)"
                          strokeWidth="2"
                        />
                      </svg>

                      {/* X-axis labels */}
                      <div className="absolute bottom-0 left-0 w-full flex justify-between text-xs text-muted-foreground">
                        {yearlyData.filter((_, i) => i % Math.ceil(yearlyData.length / 5) === 0 || i === yearlyData.length - 1).map((d, i) => (
                          <div key={i} className="absolute" style={{ left: `${(d.year - 1) / (yearlyData.length - 1) * 100}%`, bottom: '-20px' }}>
                            Year {d.year}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex justify-center mt-8 space-x-4 text-xs">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                        <span>Cumulative Revenue</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-red-600 rounded-full mr-1"></div>
                        <span>Cumulative Costs</span>
                      </div>
                    </div>
                  </div>

                  {/* Year-by-year table */}
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-3 text-xs font-medium">Year</th>
                          <th className="text-right py-2 px-3 text-xs font-medium">Credits</th>
                          <th className="text-right py-2 px-3 text-xs font-medium">Revenue</th>
                          <th className="text-right py-2 px-3 text-xs font-medium">Costs</th>
                          <th className="text-right py-2 px-3 text-xs font-medium">Profit</th>
                          <th className="text-right py-2 px-3 text-xs font-medium">Cumulative</th>
                        </tr>
                      </thead>
                      <tbody>
                        {yearlyData.map((year, index) => {
                          const cumulativeProfit = yearlyData
                            .slice(0, index + 1)
                            .reduce((sum, item) => sum + item.profit, 0);

                          return (
                            <tr key={index} className="border-b">
                              <td className="py-2 px-3 text-xs">{year.year}</td>
                              <td className="py-2 px-3 text-xs text-right">{year.credits.toFixed(0)}</td>
                              <td className="py-2 px-3 text-xs text-right text-green-600">{formatCurrency(year.revenue)}</td>
                              <td className="py-2 px-3 text-xs text-right text-red-600">{formatCurrency(year.costs)}</td>
                              <td className="py-2 px-3 text-xs text-right">{formatCurrency(year.profit)}</td>
                              <td className="py-2 px-3 text-xs text-right font-medium">
                                <span className={cumulativeProfit >= 0 ? "text-green-600" : "text-red-600"}>
                                  {formatCurrency(cumulativeProfit)}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border border-dashed rounded-md">
                  <LineChart className="h-10 w-10 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                    Complete all financial fields and set a crediting period to see year-by-year financial projections for your project.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-4 text-sm text-muted-foreground">
        <p className="flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          These are estimates only. Actual results may vary based on market conditions and verification outcomes.
        </p>
      </div>
    </div>
  );
}
