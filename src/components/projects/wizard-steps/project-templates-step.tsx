"use client";

import { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCard, InlineHelp } from "@/components/ui/contextual-help";
import {
  Wind,
  Trees,
  Recycle,
  Factory,
  Droplets,
  Leaf,
  Building,
  Info,
  Lightbulb,
  FileText
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

// Define project templates
const PROJECT_TEMPLATES = [
  {
    id: "BLANK",
    name: "Blank Project",
    icon: <FileText className="h-5 w-5" />,
    description: "Start with a blank project and customize all details.",
    longDescription: "Choose this option if you want to set up your project from scratch with full customization options.",
    difficulty: "Variable",
    timeToComplete: "30-60 minutes",
    popularity: "Medium",
    defaultValues: {}
  },
  {
    id: "RENEWABLE_ENERGY_SOLAR",
    name: "Solar Energy Project",
    icon: <Wind className="h-5 w-5" />,
    description: "Template for solar energy generation projects.",
    longDescription: "Solar energy projects generate renewable electricity through photovoltaic panels or concentrated solar power systems, reducing reliance on fossil fuels.",
    difficulty: "Medium",
    timeToComplete: "20-40 minutes",
    popularity: "High",
    marketDemand: "High",
    averagePrice: "$15-25 per credit",
    defaultValues: {
      projectType: "RENEWABLE_ENERGY",
      name: "Solar Energy Generation Project",
      description: "This project generates renewable electricity through solar photovoltaic technology, reducing greenhouse gas emissions by displacing fossil fuel-based electricity.",
      summary: "Solar energy project reducing carbon emissions through clean electricity generation.",
      standard: "VERRA",
      methodology: "VM0025 - Campus Clean Energy and Energy Efficiency",
      creditingPeriod: 10,
      estimatedPrice: 15,
      estimatedCredits: 5000
    }
  },
  {
    id: "FORESTRY_CONSERVATION",
    name: "Forest Conservation",
    icon: <Trees className="h-5 w-5" />,
    description: "Template for forest conservation and REDD+ projects.",
    longDescription: "Forest conservation projects protect existing forests from deforestation and degradation, preserving carbon stocks and biodiversity.",
    difficulty: "High",
    timeToComplete: "25-45 minutes",
    popularity: "Very High",
    marketDemand: "Very High",
    averagePrice: "$18-30 per credit",
    defaultValues: {
      projectType: "FORESTRY",
      name: "Forest Conservation and REDD+ Project",
      description: "This project protects existing forest from deforestation and degradation, preserving carbon stocks and biodiversity while supporting local communities.",
      summary: "Forest conservation project preventing deforestation and supporting sustainable development.",
      standard: "VERRA",
      methodology: "VM0007 - REDD+ Methodology Framework",
      creditingPeriod: 30,
      estimatedPrice: 20,
      estimatedCredits: 10000
    }
  },
  {
    id: "COMMUNITY_COOKSTOVES",
    name: "Clean Cookstoves",
    icon: <Building className="h-5 w-5" />,
    description: "Template for community clean cookstove projects.",
    longDescription: "Clean cookstove projects distribute efficient cooking technologies to communities, reducing fuel consumption, indoor air pollution, and greenhouse gas emissions.",
    difficulty: "Medium",
    timeToComplete: "15-30 minutes",
    popularity: "High",
    marketDemand: "Medium-High",
    averagePrice: "$12-18 per credit",
    defaultValues: {
      projectType: "COMMUNITY",
      name: "Clean Cookstoves Distribution Project",
      description: "This project distributes efficient cookstoves to communities, reducing fuel consumption, indoor air pollution, and greenhouse gas emissions while improving health outcomes.",
      summary: "Clean cookstove project reducing emissions and improving community health.",
      standard: "GOLD_STANDARD",
      methodology: "GS Simplified Methodology for Efficient Cookstoves",
      creditingPeriod: 7,
      estimatedPrice: 12,
      estimatedCredits: 3000
    }
  },
  {
    id: "AGRICULTURE_SOIL",
    name: "Soil Carbon Sequestration",
    icon: <Leaf className="h-5 w-5" />,
    description: "Template for agricultural soil carbon sequestration projects.",
    longDescription: "Soil carbon sequestration projects implement sustainable agricultural practices that increase carbon storage in soils while improving soil health and productivity.",
    difficulty: "Medium-High",
    timeToComplete: "20-40 minutes",
    popularity: "Growing",
    marketDemand: "Growing",
    averagePrice: "$15-25 per credit",
    defaultValues: {
      projectType: "AGRICULTURE",
      name: "Soil Carbon Sequestration Project",
      description: "This project implements sustainable agricultural practices that increase carbon storage in soils while improving soil health, water retention, and crop productivity.",
      summary: "Agricultural project sequestering carbon in soils through sustainable practices.",
      standard: "VERRA",
      methodology: "VM0017 - Adoption of Sustainable Agricultural Land Management",
      creditingPeriod: 20,
      estimatedPrice: 18,
      estimatedCredits: 4000
    }
  }
];

interface ProjectTemplatesStepProps {
  onSelectTemplate: (templateId: string, defaultValues: any) => void;
}

export function ProjectTemplatesStep({ onSelectTemplate }: ProjectTemplatesStepProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>("BLANK");

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = PROJECT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      onSelectTemplate(templateId, template.defaultValues);
    }
  };

  return (
    <div className="space-y-6">
      <HelpCard
        content={{
          title: "Project Templates",
          description: "Choose a template to pre-fill common project details or start with a blank project. Templates provide a head start by automatically configuring project type, methodology, and other key settings based on common carbon project types.",
          type: "info",
          learnMoreLink: "https://docs.carbon-exchange.com/projects/templates",
          learnMoreText: "Learn more about project templates",
          tips: [
            "Templates save time by pre-filling many required fields",
            "You can modify any pre-filled values in later steps",
            "Choose 'Blank Project' for complete customization",
            "Hover over the info icon to see detailed template information"
          ]
        }}
        className="mb-6"
      />

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        {PROJECT_TEMPLATES.map((template) => (
          <motion.div
            key={template.id}
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedTemplate === template.id
                  ? 'border-2 border-primary ring-2 ring-primary/20'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => handleTemplateSelect(template.id)}
            >
              <CardHeader className="pb-2 relative">
                {selectedTemplate === template.id && (
                  <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1 shadow-md">
                    <Check className="h-4 w-4" />
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`p-2 rounded-full ${
                      selectedTemplate === template.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}>
                      {template.icon}
                    </div>
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Info className="h-4 w-4" />
                          <span className="sr-only">More information</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="max-w-sm">
                        <p className="font-semibold">{template.name}</p>
                        <p className="mt-1">{template.longDescription || template.description}</p>
                        {template.id !== "BLANK" && (
                          <>
                            <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1">
                              <div>
                                <p className="font-semibold text-xs">Difficulty:</p>
                                <p className="text-xs">{template.difficulty}</p>
                              </div>
                              <div>
                                <p className="font-semibold text-xs">Time to Complete:</p>
                                <p className="text-xs">{template.timeToComplete}</p>
                              </div>
                              <div>
                                <p className="font-semibold text-xs">Popularity:</p>
                                <p className="text-xs">{template.popularity}</p>
                              </div>
                              {template.marketDemand && (
                                <div>
                                  <p className="font-semibold text-xs">Market Demand:</p>
                                  <p className="text-xs">{template.marketDemand}</p>
                                </div>
                              )}
                              {template.averagePrice && (
                                <div>
                                  <p className="font-semibold text-xs">Average Price:</p>
                                  <p className="text-xs">{template.averagePrice}</p>
                                </div>
                              )}
                            </div>
                            <div className="mt-2">
                              <p className="font-semibold text-xs">Pre-filled fields:</p>
                              <ul className="text-xs list-disc pl-4 mt-1">
                                {Object.entries(template.defaultValues)
                                  .filter(([key]) => !["description", "summary"].includes(key))
                                  .map(([key, value]) => (
                                    <li key={key}>{key}: {value}</li>
                                  ))
                                }
                              </ul>
                            </div>
                          </>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{template.description}</p>

                {template.id !== "BLANK" && (
                  <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center">
                      <span className="font-medium mr-1">Difficulty:</span>
                      <span className="text-muted-foreground">{template.difficulty}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-1">Time:</span>
                      <span className="text-muted-foreground">{template.timeToComplete}</span>
                    </div>
                  </div>
                )}
              </CardContent>
              <div className={`h-1 w-full mt-auto ${
                selectedTemplate === template.id ? 'bg-primary' : 'bg-transparent'
              }`}></div>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <div className="flex justify-end mt-6">
        <Button
          onClick={() => {
            const template = PROJECT_TEMPLATES.find(t => t.id === selectedTemplate);
            if (template) {
              onSelectTemplate(selectedTemplate, template.defaultValues);
            }
          }}
        >
          Continue with Template
          <Lightbulb className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
