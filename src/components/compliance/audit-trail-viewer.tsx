"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { 
  FileText, 
  Search, 
  Loader2, 
  Calendar, 
  Shield, 
  AlertCircle, 
  CheckCircle, 
  FileCheck,
  Download,
  User,
  Building,
  Wallet,
  ArrowRight,
  Clock,
  Filter,
  RefreshCw
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  Pagin<PERSON>, 
  PaginationContent, 
  Pa<PERSON>ationEllipsis, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from "@/components/ui/pagination";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

interface AuditLog {
  id: string;
  type: string;
  description: string;
  userId?: string;
  organizationId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

interface AuditTrailViewerProps {
  userId?: string;
  organizationId?: string;
  isAdmin?: boolean;
}

export function AuditTrailViewer({
  userId,
  organizationId,
  isAdmin = false,
}: AuditTrailViewerProps) {
  const router = useRouter();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isFiltering, setIsFiltering] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  const ITEMS_PER_PAGE = 10;

  // Load audit logs
  useEffect(() => {
    fetchAuditLogs();
  }, [page, selectedTypes, startDate, endDate, activeTab]);

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    setIsLoading(true);
    
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("limit", ITEMS_PER_PAGE.toString());
      
      if (searchQuery) {
        params.append("search", searchQuery);
      }
      
      if (selectedTypes.length > 0) {
        selectedTypes.forEach(type => params.append("types", type));
      }
      
      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }
      
      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }
      
      // Add tab-specific filters
      if (activeTab === "kyc") {
        params.append("types", "KYC_VERIFICATION_REQUESTED");
        params.append("types", "KYC_VERIFICATION_APPROVED");
        params.append("types", "KYC_VERIFICATION_REJECTED");
        params.append("types", "DOCUMENT_VERIFIED");
      } else if (activeTab === "aml") {
        params.append("types", "AML_CHECK_PERFORMED");
      } else if (activeTab === "tax") {
        params.append("types", "TAX_REPORT_GENERATED");
      }
      
      // Add user or organization filter if not admin
      if (!isAdmin) {
        if (userId) {
          params.append("userId", userId);
        }
        if (organizationId) {
          params.append("organizationId", organizationId);
        }
      }
      
      const response = await fetch(`/api/compliance/audit-logs?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch audit logs");
      }
      
      const data = await response.json();
      setAuditLogs(data.auditLogs);
      setTotalPages(Math.ceil(data.pagination.total / ITEMS_PER_PAGE));
      setTotalLogs(data.pagination.total);
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      toast({
        title: "Error",
        description: "Failed to fetch audit logs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    setPage(1);
    fetchAuditLogs();
  };

  // Handle filter change
  const handleFilterChange = () => {
    setPage(1);
    fetchAuditLogs();
  };

  // Reset filters
  const resetFilters = () => {
    setSelectedTypes([]);
    setStartDate(undefined);
    setEndDate(undefined);
    setSearchQuery("");
    setPage(1);
    setIsFiltering(false);
    fetchAuditLogs();
  };

  // Get icon for audit log type
  const getAuditLogTypeIcon = (type: string) => {
    if (type.includes("KYC")) {
      return <Shield className="h-4 w-4" />;
    } else if (type.includes("AML")) {
      return <AlertCircle className="h-4 w-4" />;
    } else if (type.includes("TAX")) {
      return <FileText className="h-4 w-4" />;
    } else if (type.includes("DOCUMENT")) {
      return <FileCheck className="h-4 w-4" />;
    } else if (type.includes("VERIFICATION")) {
      return <CheckCircle className="h-4 w-4" />;
    } else {
      return <FileText className="h-4 w-4" />;
    }
  };

  // Format audit log type
  const formatAuditLogType = (type: string) => {
    return type.replace(/_/g, " ");
  };

  // Get badge variant for audit log type
  const getAuditLogTypeBadgeVariant = (type: string) => {
    if (type.includes("KYC")) {
      return "secondary";
    } else if (type.includes("AML")) {
      return "destructive";
    } else if (type.includes("TAX")) {
      return "default";
    } else if (type.includes("DOCUMENT")) {
      return "outline";
    } else if (type.includes("VERIFICATION")) {
      return "success";
    } else {
      return "outline";
    }
  };

  // Format metadata for display
  const formatMetadata = (metadata: any) => {
    if (!metadata) return null;
    
    // Format specific metadata fields
    const formattedMetadata = { ...metadata };
    
    // Format dates
    if (formattedMetadata.timestamp) {
      try {
        formattedMetadata.timestamp = format(new Date(formattedMetadata.timestamp), "MMM d, yyyy HH:mm:ss");
      } catch (error) {
        // Keep original if parsing fails
      }
    }
    
    // Format arrays
    Object.keys(formattedMetadata).forEach(key => {
      if (Array.isArray(formattedMetadata[key])) {
        formattedMetadata[key] = formattedMetadata[key].map((item: any) => {
          if (typeof item === 'object') {
            return JSON.stringify(item, null, 2);
          }
          return item;
        }).join(", ");
      }
    });
    
    return formattedMetadata;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Compliance Audit Trail</CardTitle>
        <CardDescription>
          View detailed audit logs for compliance-related activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search audit logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleSearch}
                disabled={isLoading}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <Popover open={isFiltering} onOpenChange={setIsFiltering}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-4" align="end">
                  <div className="space-y-4">
                    <h4 className="font-medium">Filter Audit Logs</h4>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Log Types</label>
                      <Select
                        value={selectedTypes[0] || ""}
                        onValueChange={(value) => {
                          if (value) {
                            setSelectedTypes([value]);
                          } else {
                            setSelectedTypes([]);
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select log type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Types</SelectItem>
                          <SelectItem value="KYC_VERIFICATION_REQUESTED">KYC Verification Requested</SelectItem>
                          <SelectItem value="KYC_VERIFICATION_APPROVED">KYC Verification Approved</SelectItem>
                          <SelectItem value="KYC_VERIFICATION_REJECTED">KYC Verification Rejected</SelectItem>
                          <SelectItem value="AML_CHECK_PERFORMED">AML Check Performed</SelectItem>
                          <SelectItem value="DOCUMENT_VERIFIED">Document Verified</SelectItem>
                          <SelectItem value="TAX_REPORT_GENERATED">Tax Report Generated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Date Range</label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="text-xs text-muted-foreground">Start Date</label>
                          <CalendarComponent
                            mode="single"
                            selected={startDate}
                            onSelect={setStartDate}
                            className="border rounded-md p-2"
                          />
                        </div>
                        <div>
                          <label className="text-xs text-muted-foreground">End Date</label>
                          <CalendarComponent
                            mode="single"
                            selected={endDate}
                            onSelect={setEndDate}
                            className="border rounded-md p-2"
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetFilters}
                      >
                        Reset
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => {
                          handleFilterChange();
                          setIsFiltering(false);
                        }}
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={fetchAuditLogs}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Logs</TabsTrigger>
              <TabsTrigger value="kyc">KYC</TabsTrigger>
              <TabsTrigger value="aml">AML</TabsTrigger>
              <TabsTrigger value="tax">Tax Reports</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="pt-4">
              {renderAuditLogs()}
            </TabsContent>
            
            <TabsContent value="kyc" className="pt-4">
              {renderAuditLogs()}
            </TabsContent>
            
            <TabsContent value="aml" className="pt-4">
              {renderAuditLogs()}
            </TabsContent>
            
            <TabsContent value="tax" className="pt-4">
              {renderAuditLogs()}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {auditLogs.length} of {totalLogs} logs
        </div>
        
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1 || isLoading}
              />
            </PaginationItem>
            
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNumber = i + 1;
              return (
                <PaginationItem key={pageNumber}>
                  <PaginationLink
                    isActive={pageNumber === page}
                    onClick={() => setPage(pageNumber)}
                    disabled={isLoading}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            
            {totalPages > 5 && (
              <>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink
                    onClick={() => setPage(totalPages)}
                    disabled={isLoading}
                  >
                    {totalPages}
                  </PaginationLink>
                </PaginationItem>
              </>
            )}
            
            <PaginationItem>
              <PaginationNext 
                onClick={() => setPage(Math.min(totalPages, page + 1))}
                disabled={page === totalPages || isLoading}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </CardFooter>
    </Card>
  );

  // Render audit logs
  function renderAuditLogs() {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      );
    }
    
    if (auditLogs.length === 0) {
      return (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No Audit Logs Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No compliance audit logs match your current filters.
          </p>
          {(selectedTypes.length > 0 || startDate || endDate || searchQuery) && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={resetFilters}
            >
              Reset Filters
            </Button>
          )}
        </div>
      );
    }
    
    return (
      <div className="space-y-4">
        <Accordion type="single" collapsible className="w-full">
          {auditLogs.map((log) => (
            <AccordionItem key={log.id} value={log.id}>
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center justify-between w-full pr-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                      {getAuditLogTypeIcon(log.type)}
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{log.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(log.createdAt), "MMM d, yyyy HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                  <Badge variant={getAuditLogTypeBadgeVariant(log.type)}>
                    {formatAuditLogType(log.type)}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 p-4 bg-muted/50 rounded-md">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">User</h4>
                      <div className="flex items-center text-sm">
                        <User className="h-3 w-3 mr-1 text-muted-foreground" />
                        {log.userId || "N/A"}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Organization</h4>
                      <div className="flex items-center text-sm">
                        <Building className="h-3 w-3 mr-1 text-muted-foreground" />
                        {log.organizationId || "N/A"}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">IP Address</h4>
                      <div className="flex items-center text-sm">
                        <Wallet className="h-3 w-3 mr-1 text-muted-foreground" />
                        {log.ipAddress || "N/A"}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Timestamp</h4>
                      <div className="flex items-center text-sm">
                        <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                        {format(new Date(log.createdAt), "MMM d, yyyy HH:mm:ss")}
                      </div>
                    </div>
                  </div>
                  
                  {log.metadata && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Details</h4>
                      <div className="bg-background p-3 rounded-md text-xs font-mono overflow-x-auto">
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(formatMetadata(log.metadata), null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                  
                  {log.userAgent && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">User Agent</h4>
                      <div className="text-xs text-muted-foreground truncate">
                        {log.userAgent}
                      </div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    );
  }
}
