"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { Coins, Loader2, Zap } from "lucide-react";
import { TransactionStatus } from "@/components/blockchain/transaction-status";

// Schema for tokenization form
const tokenizationFormSchema = z.object({
  network: z.enum(["ethereum", "polygon", "arbitrum", "optimism", "base"]),
  useTestnet: z.boolean(),
});

type TokenizationFormValues = z.infer<typeof tokenizationFormSchema>;

interface EnhancedTokenizeDialogProps {
  carbonCreditId: string;
  disabled?: boolean;
  onSuccess?: () => void;
}

export function EnhancedTokenizeDialog({
  carbonCreditId,
  disabled = false,
  onSuccess,
}: EnhancedTokenizeDialogProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gasEstimation, setGasEstimation] = useState<any>(null);
  const [isEstimatingGas, setIsEstimatingGas] = useState(false);
  const [currentStep, setCurrentStep] = useState<"form" | "processing" | "complete">("form");
  const [transactionHash, setTransactionHash] = useState<string | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<string>("polygon");
  const [isTestnet, setIsTestnet] = useState<boolean>(true);

  // Initialize form
  const form = useForm<TokenizationFormValues>({
    resolver: zodResolver(tokenizationFormSchema),
    defaultValues: {
      network: "polygon",
      useTestnet: true,
    },
  });

  // Estimate gas when form values change
  const estimateGas = async (data: TokenizationFormValues) => {
    setIsEstimatingGas(true);
    setGasEstimation(null);

    try {
      const response = await fetch(`/api/gas-estimation/tokenize`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          carbonCreditId,
          network: data.network,
          useTestnet: data.useTestnet,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to estimate gas");
      }

      const result = await response.json();
      setGasEstimation(result);
    } catch (error) {
      console.error("Error estimating gas:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to estimate gas",
        variant: "destructive",
      });
    } finally {
      setIsEstimatingGas(false);
    }
  };

  // Watch form values and estimate gas when they change
  const watchNetwork = form.watch("network");
  const watchUseTestnet = form.watch("useTestnet");

  // Update gas estimation when form values change
  const updateGasEstimation = async () => {
    const data = {
      network: watchNetwork,
      useTestnet: watchUseTestnet,
    };
    await estimateGas(data);
  };

  // Form submission handler
  const onSubmit = async (data: TokenizationFormValues) => {
    setIsSubmitting(true);
    setCurrentStep("processing");
    setSelectedNetwork(data.network);
    setIsTestnet(data.useTestnet);

    try {
      const response = await fetch(`/api/carbon-credits/${carbonCreditId}/tokenize`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to tokenize carbon credit");
      }

      const result = await response.json();
      
      // Set transaction hash for tracking
      if (result.tokenizationResult && result.tokenizationResult.transactionHash) {
        setTransactionHash(result.tokenizationResult.transactionHash);
      } else {
        throw new Error("No transaction hash returned");
      }
      
      // Don't close dialog yet - we'll show the transaction status
    } catch (error) {
      console.error("Error tokenizing carbon credit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to tokenize carbon credit",
        variant: "destructive",
      });
      
      // Go back to form step on error
      setCurrentStep("form");
      setIsSubmitting(false);
    }
  };
  
  // Handle transaction completion
  const handleTransactionComplete = (success: boolean) => {
    setIsSubmitting(false);
    setCurrentStep("complete");
    
    if (success) {
      toast({
        title: "Carbon Credit Tokenized",
        description: "Your carbon credit has been successfully tokenized on the blockchain.",
      });
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
      
      // Refresh the page after a short delay
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } else {
      toast({
        title: "Tokenization Failed",
        description: "The transaction failed or was dropped. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    // Prevent closing while submitting
    if (isSubmitting && open === false) {
      toast({
        title: "Transaction in Progress",
        description: "Please wait for the transaction to complete before closing.",
      });
      return;
    }
    
    // Reset state when closing
    if (!open) {
      // Only reset if not in the middle of a transaction
      if (!isSubmitting) {
        setCurrentStep("form");
        setTransactionHash(null);
        form.reset({
          network: "polygon",
          useTestnet: true,
        });
      }
    }
    
    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>
        <Button disabled={disabled} variant="outline" size="sm">
          <Coins className="mr-2 h-4 w-4" />
          Tokenize
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        {currentStep === "form" && (
          <>
            <DialogHeader>
              <DialogTitle>Tokenize Carbon Credit</DialogTitle>
              <DialogDescription>
                Tokenize your carbon credit on the blockchain to enable trading and retirement.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="network"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Blockchain Network</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            // Estimate gas after a short delay
                            setTimeout(updateGasEstimation, 100);
                          }}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a network" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ethereum">Ethereum</SelectItem>
                            <SelectItem value="polygon">Polygon</SelectItem>
                            <SelectItem value="arbitrum">Arbitrum</SelectItem>
                            <SelectItem value="optimism">Optimism</SelectItem>
                            <SelectItem value="base">Base</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Select the blockchain network where you want to tokenize your carbon credit.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="useTestnet"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Use Testnet</FormLabel>
                        <FormDescription>
                          Use testnet for testing purposes. No real value will be transferred.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            // Estimate gas after a short delay
                            setTimeout(updateGasEstimation, 100);
                          }}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {gasEstimation && (
                  <div className="rounded-lg border p-4 space-y-2">
                    <h4 className="font-medium">Gas Estimation</h4>
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Gas Price:</span>
                        <span>{parseFloat(gasEstimation.gasPrice).toFixed(2)} Gwei</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Gas Limit:</span>
                        <span>{parseInt(gasEstimation.gasLimit).toLocaleString()}</span>
                      </div>
                      <Separator className="my-2" />
                      <div className="flex justify-between font-medium">
                        <span>Estimated Cost:</span>
                        <span>
                          {gasEstimation.costInEther.toFixed(6)}{" "}
                          {watchNetwork === "ethereum" || watchNetwork === "optimism" || watchNetwork === "arbitrum" || watchNetwork === "base"
                            ? "ETH"
                            : watchNetwork === "polygon"
                            ? "MATIC"
                            : ""}
                        </span>
                      </div>
                      {gasEstimation.usdCost && (
                        <div className="flex justify-between text-muted-foreground">
                          <span>USD Equivalent:</span>
                          <span>${gasEstimation.usdCost.toFixed(2)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || isEstimatingGas}
                    className="gap-1"
                  >
                    {isEstimatingGas ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Estimating Gas...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4" />
                        Tokenize
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}

        {currentStep === "processing" && transactionHash && (
          <>
            <DialogHeader>
              <DialogTitle>Tokenizing Carbon Credit</DialogTitle>
              <DialogDescription>
                Your carbon credit is being tokenized on the blockchain. This process may take a few minutes.
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <TransactionStatus
                txHash={transactionHash}
                network={selectedNetwork}
                isTestnet={isTestnet}
                onComplete={handleTransactionComplete}
              />
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={true}
              >
                Please Wait...
              </Button>
            </DialogFooter>
          </>
        )}
        
        {currentStep === "complete" && (
          <>
            <DialogHeader>
              <DialogTitle>Tokenization Complete</DialogTitle>
              <DialogDescription>
                Your carbon credit has been tokenized on the blockchain.
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                <Coins className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium">Successfully Tokenized</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Your carbon credit is now represented as a token on the blockchain and can be traded or retired.
              </p>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                onClick={() => setIsOpen(false)}
              >
                Close
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
