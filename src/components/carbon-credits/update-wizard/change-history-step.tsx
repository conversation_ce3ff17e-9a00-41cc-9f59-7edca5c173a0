"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertCircle,
  ArrowRight,
  Calendar,
  ChevronDown,
  ChevronRight,
  Clock,
  User,
  Search,
  Filter,
  CheckCircle,
  ShoppingCart,
  Archive,
  Eye,
  EyeOff,
  Diff,
  History,
  RotateCcw,
  GitCompare,
  FileText
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { motion, AnimatePresence } from "framer-motion";

interface ChangeHistoryStepProps {
  history: any[];
}

export function ChangeHistoryStep({ history }: ChangeHistoryStepProps) {
  const [expandedChanges, setExpandedChanges] = useState<Record<string, boolean>>({});
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);
  const [compareVersions, setCompareVersions] = useState<{from: string | null, to: string | null}>({from: null, to: null});
  const [searchTerm, setSearchTerm] = useState("");
  const [filterField, setFilterField] = useState<string | null>(null);
  const [filterUser, setFilterUser] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Toggle expanded state for a change
  const toggleExpanded = (id: string) => {
    setExpandedChanges(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
    }

    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} ${diffInYears === 1 ? 'year' : 'years'} ago`;
  };

  // Format value based on field type
  const formatValue = (field: string, value: any) => {
    if (field === "price") {
      return `$${parseFloat(value).toFixed(2)}`;
    }

    if (field === "status") {
      const statusLabels: Record<string, string> = {
        "PENDING": "Pending",
        "VERIFIED": "Verified",
        "LISTED": "Listed",
        "RETIRED": "Retired",
      };

      return statusLabels[value] || value;
    }

    if (value === undefined || value === null) {
      return "—";
    }

    return value.toString();
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "LISTED":
        return <ShoppingCart className="h-4 w-4 text-blue-500" />;
      case "RETIRED":
        return <Archive className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };

  // Field labels for display
  const fieldLabels: Record<string, string> = {
    name: "Name",
    description: "Description",
    vintage: "Vintage Year",
    standard: "Standard",
    methodology: "Methodology",
    location: "Location",
    price: "Price per Ton",
    minPurchaseQuantity: "Minimum Purchase Quantity",
    status: "Status",
  };

  // Get all unique users from history
  const uniqueUsers = [...new Set(history.map(change => change.user))];

  // Get all unique fields from history
  const uniqueFields = [...new Set(
    history.flatMap(change => Object.keys(change.changes))
  )];

  // Filter history based on search and filters
  const filteredHistory = history.filter(change => {
    // Search term filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const userMatch = change.user.toLowerCase().includes(searchLower);
      const fieldsMatch = Object.keys(change.changes).some(field =>
        fieldLabels[field]?.toLowerCase().includes(searchLower)
      );
      const valuesMatch = Object.entries(change.changes).some(([field, { from, to }]) =>
        formatValue(field, from).toLowerCase().includes(searchLower) ||
        formatValue(field, to).toLowerCase().includes(searchLower)
      );

      if (!(userMatch || fieldsMatch || valuesMatch)) {
        return false;
      }
    }

    // Field filter
    if (filterField && !Object.keys(change.changes).includes(filterField)) {
      return false;
    }

    // User filter
    if (filterUser && change.user !== filterUser) {
      return false;
    }

    return true;
  });

  // Check if there is history
  const hasHistory = history.length > 0;

  // Get version by ID
  const getVersionById = (id: string | null) => {
    if (!id) return null;
    return history.find(h => h.id === id);
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setFilterField(null);
    setFilterUser(null);
  };

  // Render the visual timeline
  const renderVisualTimeline = () => {
    if (!hasHistory) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No change history available for this carbon credit.
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="space-y-4">
        {/* Search and filters */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search changes..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {(filterField || filterUser) && (
                <Badge variant="default" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                  {(filterField ? 1 : 0) + (filterUser ? 1 : 0)}
                </Badge>
              )}
            </Button>
            {(searchTerm || filterField || filterUser) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </Button>
            )}
          </div>

          <Collapsible open={showFilters}>
            <CollapsibleContent className="pt-2">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs font-medium">Filter by Field</label>
                  <Select value={filterField || ""} onValueChange={(value) => setFilterField(value || null)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="All fields" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All fields</SelectItem>
                      {uniqueFields.map(field => (
                        <SelectItem key={field} value={field}>
                          {fieldLabels[field] || field}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-xs font-medium">Filter by User</label>
                  <Select value={filterUser || ""} onValueChange={(value) => setFilterUser(value || null)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="All users" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All users</SelectItem>
                      {uniqueUsers.map(user => (
                        <SelectItem key={user} value={user}>{user}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* Results count */}
        <div className="text-sm text-muted-foreground">
          Showing {filteredHistory.length} of {history.length} changes
        </div>

        {/* Timeline */}
        <div className="relative space-y-0">
          {/* Timeline line */}
          <div className="absolute left-[19px] top-6 bottom-6 w-[2px] bg-muted-foreground/20"></div>

          {/* Timeline items */}
          <AnimatePresence initial={false}>
            {filteredHistory.map((change, index) => (
              <motion.div
                key={change.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="relative pl-10 pb-6"
              >
                {/* Timeline dot */}
                <div className="absolute left-0 top-0 h-10 flex items-center">
                  <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center z-10">
                    <History className="h-3 w-3 text-primary-foreground" />
                  </div>
                </div>

                {/* Timeline content */}
                <Card className="relative">
                  {index === 0 && (
                    <Badge className="absolute top-4 right-4 bg-blue-500">
                      Latest
                    </Badge>
                  )}
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {formatDate(change.date)}
                        </span>
                        <Badge variant="outline" className="ml-2 text-xs">
                          {formatRelativeTime(change.date)}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 p-0"
                        onClick={() => toggleExpanded(change.id)}
                      >
                        {expandedChanges[change.id] ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <div className="flex items-center mt-1">
                      <User className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {change.user}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {Object.keys(change.changes).map(field => (
                          <Badge key={field} variant="outline">
                            {fieldLabels[field] || field}
                          </Badge>
                        ))}
                      </div>

                      <AnimatePresence>
                        {expandedChanges[change.id] && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                            className="mt-4 space-y-4 pt-4 border-t overflow-hidden"
                          >
                            {Object.keys(change.changes).map(field => (
                              <div key={field} className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">{fieldLabels[field] || field}</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="flex-1 p-2 bg-muted rounded-md text-sm">
                                    {field === "status" ? (
                                      <div className="flex items-center">
                                        {getStatusIcon(change.changes[field].from)}
                                        <span className="ml-2">{formatValue(field, change.changes[field].from)}</span>
                                      </div>
                                    ) : (
                                      formatValue(field, change.changes[field].from)
                                    )}
                                  </div>
                                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 p-2 bg-primary/10 rounded-md text-sm font-medium">
                                    {field === "status" ? (
                                      <div className="flex items-center">
                                        {getStatusIcon(change.changes[field].to)}
                                        <span className="ml-2">{formatValue(field, change.changes[field].to)}</span>
                                      </div>
                                    ) : (
                                      formatValue(field, change.changes[field].to)
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}

                            <div className="flex justify-end pt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setCompareVersions({
                                    from: index < history.length - 1 ? history[index + 1].id : null,
                                    to: change.id
                                  });
                                  document.querySelector('[data-value="compare"]')?.dispatchEvent(
                                    new MouseEvent('click', { bubbles: true })
                                  );
                                }}
                              >
                                <GitCompare className="h-4 w-4 mr-2" />
                                Compare with Previous
                              </Button>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>

          {filteredHistory.length === 0 && (
            <div className="py-8 text-center">
              <FileText className="h-8 w-8 mx-auto text-muted-foreground/50 mb-2" />
              <p className="text-muted-foreground">No changes match your filters</p>
              <Button
                variant="link"
                size="sm"
                onClick={resetFilters}
                className="mt-2"
              >
                Reset Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render the version comparison
  const renderVersionComparison = () => {
    if (!hasHistory) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No versions available for comparison.
          </AlertDescription>
        </Alert>
      );
    }

    // Initialize compare versions if not set
    useEffect(() => {
      if (compareVersions.from === null && compareVersions.to === null && history.length >= 2) {
        setCompareVersions({
          from: history[1].id,
          to: history[0].id
        });
      }
    }, []);

    const fromVersion = getVersionById(compareVersions.from);
    const toVersion = getVersionById(compareVersions.to);

    // Get all fields that have changed between versions
    const getChangedFields = () => {
      if (!fromVersion || !toVersion) return [];

      // Get fields from both versions
      const fromFields = Object.keys(fromVersion.changes || {});
      const toFields = Object.keys(toVersion.changes || {});

      // Get all unique fields
      return [...new Set([...fromFields, ...toFields])];
    };

    const changedFields = getChangedFields();

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium">From Version</label>
            <Select
              value={compareVersions.from || ""}
              onValueChange={(value) => setCompareVersions(prev => ({ ...prev, from: value || null }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select a version" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">None</SelectItem>
                {history.map((change, index) => (
                  <SelectItem key={change.id} value={change.id}>
                    {formatDate(change.date)} - {change.user}
                    {index === 0 && " (Latest)"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">To Version</label>
            <Select
              value={compareVersions.to || ""}
              onValueChange={(value) => setCompareVersions(prev => ({ ...prev, to: value || null }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select a version" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">None</SelectItem>
                {history.map((change, index) => (
                  <SelectItem key={change.id} value={change.id}>
                    {formatDate(change.date)} - {change.user}
                    {index === 0 && " (Latest)"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {fromVersion && toVersion ? (
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Version Comparison</CardTitle>
                <CardDescription>
                  Comparing changes between {formatDate(fromVersion.date)} and {formatDate(toVersion.date)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4">
                  <div className="grid grid-cols-3 gap-4 font-medium text-sm">
                    <div>Field</div>
                    <div>{fromVersion.user}'s Version</div>
                    <div>{toVersion.user}'s Version</div>
                  </div>
                  <Separator />

                  {changedFields.length > 0 ? (
                    changedFields.map(field => {
                      const fromValue = fromVersion.changes[field]?.to;
                      const toValue = toVersion.changes[field]?.to;
                      const hasChanged = fromValue !== toValue;

                      return (
                        <div key={field} className="grid grid-cols-3 gap-4 items-center">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{fieldLabels[field] || field}</span>
                            {hasChanged && (
                              <Badge variant="default" className="text-xs">Changed</Badge>
                            )}
                          </div>
                          <div>
                            {field === "status" ? (
                              <div className="flex items-center text-sm">
                                {getStatusIcon(fromValue)}
                                <span className="ml-2">{formatValue(field, fromValue)}</span>
                              </div>
                            ) : (
                              <span className="text-sm">{formatValue(field, fromValue)}</span>
                            )}
                          </div>
                          <div>
                            {field === "status" ? (
                              <div className="flex items-center text-sm font-medium">
                                {getStatusIcon(toValue)}
                                <span className="ml-2">
                                  {formatValue(field, toValue)}
                                </span>
                              </div>
                            ) : (
                              <span className={`text-sm ${hasChanged ? "font-medium" : ""}`}>
                                {formatValue(field, toValue)}
                              </span>
                            )}
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="py-4 text-center text-muted-foreground">
                      No differences found between these versions
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">
              Select two versions to compare
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="timeline">
        <TabsList className="w-full">
          <TabsTrigger value="timeline" className="flex-1">
            <History className="h-4 w-4 mr-2" />
            Timeline
          </TabsTrigger>
          <TabsTrigger value="compare" className="flex-1">
            <GitCompare className="h-4 w-4 mr-2" />
            Compare Versions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="timeline" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Change History</CardTitle>
              <CardDescription>
                View the history of changes made to this carbon credit
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {renderVisualTimeline()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compare" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Compare Versions</CardTitle>
              <CardDescription>
                Compare different versions of this carbon credit
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {renderVersionComparison()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
