import { useState, useEffect } from 'react';
import { ProfileField } from '@/components/profile/profile-completion-indicator';

interface UseProfileCompletionProps {
  organizationId: string;
}

interface UseProfileCompletionResult {
  fields: ProfileField[];
  completionPercentage: number;
  loading: boolean;
  error: string | null;
  organization: any;
  refreshProfile: () => Promise<void>;
}

export function useProfileCompletion({ organizationId }: UseProfileCompletionProps): UseProfileCompletionResult {
  const [organization, setOrganization] = useState<any>(null);
  const [fields, setFields] = useState<ProfileField[]>([]);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrganization = async () => {
    if (!organizationId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/organizations/${organizationId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch organization data');
      }
      
      const data = await response.json();
      setOrganization(data.organization);
      
      // Create fields array based on organization data
      const fieldsList: ProfileField[] = [
        { name: 'name', label: 'Organization Name', completed: !!data.organization.name, required: true },
        { name: 'industry', label: 'Industry', completed: !!data.organization.industry, required: true },
        { name: 'country', label: 'Country', completed: !!data.organization.country, required: true },
        { name: 'size', label: 'Organization Size', completed: !!data.organization.size, required: true },
        { name: 'legalName', label: 'Legal Name', completed: !!data.organization.legalName, required: false },
        { name: 'description', label: 'Description', completed: !!data.organization.description, required: false },
        { name: 'website', label: 'Website', completed: !!data.organization.website, required: false },
        { name: 'address', label: 'Address', completed: !!data.organization.address, required: false },
        { name: 'city', label: 'City', completed: !!data.organization.city, required: false },
        { name: 'phoneNumber', label: 'Phone Number', completed: !!data.organization.phoneNumber, required: false },
        { name: 'registrationNumber', label: 'Registration Number', completed: !!data.organization.registrationNumber, required: false },
        { name: 'taxId', label: 'Tax ID', completed: !!data.organization.taxId, required: false },
        { name: 'primaryContact', label: 'Primary Contact', completed: !!data.organization.primaryContact, required: false },
        { name: 'primaryContactEmail', label: 'Contact Email', completed: !!data.organization.primaryContactEmail, required: false },
      ];
      
      setFields(fieldsList);
    } catch (error) {
      console.error('Error fetching organization:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate completion percentage
  useEffect(() => {
    if (fields.length === 0) return;
    
    // Count completed required fields and total required fields
    const requiredFields = fields.filter(field => field.required);
    const completedRequiredFields = requiredFields.filter(field => field.completed);
    
    // Count completed optional fields and total optional fields
    const optionalFields = fields.filter(field => !field.required);
    const completedOptionalFields = optionalFields.filter(field => field.completed);
    
    // Calculate percentage (required fields count more than optional fields)
    const requiredWeight = 0.7; // 70% of the score comes from required fields
    const optionalWeight = 0.3; // 30% of the score comes from optional fields
    
    let requiredPercentage = requiredFields.length > 0 
      ? (completedRequiredFields.length / requiredFields.length) * 100 * requiredWeight 
      : 0;
      
    let optionalPercentage = optionalFields.length > 0 
      ? (completedOptionalFields.length / optionalFields.length) * 100 * optionalWeight 
      : 0;
    
    // If all required fields are completed, ensure at least 70% completion
    if (requiredFields.length > 0 && completedRequiredFields.length === requiredFields.length) {
      requiredPercentage = 70;
    }
    
    const totalPercentage = Math.min(100, Math.round(requiredPercentage + optionalPercentage));
    setCompletionPercentage(totalPercentage);
  }, [fields]);

  // Fetch organization data on mount
  useEffect(() => {
    fetchOrganization();
  }, [organizationId]);

  return {
    fields,
    completionPercentage,
    loading,
    error,
    organization,
    refreshProfile: fetchOrganization
  };
}
