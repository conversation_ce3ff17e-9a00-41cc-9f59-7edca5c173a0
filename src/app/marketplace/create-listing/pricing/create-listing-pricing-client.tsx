"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  AnimatedCard, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  ArrowLeft, 
  ArrowRight, 
  Loader2, 
  AlertTriangle,
  Check,
  Info
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface CreateListingPricingClientProps {
  userName: string;
}

const pricingSchema = z.object({
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).default("FIXED"),
  price: z.number().positive("Price must be positive").optional(),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").default(1),
  auctionEndTime: z.string().optional(),
  auctionReservePrice: z.number().positive("Reserve price must be positive").optional(),
  auctionMinIncrement: z.number().positive("Minimum increment must be positive").optional(),
  dynamicPricingRules: z.string().optional(),
  tieredPricingRules: z.string().optional(),
  allowPartialFills: z.boolean().default(true),
  allowCounterOffers: z.boolean().default(false),
});

type PricingFormValues = z.infer<typeof pricingSchema>;

export default function CreateListingPricingClient({ userName }: CreateListingPricingClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<PricingFormValues>({
    resolver: zodResolver(pricingSchema),
    defaultValues: {
      pricingStrategy: "FIXED",
      price: undefined,
      minPurchaseQuantity: 1,
      auctionEndTime: "",
      auctionReservePrice: undefined,
      auctionMinIncrement: undefined,
      dynamicPricingRules: "",
      tieredPricingRules: "",
      allowPartialFills: true,
      allowCounterOffers: false,
    },
  });

  // Check if we have listing data in session storage
  useEffect(() => {
    const storedData = sessionStorage.getItem("createListingData");
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      if (parsedData.pricingStrategy) {
        form.setValue("pricingStrategy", parsedData.pricingStrategy);
      }
      if (parsedData.price) {
        form.setValue("price", parsedData.price);
      }
      if (parsedData.minPurchaseQuantity) {
        form.setValue("minPurchaseQuantity", parsedData.minPurchaseQuantity);
      }
      if (parsedData.auctionEndTime) {
        form.setValue("auctionEndTime", parsedData.auctionEndTime);
      }
      if (parsedData.auctionReservePrice) {
        form.setValue("auctionReservePrice", parsedData.auctionReservePrice);
      }
      if (parsedData.auctionMinIncrement) {
        form.setValue("auctionMinIncrement", parsedData.auctionMinIncrement);
      }
      if (parsedData.dynamicPricingRules) {
        form.setValue("dynamicPricingRules", parsedData.dynamicPricingRules);
      }
      if (parsedData.tieredPricingRules) {
        form.setValue("tieredPricingRules", parsedData.tieredPricingRules);
      }
      if (parsedData.allowPartialFills !== undefined) {
        form.setValue("allowPartialFills", parsedData.allowPartialFills);
      }
      if (parsedData.allowCounterOffers !== undefined) {
        form.setValue("allowCounterOffers", parsedData.allowCounterOffers);
      }
    }
  }, [form]);

  const onSubmit = (values: PricingFormValues) => {
    // Validate based on pricing strategy
    if (values.pricingStrategy === "FIXED" && !values.price) {
      toast({
        title: "Validation Error",
        description: "Price is required for fixed pricing",
        variant: "destructive",
      });
      return;
    }

    if (values.pricingStrategy === "AUCTION" && (!values.auctionEndTime || !values.auctionReservePrice)) {
      toast({
        title: "Validation Error",
        description: "End time and reserve price are required for auctions",
        variant: "destructive",
      });
      return;
    }

    // Store the form data in session storage
    const existingData = sessionStorage.getItem("createListingData");
    const parsedData = existingData ? JSON.parse(existingData) : {};
    
    const updatedData = {
      ...parsedData,
      ...values,
    };
    
    sessionStorage.setItem("createListingData", JSON.stringify(updatedData));
    
    // Navigate to the next step
    router.push("/marketplace/create-listing/confirm");
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="Pricing Strategy"
              description="Set the pricing details for your carbon credit listing"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Create Listing", href: "/marketplace/create-listing" },
                { label: "Details", href: "/marketplace/create-listing/details" },
                { label: "Pricing", href: "/marketplace/create-listing/pricing", isCurrent: true }
              ]}
            />
          </div>
          
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Select Credit</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <Check className="h-4 w-4" />
                </div>
                <span className="font-medium">Details</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  3
                </div>
                <span className="font-medium">Pricing</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground">
                  4
                </div>
                <span className="text-muted-foreground">Confirm</span>
              </div>
            </div>
          </div>
          
          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Pricing Strategy</AnimatedCardTitle>
              <AnimatedCardDescription>
                Choose how you want to price your carbon credits
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="pricingStrategy"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Pricing Strategy</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="FIXED" id="fixed" />
                              <Label htmlFor="fixed">Fixed Price</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="AUCTION" id="auction" />
                              <Label htmlFor="auction">Auction</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="DYNAMIC" id="dynamic" />
                              <Label htmlFor="dynamic">Dynamic Pricing</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="TIERED" id="tiered" />
                              <Label htmlFor="tiered">Tiered Pricing</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormDescription>
                          Select the pricing model that best suits your carbon credits
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {form.watch("pricingStrategy") === "FIXED" && (
                    <div className="space-y-4">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Fixed Price</AlertTitle>
                        <AlertDescription>
                          Set a single price per ton for your carbon credits
                        </AlertDescription>
                      </Alert>
                      
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price per Ton (USD)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={0.01}
                                step={0.01}
                                placeholder="10.00"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Set the price per ton of carbon credits
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                  
                  {form.watch("pricingStrategy") === "AUCTION" && (
                    <div className="space-y-4">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Auction</AlertTitle>
                        <AlertDescription>
                          Allow buyers to bid on your carbon credits
                        </AlertDescription>
                      </Alert>
                      
                      <FormField
                        control={form.control}
                        name="auctionEndTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Auction End Date</FormLabel>
                            <FormControl>
                              <Input type="datetime-local" {...field} />
                            </FormControl>
                            <FormDescription>
                              When the auction will end
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="auctionReservePrice"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reserve Price (USD)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={0.01}
                                step={0.01}
                                placeholder="5.00"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Minimum price you're willing to accept
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="auctionMinIncrement"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minimum Bid Increment (USD)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={0.01}
                                step={0.01}
                                placeholder="0.50"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Minimum amount by which bids must increase
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                  
                  {form.watch("pricingStrategy") === "DYNAMIC" && (
                    <div className="space-y-4">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Dynamic Pricing</AlertTitle>
                        <AlertDescription>
                          Set rules for prices that change based on market conditions or purchase volume
                        </AlertDescription>
                      </Alert>
                      
                      <FormField
                        control={form.control}
                        name="dynamicPricingRules"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dynamic Pricing Rules (JSON)</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='{"basePrice": 10, "volumeDiscounts": [{"quantity": 100, "discount": 0.05}, {"quantity": 500, "discount": 0.1}]}'
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter dynamic pricing rules in JSON format
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                  
                  {form.watch("pricingStrategy") === "TIERED" && (
                    <div className="space-y-4">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertTitle>Tiered Pricing</AlertTitle>
                        <AlertDescription>
                          Set different prices for different quantity tiers
                        </AlertDescription>
                      </Alert>
                      
                      <FormField
                        control={form.control}
                        name="tieredPricingRules"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tiered Pricing Rules (JSON)</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='{"tiers": [{"minQuantity": 1, "maxQuantity": 99, "price": 12}, {"minQuantity": 100, "maxQuantity": 499, "price": 10}, {"minQuantity": 500, "price": 8}]}'
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter tiered pricing rules in JSON format
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="minPurchaseQuantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Purchase Quantity (tons)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            step={1}
                            placeholder="1"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Set the minimum quantity that buyers must purchase
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="flex justify-between pt-4">
                    <AnimatedButton
                      type="button"
                      variant="outline"
                      onClick={() => router.push("/marketplace/create-listing/details")}
                      animationVariant="buttonTap"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back
                    </AnimatedButton>
                    <AnimatedButton
                      type="submit"
                      animationVariant="buttonTap"
                    >
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </AnimatedButton>
                  </div>
                </form>
              </Form>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
