"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import {
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardHeader,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  ArrowLeft,
  ArrowRight,
  Loader2,
  AlertTriangle,
  <PERSON>,
  Tag,
  Plus,
  AlertCircle
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ValidatedForm } from "@/components/forms/validated-form";
import { marketplaceListingSchema } from "@/lib/validation/financial-schemas";

interface CreateListingClientProps {
  userName: string;
}

interface CarbonCredit {
  id: string;
  name: string;
  description: string | null;
  quantity: number;
  vintage: number;
  standard: string;
  methodology: string;
  location: string | null;
  status: string;
  verificationStatus: string;
}

// Use the marketplaceListingSchema from our validation library
type CreateListingFormValues = {
  title: string;
  description: string;
  carbonCreditId: string;
  quantity: number;
  minPurchaseQuantity: number;
  pricingStrategy: "FIXED" | "AUCTION" | "DYNAMIC" | "TIERED";
  price?: number;
  visibility: "PUBLIC" | "PRIVATE";
  featured: boolean;
  tags?: string[];
};

export default function CreateListingClient({ userName }: CreateListingClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [availableCarbonCredits, setAvailableCarbonCredits] = useState<CarbonCredit[]>([]);
  const [selectedCredit, setSelectedCredit] = useState<CarbonCredit | null>(null);
  const [tagInput, setTagInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);

  useEffect(() => {
    async function fetchAvailableCarbonCredits() {
      try {
        setIsLoading(true);

        const response = await fetch("/api/carbon-credits?status=VERIFIED&verificationStatus=VERIFIED");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch carbon credits");
        }

        const data = await response.json();
        setAvailableCarbonCredits(data.carbonCredits || []);
      } catch (error) {
        console.error("Error fetching carbon credits:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }

    fetchAvailableCarbonCredits();
  }, []);

  // Update selected credit when carbon credit ID changes
  const updateSelectedCredit = (creditId: string, formValues: any, setValue: (name: string, value: any) => void) => {
    if (creditId) {
      const credit = availableCarbonCredits.find(c => c.id === creditId);
      setSelectedCredit(credit || null);

      if (credit) {
        setValue("quantity", credit.quantity);
        setValue("title", credit.name);
        if (credit.description) {
          setValue("description", credit.description);
        }
      }
    } else {
      setSelectedCredit(null);
    }
  };

  const handleNext = (formData: any) => {
    if (step === 1) {
      // Check if required fields for step 1 are filled
      if (formData.carbonCreditId && formData.quantity && formData.minPurchaseQuantity) {
        setStep(2);
      }
    } else if (step === 2) {
      // Check if required fields for step 2 are filled
      if (formData.title &&
          (formData.pricingStrategy !== "FIXED" || (formData.pricingStrategy === "FIXED" && formData.price))) {
        setStep(3);
      }
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const addTag = (formData: any, setValue: (name: string, value: any) => void) => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tag: string, formData: any, setValue: (name: string, value: any) => void) => {
    const newTags = tags.filter(t => t !== tag);
    setTags(newTags);
    setValue("tags", newTags);
  };

  const onSubmit = async (values: CreateListingFormValues) => {
    if (!session?.user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a listing",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch("/api/marketplace/listings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create listing");
      }

      toast({
        title: "Listing created",
        description: "Your carbon credit listing has been created successfully",
      });

      router.push(`/marketplace/${data.listing.id}`);
    } catch (error) {
      console.error("Error creating listing:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to create listing");
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading available carbon credits...</p>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Carbon Credits</h3>
              <p className="text-muted-foreground mb-6">{error}</p>
              <AnimatedButton onClick={() => router.push("/marketplace")} animationVariant="buttonTap">
                Return to Marketplace
              </AnimatedButton>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (availableCarbonCredits.length === 0) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6">
              <PageHeaderWithBreadcrumb
                title="Create Listing"
                description="List your carbon credits on the marketplace"
                breadcrumbItems={[
                  { label: "Marketplace", href: "/marketplace" },
                  { label: "Create Listing", href: "/marketplace/create-listing", isCurrent: true }
                ]}
              />
            </div>

            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Available Carbon Credits</h3>
                <p className="text-muted-foreground mb-6">
                  You don't have any verified carbon credits available for listing.
                </p>
                <div className="flex space-x-4">
                  <AnimatedButton
                    variant="outline"
                    onClick={() => router.push("/marketplace")}
                    animationVariant="buttonTap"
                  >
                    Return to Marketplace
                  </AnimatedButton>
                  <AnimatedButton
                    onClick={() => router.push("/dashboard/carbon-credits")}
                    animationVariant="buttonTap"
                  >
                    Manage Carbon Credits
                  </AnimatedButton>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6">
            <PageHeaderWithBreadcrumb
              title="Create Listing"
              description="List your carbon credits on the marketplace"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Create Listing", href: "/marketplace/create-listing", isCurrent: true }
              ]}
            />
          </div>

          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`flex h-8 w-8 items-center justify-center rounded-full ${step >= 1 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                  {step > 1 ? <Check className="h-4 w-4" /> : "1"}
                </div>
                <span className={step >= 1 ? 'font-medium' : 'text-muted-foreground'}>Select Credit</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className={`flex h-8 w-8 items-center justify-center rounded-full ${step >= 2 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                  {step > 2 ? <Check className="h-4 w-4" /> : "2"}
                </div>
                <span className={step >= 2 ? 'font-medium' : 'text-muted-foreground'}>Pricing</span>
              </div>
              <Separator className="flex-1 mx-4" />
              <div className="flex items-center space-x-2">
                <div className={`flex h-8 w-8 items-center justify-center rounded-full ${step >= 3 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                  {step > 3 ? <Check className="h-4 w-4" /> : "3"}
                </div>
                <span className={step >= 3 ? 'font-medium' : 'text-muted-foreground'}>Confirm</span>
              </div>
            </div>
          </div>

          <ValidatedForm
            schema={marketplaceListingSchema}
            defaultValues={{
              title: "",
              description: "",
              carbonCreditId: "",
              quantity: 0,
              minPurchaseQuantity: 1,
              pricingStrategy: "FIXED",
              price: undefined,
              visibility: "PUBLIC",
              featured: false,
              tags: [],
            }}
            onSubmit={onSubmit}
            className="space-y-6"
            showErrorSummary={false}
          >
            {({ control, formState, isSubmitting, watch, setValue, getValues }) => (
              <>
              {step === 1 && (
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Select Carbon Credit</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Choose a carbon credit to list on the marketplace
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="carbonCreditId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Carbon Credit</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              updateSelectedCredit(value, getValues(), setValue);
                            }}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a carbon credit" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableCarbonCredits.map((credit) => (
                                <SelectItem key={credit.id} value={credit.id}>
                                  {credit.name} ({credit.vintage}, {credit.standard})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Select a verified carbon credit to list on the marketplace
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {selectedCredit && (
                      <div className="rounded-md bg-muted p-4 space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Standard</p>
                            <p className="text-sm">{selectedCredit.standard}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Methodology</p>
                            <p className="text-sm">{selectedCredit.methodology}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Vintage</p>
                            <p className="text-sm">{selectedCredit.vintage}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Location</p>
                            <p className="text-sm">{selectedCredit.location || "N/A"}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Available Quantity</p>
                            <p className="text-sm">{selectedCredit.quantity.toLocaleString()} tons</p>
                          </div>
                        </div>
                      </div>
                    )}

                    <FormField
                      control={form.control}
                      name="quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantity to List (tons)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={selectedCredit?.quantity || 0}
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Enter the quantity of carbon credits to list (maximum: {selectedCredit?.quantity.toLocaleString() || 0} tons)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minPurchaseQuantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Purchase Quantity (tons)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={form.watch("quantity") || 0}
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Set the minimum quantity that buyers must purchase
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-between pt-4">
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={() => router.push("/marketplace")}
                        animationVariant="buttonTap"
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Cancel
                      </AnimatedButton>
                      <AnimatedButton
                        type="button"
                        onClick={() => handleNext(getValues())}
                        disabled={!watch("carbonCreditId") || !watch("quantity")}
                        animationVariant="buttonTap"
                      >
                        Next
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}

              {step === 2 && (
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Pricing Strategy</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Set the pricing details for your carbon credit listing
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Listing Title</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormDescription>
                            Enter a title for your listing
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              rows={4}
                              placeholder="Describe your carbon credits and their environmental impact..."
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Provide details about your carbon credits to attract buyers
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pricingStrategy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pricing Strategy</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select pricing strategy" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="FIXED">Fixed Price</SelectItem>
                              <SelectItem value="AUCTION">Auction</SelectItem>
                              <SelectItem value="DYNAMIC">Dynamic Pricing</SelectItem>
                              <SelectItem value="TIERED">Tiered Pricing</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose how you want to price your carbon credits
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch("pricingStrategy") === "FIXED" && (
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Price per Ton (USD)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={0.01}
                                step={0.01}
                                placeholder="10.00"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Set the price per ton of carbon credits
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Additional fields for other pricing strategies would go here */}

                    <FormField
                      control={form.control}
                      name="visibility"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Visibility</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select visibility" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="PUBLIC">Public (Visible to all users)</SelectItem>
                              <SelectItem value="PRIVATE">Private (Visible to selected users only)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Control who can see your listing
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-between pt-4">
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={handleBack}
                        animationVariant="buttonTap"
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back
                      </AnimatedButton>
                      <AnimatedButton
                        type="button"
                        onClick={() => handleNext(getValues())}
                        disabled={!watch("title") || (watch("pricingStrategy") === "FIXED" && !watch("price"))}
                        animationVariant="buttonTap"
                      >
                        Next
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}

              {step === 3 && (
                <AnimatedCard>
                  <AnimatedCardHeader>
                    <AnimatedCardTitle>Confirm Listing</AnimatedCardTitle>
                    <AnimatedCardDescription>
                      Review and confirm your carbon credit listing
                    </AnimatedCardDescription>
                  </AnimatedCardHeader>
                  <AnimatedCardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium">Carbon Credit</h3>
                          <p className="text-sm">
                            {selectedCredit?.name} ({selectedCredit?.vintage}, {selectedCredit?.standard})
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium">Quantity</h3>
                          <p className="text-sm">{form.watch("quantity")?.toLocaleString()} tons</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium">Minimum Purchase</h3>
                          <p className="text-sm">{form.watch("minPurchaseQuantity")?.toLocaleString()} tons</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium">Pricing Strategy</h3>
                          <p className="text-sm">{form.watch("pricingStrategy")}</p>
                        </div>
                        {form.watch("pricingStrategy") === "FIXED" && (
                          <div>
                            <h3 className="text-sm font-medium">Price per Ton</h3>
                            <p className="text-sm">${form.watch("price")?.toFixed(2)}</p>
                          </div>
                        )}
                        <div>
                          <h3 className="text-sm font-medium">Visibility</h3>
                          <p className="text-sm">{form.watch("visibility")}</p>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="text-sm font-medium mb-2">Listing Title</h3>
                        <p className="text-sm">{form.watch("title")}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Description</h3>
                        <p className="text-sm">{form.watch("description") || "No description provided."}</p>
                      </div>

                      <div className="space-y-2">
                        <h3 className="text-sm font-medium">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                              {tag}
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 hover:bg-transparent"
                                onClick={() => removeTag(tag, getValues(), setValue)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </Badge>
                          ))}
                          <div className="flex items-center gap-2">
                            <Input
                              value={tagInput}
                              onChange={(e) => setTagInput(e.target.value)}
                              placeholder="Add tag..."
                              className="h-8 w-32"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  addTag(getValues(), setValue);
                                }
                              }}
                            />
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              className="h-8"
                              onClick={() => addTag(getValues(), setValue)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <FormField
                        control={form.control}
                        name="featured"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Feature this listing</FormLabel>
                              <FormDescription>
                                Featured listings appear at the top of the marketplace and receive more visibility
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-between pt-4">
                      <AnimatedButton
                        type="button"
                        variant="outline"
                        onClick={handleBack}
                        animationVariant="buttonTap"
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back
                      </AnimatedButton>
                      <AnimatedButton
                        type="submit"
                        disabled={isSubmitting}
                        animationVariant="buttonTap"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating Listing...
                          </>
                        ) : (
                          "Create Listing"
                        )}
                      </AnimatedButton>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              )}
              </>
            )}
          </ValidatedForm>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
