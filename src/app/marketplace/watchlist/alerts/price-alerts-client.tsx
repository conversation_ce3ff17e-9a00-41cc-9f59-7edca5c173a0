"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { 
  ArrowLeft, 
  Bell, 
  AlertTriangle, 
  Plus, 
  Trash2, 
  ToggleLeft, 
  ToggleRight,
  Filter,
  Search
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Spinner } from "@/components/ui/spinner";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  <PERSON><PERSON><PERSON>on,
  AnimatedCard,
  AnimatedCardContent,
  AnimatedCardDescription,
  AnimatedCardFooter,
  AnimatedCardHeader,
  AnimatedCardTitle,
  PageTransition,
  StaggeredList
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { format } from "date-fns";

interface PriceAlertsClientProps {
  userName: string;
}

interface PriceAlert {
  id: string;
  watchlistItemId: string;
  condition: "ABOVE" | "BELOW";
  price: number;
  active: boolean;
  triggered: boolean;
  createdAt: string;
  updatedAt: string;
  watchlistItem: {
    carbonCredit: {
      id: string;
      name: string;
      price: number;
      standard: string;
      vintage: number;
      organization: {
        name: string;
      };
    };
  };
}

export default function PriceAlertsClient({ userName }: PriceAlertsClientProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [priceAlerts, setPriceAlerts] = useState<PriceAlert[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<PriceAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [conditionFilter, setConditionFilter] = useState("all");
  const [selectedAlert, setSelectedAlert] = useState<PriceAlert | null>(null);
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isTogglingActive, setIsTogglingActive] = useState(false);

  useEffect(() => {
    async function fetchPriceAlerts() {
      try {
        setIsLoading(true);
        const response = await fetch("/api/watchlist/alerts");
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch price alerts");
        }
        
        const data = await response.json();
        setPriceAlerts(data.priceAlerts);
        setFilteredAlerts(data.priceAlerts);
      } catch (error) {
        console.error("Error fetching price alerts:", error);
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchPriceAlerts();
  }, []);

  useEffect(() => {
    // Apply filters
    let filtered = [...priceAlerts];
    
    // Apply status filter
    if (statusFilter === "active") {
      filtered = filtered.filter((alert) => alert.active && !alert.triggered);
    } else if (statusFilter === "triggered") {
      filtered = filtered.filter((alert) => alert.triggered);
    } else if (statusFilter === "inactive") {
      filtered = filtered.filter((alert) => !alert.active && !alert.triggered);
    }
    
    // Apply condition filter
    if (conditionFilter === "above") {
      filtered = filtered.filter((alert) => alert.condition === "ABOVE");
    } else if (conditionFilter === "below") {
      filtered = filtered.filter((alert) => alert.condition === "BELOW");
    }
    
    // Apply search filter
    if (searchTerm.trim() !== "") {
      const lowercasedSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (alert) =>
          alert.watchlistItem.carbonCredit.name.toLowerCase().includes(lowercasedSearch) ||
          alert.watchlistItem.carbonCredit.organization.name.toLowerCase().includes(lowercasedSearch) ||
          alert.watchlistItem.carbonCredit.standard.toLowerCase().includes(lowercasedSearch)
      );
    }
    
    setFilteredAlerts(filtered);
  }, [statusFilter, conditionFilter, searchTerm, priceAlerts]);

  const handleDeleteAlert = async (alertId: string) => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/watchlist/alerts/${alertId}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete price alert");
      }
      
      // Update local state
      setPriceAlerts(priceAlerts.filter((alert) => alert.id !== alertId));
      
      toast({
        title: "Alert Deleted",
        description: "Price alert has been deleted successfully",
      });
      
      setIsConfirmingDelete(false);
      setSelectedAlert(null);
    } catch (error) {
      console.error("Error deleting price alert:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete price alert",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleActive = async (alertId: string, currentActive: boolean) => {
    try {
      setIsTogglingActive(true);
      const response = await fetch(`/api/watchlist/alerts/${alertId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          active: !currentActive,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update price alert");
      }
      
      // Update local state
      setPriceAlerts(
        priceAlerts.map((alert) =>
          alert.id === alertId ? { ...alert, active: !currentActive } : alert
        )
      );
      
      toast({
        title: currentActive ? "Alert Deactivated" : "Alert Activated",
        description: `Price alert has been ${currentActive ? "deactivated" : "activated"} successfully`,
      });
    } catch (error) {
      console.error("Error updating price alert:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update price alert",
        variant: "destructive",
      });
    } finally {
      setIsTogglingActive(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy HH:mm");
  };

  if (isLoading) {
    return (
      <ProtectedPage loadingComponent={<Spinner size="lg" />}>
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertTriangle className="h-10 w-10 text-destructive" />
              <p className="text-center text-lg font-medium">{error}</p>
              <Button variant="outline" onClick={() => router.push("/marketplace/watchlist")}>
                Back to Watchlist
              </Button>
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Price Alerts"
              description="Manage your carbon credit price alerts"
              breadcrumbItems={[
                { label: "Marketplace", href: "/marketplace" },
                { label: "Watchlist", href: "/marketplace/watchlist" },
                { label: "Price Alerts", href: "/marketplace/watchlist/alerts", isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <AnimatedButton
                onClick={() => router.push("/marketplace/watchlist/alerts/create")}
                animationVariant="buttonTap"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Alert
              </AnimatedButton>
              <AnimatedButton
                variant="outline"
                onClick={() => router.push("/marketplace/watchlist")}
                animationVariant="buttonTap"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Watchlist
              </AnimatedButton>
            </div>
          </div>

          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Status:</span>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="triggered">Triggered</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Condition:</span>
                <Select value={conditionFilter} onValueChange={setConditionFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="above">Above</SelectItem>
                    <SelectItem value="below">Below</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search alerts..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {filteredAlerts.length === 0 ? (
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="mb-4 h-12 w-12 text-muted-foreground" />
                <h3 className="text-xl font-medium">No price alerts found</h3>
                <p className="mb-6 text-center text-muted-foreground">
                  {priceAlerts.length === 0
                    ? "You haven't created any price alerts yet."
                    : "No alerts match your current filters."}
                </p>
                <AnimatedButton
                  onClick={() => router.push("/marketplace/watchlist/alerts/create")}
                  animationVariant="buttonTap"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create Alert
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          ) : (
            <StaggeredList className="space-y-4">
              {filteredAlerts.map((alert) => (
                <AnimatedCard key={alert.id}>
                  <AnimatedCardContent className="p-6">
                    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                      <div className="flex flex-col gap-1">
                        <h3 className="font-medium">
                          {alert.watchlistItem.carbonCredit.name}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {alert.watchlistItem.carbonCredit.organization.name} • {alert.watchlistItem.carbonCredit.standard} • {alert.watchlistItem.carbonCredit.vintage}
                        </p>
                      </div>
                      <div className="flex flex-col gap-1 md:items-end">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Current Price:</span>
                          <span>${alert.watchlistItem.carbonCredit.price.toFixed(2)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Alert Price:</span>
                          <Badge variant="outline" className={
                            alert.condition === "ABOVE" ? "border-orange-500 text-orange-500" : "border-blue-500 text-blue-500"
                          }>
                            {alert.condition === "ABOVE" ? "Above" : "Below"} ${alert.price.toFixed(2)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">Status:</span>
                          {alert.triggered ? (
                            <Badge className="bg-green-500">Triggered</Badge>
                          ) : alert.active ? (
                            <Badge variant="outline" className="border-green-500 text-green-500">Active</Badge>
                          ) : (
                            <Badge variant="outline" className="border-gray-500 text-gray-500">Inactive</Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Created: {formatDate(alert.createdAt)}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!alert.triggered && (
                          <Switch
                            checked={alert.active}
                            onCheckedChange={() => handleToggleActive(alert.id, alert.active)}
                            disabled={isTogglingActive}
                          />
                        )}
                        <Dialog
                          open={isConfirmingDelete && selectedAlert?.id === alert.id}
                          onOpenChange={(open) => {
                            setIsConfirmingDelete(open);
                            if (!open) setSelectedAlert(null);
                          }}
                        >
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setSelectedAlert(alert)}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Delete Price Alert</DialogTitle>
                              <DialogDescription>
                                Are you sure you want to delete this price alert? This action cannot be undone.
                              </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setIsConfirmingDelete(false);
                                  setSelectedAlert(null);
                                }}
                              >
                                Cancel
                              </Button>
                              <Button
                                variant="destructive"
                                onClick={() => handleDeleteAlert(alert.id)}
                                disabled={isDeleting}
                              >
                                {isDeleting ? (
                                  <>
                                    <Spinner className="mr-2 h-4 w-4" />
                                    Deleting...
                                  </>
                                ) : (
                                  <>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </>
                                )}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </AnimatedCardContent>
                </AnimatedCard>
              ))}
            </StaggeredList>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
