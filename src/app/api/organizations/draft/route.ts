import { NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";

// Schema for organization draft
const organizationDraftSchema = z.object({
  // Basic Information
  name: z.string().min(2, "Organization name must be at least 2 characters").optional(),
  industry: z.string().optional(),
  size: z.enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"]).optional(),
  description: z.string().optional(),
  
  // Contact Information
  website: z.string().url("Please enter a valid URL").or(z.string().length(0)).optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email("Please enter a valid email address").optional(),
  
  // Legal Information
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  
  // Address Information
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  
  // Metadata
  currentStep: z.number().int().min(1).max(4).optional(),
  draftId: z.string().optional(), // For updating existing drafts
});

/**
 * POST /api/organizations/draft
 * Save organization draft
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to save a draft" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = organizationDraftSchema.parse(body);
    
    // Check if user already has a draft
    const existingDraft = await db.organizationDraft.findFirst({
      where: {
        userId: session.user.id,
      },
    });

    let draft;
    
    if (existingDraft) {
      // Update existing draft
      draft = await db.organizationDraft.update({
        where: {
          id: existingDraft.id,
        },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new draft
      draft = await db.organizationDraft.create({
        data: {
          ...validatedData,
          userId: session.user.id,
        },
      });
    }

    logger.info(`Organization draft saved for user ${session.user.id}`);

    return NextResponse.json({
      message: "Draft saved successfully",
      draft,
    });
  } catch (error) {
    logger.error("Error saving organization draft:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while saving the draft" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/organizations/draft
 * Get organization draft for current user
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to retrieve a draft" },
        { status: 401 }
      );
    }

    // Get draft for current user
    const draft = await db.organizationDraft.findFirst({
      where: {
        userId: session.user.id,
      },
    });

    if (!draft) {
      return NextResponse.json(
        { message: "No draft found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ draft });
  } catch (error) {
    logger.error("Error retrieving organization draft:", error);

    return NextResponse.json(
      { error: "An error occurred while retrieving the draft" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organizations/draft
 * Delete organization draft for current user
 */
export async function DELETE() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete a draft" },
        { status: 401 }
      );
    }

    // Delete draft for current user
    await db.organizationDraft.deleteMany({
      where: {
        userId: session.user.id,
      },
    });

    logger.info(`Organization draft deleted for user ${session.user.id}`);

    return NextResponse.json({
      message: "Draft deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting organization draft:", error);

    return NextResponse.json(
      { error: "An error occurred while deleting the draft" },
      { status: 500 }
    );
  }
}
