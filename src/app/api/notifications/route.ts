import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { notificationService, NotificationChannel } from "@/lib/notifications";
import { withTenantIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for creating a notification
const createNotificationSchema = z.object({
  title: z.string().min(1, "Title is required"),
  message: z.string().min(1, "Message is required"),
  type: z.enum(["SYSTEM", "CREDIT", "TRANSACTION", "ORDER"]),
  userId: z.string().min(1, "User ID is required"),
  channels: z.array(z.enum(["in_app", "email", "sms", "webhook"])).optional(),
  metadata: z.record(z.any()).optional(),
});

// Original GET handler
async function getHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access notifications" },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");
    const unreadOnly = searchParams.get("unreadOnly") === "true";

    // Get tenant context - this is handled by the withTenantIsolation wrapper
    await getTenantContext(session.user.id);

    // Get user's notifications
    const result = await notificationService.getNotifications(
      session.user.id,
      limit,
      offset,
      unreadOnly
    );

    return NextResponse.json(result);
  } catch (error) {
    logger.error("Error fetching notifications:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching notifications" },
      { status: 500 }
    );
  }
}

// Original POST handler
async function postHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create notifications" },
        { status: 401 }
      );
    }

    // Only admins can create notifications for other users
    if (session.user.role !== "ADMIN" && session.user.role !== "ORGANIZATION_ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to create notifications" },
        { status: 403 }
      );
    }

    // Parse and validate the request body
    const body = await req.json();
    const validatedData = createNotificationSchema.parse(body);

    // Convert channels to NotificationChannel enum
    const channels = validatedData.channels?.map(
      (channel) => NotificationChannel[channel.toUpperCase() as keyof typeof NotificationChannel]
    ) || [NotificationChannel.IN_APP];

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Verify that the target user belongs to the same organization if not an admin
    if (!tenantContext.isAdmin) {
      const targetUser = await db.user.findUnique({
        where: { id: validatedData.userId },
        select: { organizationId: true },
      });

      if (!targetUser || targetUser.organizationId !== tenantContext.organizationId) {
        logger.warn(`User ${session.user.id} attempted to create notification for user ${validatedData.userId} from another organization`);
        return NextResponse.json(
          { error: "You do not have permission to create notifications for users outside your organization" },
          { status: 403 }
        );
      }
    }

    // Create the notification
    const notification = await notificationService.createNotification(
      validatedData.userId,
      validatedData.title,
      validatedData.message,
      validatedData.type,
      channels,
      validatedData.metadata
    );

    logger.info(`User ${session.user.id} created notification for user ${validatedData.userId}`);

    return NextResponse.json({
      notification,
      message: "Notification created successfully",
    });
  } catch (error) {
    logger.error("Error creating notification:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the notification" },
      { status: 500 }
    );
  }
}

// Original PATCH handler
async function patchHandler(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update notifications" },
        { status: 401 }
      );
    }

    const searchParams = req.nextUrl.searchParams;
    const notificationId = searchParams.get("id");
    const markAllRead = searchParams.get("markAllRead");

    if (markAllRead === "true") {
      // Mark all notifications as read
      const count = await notificationService.markAllAsRead(session.user.id);

      return NextResponse.json({
        message: `${count} notifications marked as read`,
        count,
      });
    } else if (notificationId) {
      // Mark specific notification as read
      const updatedNotification = await notificationService.markAsRead(notificationId, session.user.id);

      return NextResponse.json({
        message: "Notification marked as read",
        notification: updatedNotification,
      });
    } else {
      return NextResponse.json(
        { error: "Missing notification ID or markAllRead parameter" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Error updating notifications:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating notifications" },
      { status: 500 }
    );
  }
}

// Export the handlers with tenant isolation middleware
export const GET = withTenantIsolation(getHandler);
export const POST = withTenantIsolation(postHandler);
