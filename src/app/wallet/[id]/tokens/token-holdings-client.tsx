"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { ProtectedPage } from "@/components/auth/protected-page";
import { 
  Animated<PERSON><PERSON>, 
  AnimatedCardContent, 
  AnimatedCardHeader, 
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  Wallet, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Search, 
  ExternalLink,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";

interface TokenHoldingsClientProps {
  userName: string;
  walletId: string;
}

interface Token {
  id: string;
  name: string;
  symbol: string;
  tokenAddress: string;
  balance: number;
  decimals: number;
  value: number;
  price: number;
  network: string;
  chainId: number;
  isTestnet: boolean;
}

interface WalletData {
  id: string;
  name: string;
  address: string;
  network: string;
  chainId: number;
  isTestnet: boolean;
}

export default function TokenHoldingsClient({ userName, walletId }: TokenHoldingsClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletData | null>(null);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [filteredTokens, setFilteredTokens] = useState<Token[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    async function fetchWalletAndTokens() {
      try {
        setIsLoading(true);
        
        // Fetch wallet details
        const walletResponse = await fetch(`/api/wallet/${walletId}`);
        
        if (!walletResponse.ok) {
          const errorData = await walletResponse.json();
          throw new Error(errorData.error || "Failed to fetch wallet details");
        }
        
        const walletData = await walletResponse.json();
        setWallet(walletData.wallet);
        
        // Fetch tokens
        const tokensResponse = await fetch(`/api/wallet/${walletId}/tokens`);
        
        if (!tokensResponse.ok) {
          const errorData = await tokensResponse.json();
          throw new Error(errorData.error || "Failed to fetch tokens");
        }
        
        const tokensData = await tokensResponse.json();
        setTokens(tokensData.tokens || []);
        setFilteredTokens(tokensData.tokens || []);
      } catch (error) {
        console.error("Error fetching wallet and tokens:", error);
        setError(error instanceof Error ? error.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchWalletAndTokens();
  }, [walletId]);

  // Filter tokens based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTokens(tokens);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = tokens.filter(
      token => 
        token.name.toLowerCase().includes(query) || 
        token.symbol.toLowerCase().includes(query)
    );
    
    setFilteredTokens(filtered);
  }, [searchQuery, tokens]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="space-y-1">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  if (error || !wallet) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
              <PageHeaderWithBreadcrumb
                title="Token Holdings"
                description="View tokens in your wallet"
                breadcrumbItems={[
                  { label: "Wallet", href: "/wallet" },
                  { label: "Details", href: `/wallet/${walletId}` },
                  { label: "Tokens", href: `/wallet/${walletId}/tokens`, isCurrent: true }
                ]}
              />
            </div>
            <AnimatedCard>
              <AnimatedCardContent className="flex flex-col items-center justify-center py-10">
                <AlertTriangle className="h-10 w-10 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">Error Loading Tokens</h3>
                <p className="text-muted-foreground mb-6">{error || "Wallet not found"}</p>
                <AnimatedButton onClick={() => router.push("/wallet")} animationVariant="buttonTap">
                  Return to Wallet Dashboard
                </AnimatedButton>
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Token Holdings"
              description="View tokens in your wallet"
              breadcrumbItems={[
                { label: "Wallet", href: "/wallet" },
                { label: "Details", href: `/wallet/${walletId}` },
                { label: "Tokens", href: `/wallet/${walletId}/tokens`, isCurrent: true }
              ]}
            />
            <div className="flex space-x-2">
              <AnimatedButton
                variant="outline"
                onClick={() => router.push(`/wallet/${walletId}/send`)}
                animationVariant="buttonTap"
              >
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Send
              </AnimatedButton>
              <AnimatedButton
                onClick={() => router.push(`/wallet/${walletId}/receive`)}
                animationVariant="buttonTap"
              >
                <ArrowDownLeft className="mr-2 h-4 w-4" />
                Receive
              </AnimatedButton>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search tokens..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <AnimatedCard>
              <AnimatedCardHeader>
                <AnimatedCardTitle>Token Holdings</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Tokens held in this wallet
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                {tokens.length > 0 ? (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[250px]">Token</TableHead>
                          <TableHead>Balance</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead className="text-right">Value</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTokens.map((token) => (
                          <TableRow key={token.id}>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                  {token.symbol.charAt(0)}
                                </div>
                                <div>
                                  <div className="font-medium">{token.name}</div>
                                  <div className="text-sm text-muted-foreground">{token.symbol}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {token.balance.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 6,
                              })}
                            </TableCell>
                            <TableCell>
                              {formatCurrency(token.price)}
                            </TableCell>
                            <TableCell className="text-right">
                              {formatCurrency(token.value)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No tokens found</p>
                    <AnimatedButton
                      variant="link"
                      onClick={() => router.push(`/wallet/${walletId}/receive`)}
                      animationVariant="buttonTap"
                    >
                      Receive tokens
                    </AnimatedButton>
                  </div>
                )}
              </AnimatedCardContent>
            </AnimatedCard>
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
