"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  PageTransition,
  AnimatedCard,
  Animated<PERSON>ardHeader,
  Animated<PERSON>ardContent,
  AnimatedCardTitle,
  AnimatedCardDescription,
  AnimatedButton,
  StaggeredList
} from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header-with-breadcrumb";
import { DollarSign, CreditCard, Users, Activity, Leaf, ShoppingCart, Wallet, Settings, Plus, ArrowRight } from "lucide-react";
import { WalletWidget } from "@/components/dashboard/wallet-widget";
import { CarbonCreditsWidget } from "@/components/dashboard/carbon-credits-widget";
import { ProfileCompletionBanner } from "@/components/dashboard/profile-completion-banner";
import { ProfileCompletionIndicator } from "@/components/profile/profile-completion-indicator";
import { Card, CardContent, CardDescription, CardFooter, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface DashboardClientProps {
  userName: string;
}

export default function DashboardClient({ userName }: DashboardClientProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [profileCompletion, setProfileCompletion] = useState(0);

  useEffect(() => {
    if (session?.user?.organizationId) {
      setOrganizationId(session.user.organizationId);
    }
  }, [session]);

  return (
    <PageTransition>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <PageHeaderWithBreadcrumb
            title={`Welcome, ${userName}`}
            description="Your carbon trading dashboard"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard", isCurrent: true }
            ]}
          />
        </div>

        {organizationId && profileCompletion < 100 && (
          <div className="mb-6">
            <ProfileCompletionBanner organizationId={organizationId} />
          </div>
        )}

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Total Carbon Credits
              </AnimatedCardTitle>
              <Leaf className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">5,000</div>
              <p className="text-xs text-muted-foreground">
                +20% from last month
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Active Listings
              </AnimatedCardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                +2 new listings this week
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Wallet Balance
              </AnimatedCardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">$12,234</div>
              <p className="text-xs text-muted-foreground">
                +$1,234 from transactions
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
          <AnimatedCard>
            <AnimatedCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <AnimatedCardTitle className="text-sm font-medium">
                Active Subscriptions
              </AnimatedCardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="text-2xl font-bold">Enterprise</div>
              <p className="text-xs text-muted-foreground">
                Renews in 26 days
              </p>
            </AnimatedCardContent>
          </AnimatedCard>
        </div>

        <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <StaggeredList className="col-span-4">
            <AnimatedCard className="col-span-4">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Carbon Credits</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Your carbon credit portfolio
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <CarbonCreditsWidget />
              </AnimatedCardContent>
              <CardFooter>
                <AnimatedButton 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push("/dashboard/carbon-credits")}
                >
                  View All Carbon Credits
                  <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </CardFooter>
            </AnimatedCard>
          </StaggeredList>

          <StaggeredList className="col-span-3">
            <AnimatedCard className="col-span-3">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Quick Actions</AnimatedCardTitle>
                <AnimatedCardDescription>
                  Common tasks and actions
                </AnimatedCardDescription>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <div className="space-y-4">
                  <Button 
                    className="w-full justify-start" 
                    onClick={() => router.push("/dashboard/carbon-credits/create")}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create Carbon Credit
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => router.push("/dashboard/marketplace")}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Browse Marketplace
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => router.push("/dashboard/wallet")}
                  >
                    <Wallet className="mr-2 h-4 w-4" />
                    Manage Wallet
                  </Button>
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => router.push("/dashboard/carbon-credits/batch")}
                  >
                    <Activity className="mr-2 h-4 w-4" />
                    Batch Operations
                  </Button>
                </div>
              </AnimatedCardContent>
            </AnimatedCard>
          </StaggeredList>
        </div>

        <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <AnimatedCard className="col-span-2">
            <AnimatedCardHeader>
              <AnimatedCardTitle>Recent Activity</AnimatedCardTitle>
              <AnimatedCardDescription>
                Your recent transactions and activities
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              <div className="space-y-8">
                <div className="flex items-center">
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                    <Leaf className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      Carbon Credit Created
                    </p>
                    <p className="text-sm text-muted-foreground">
                      You created a new carbon credit: Renewable Energy Project
                    </p>
                  </div>
                  <div className="ml-auto font-medium">Just now</div>
                </div>
                <div className="flex items-center">
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                    <ShoppingCart className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      Marketplace Listing
                    </p>
                    <p className="text-sm text-muted-foreground">
                      You listed 1,000 carbon credits for sale
                    </p>
                  </div>
                  <div className="ml-auto font-medium">2 hours ago</div>
                </div>
                <div className="flex items-center">
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
                    <Wallet className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      Wallet Transaction
                    </p>
                    <p className="text-sm text-muted-foreground">
                      You received a payment of $1,234
                    </p>
                  </div>
                  <div className="ml-auto font-medium">Yesterday</div>
                </div>
              </div>
            </AnimatedCardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Activity
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </AnimatedCard>

          <AnimatedCard>
            <AnimatedCardHeader>
              <AnimatedCardTitle>Profile Completion</AnimatedCardTitle>
              <AnimatedCardDescription>
                Complete your profile to unlock all features
              </AnimatedCardDescription>
            </AnimatedCardHeader>
            <AnimatedCardContent>
              {organizationId ? (
                <ProfileCompletionIndicator
                  organizationId={organizationId}
                  showDetails={true}
                />
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Organization information not available
                  </p>
                </div>
              )}
            </AnimatedCardContent>
            <CardFooter>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => router.push("/settings/organization")}
              >
                Complete Profile
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </AnimatedCard>
        </div>
      </div>
    </PageTransition>
  );
}
