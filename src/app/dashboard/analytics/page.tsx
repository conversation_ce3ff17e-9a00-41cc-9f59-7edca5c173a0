"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Tabs, 
  <PERSON><PERSON><PERSON>ontent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Pie<PERSON>hart 
} from "@/components/ui/charts";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart as LineChartIcon, 
  <PERSON><PERSON>hart as PieChartIcon, 
  Download, 
  Calendar, 
  RefreshCw, 
  Loader2,
  ArrowLeft,
  Leaf,
  DollarSign,
  TrendingUp,
  Activity
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { format, subDays, subMonths } from "date-fns";
import { ProtectedPage } from "@/components/auth/protected-page";
import { PageTransition } from "@/components/ui/animated";
import { PageHeaderWithBreadcrumb } from "@/components/ui/page-header";
import { AnimatedButton } from "@/components/ui/animated-button";

interface AnalyticsData {
  carbonImpact: {
    totalEmissionsReduced: number;
    totalCreditsRetired: number;
    emissionsReductionByMonth: { month: string; amount: number }[];
    creditsByStandard: { standard: string; count: number }[];
    creditsByVintage: { vintage: number; count: number }[];
  };
  financial: {
    totalTransactionVolume: number;
    totalRevenue: number;
    totalExpenses: number;
    walletBalances: { wallet: string; balance: number }[];
    carbonAssetValue: number;
    transactionsByMonth: { month: string; amount: number }[];
    revenueBySource: { source: string; amount: number }[];
  };
  trading: {
    totalOrders: number;
    activeOrders: number;
    completedOrders: number;
    ordersByType: { type: string; count: number }[];
    priceHistory: { date: string; price: number }[];
    marketTrends: { metric: string; value: number; change: number }[];
  };
}

export default function AnalyticsDashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("carbon-impact");
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Fetch analytics data
  const fetchAnalyticsData = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const response = await fetch(`/api/analytics/dashboard?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
  };

  // Fetch data when component mounts or time range changes
  useEffect(() => {
    if (session?.user) {
      fetchAnalyticsData();
    }
  }, [session, timeRange]);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Mock data for development
  const mockData: AnalyticsData = {
    carbonImpact: {
      totalEmissionsReduced: 15000,
      totalCreditsRetired: 7500,
      emissionsReductionByMonth: [
        { month: "Jan", amount: 1200 },
        { month: "Feb", amount: 1350 },
        { month: "Mar", amount: 1100 },
        { month: "Apr", amount: 1500 },
        { month: "May", amount: 1700 },
        { month: "Jun", amount: 1800 },
      ],
      creditsByStandard: [
        { standard: "Verra", count: 4500 },
        { standard: "Gold Standard", count: 2000 },
        { standard: "American Carbon Registry", count: 1000 },
      ],
      creditsByVintage: [
        { vintage: 2018, count: 1000 },
        { vintage: 2019, count: 1500 },
        { vintage: 2020, count: 2000 },
        { vintage: 2021, count: 2500 },
        { vintage: 2022, count: 3000 },
      ],
    },
    financial: {
      totalTransactionVolume: 750000,
      totalRevenue: 125000,
      totalExpenses: 45000,
      walletBalances: [
        { wallet: "Main Wallet", balance: 250000 },
        { wallet: "Ethereum", balance: 150000 },
        { wallet: "Polygon", balance: 100000 },
      ],
      carbonAssetValue: 500000,
      transactionsByMonth: [
        { month: "Jan", amount: 50000 },
        { month: "Feb", amount: 65000 },
        { month: "Mar", amount: 55000 },
        { month: "Apr", amount: 70000 },
        { month: "May", amount: 85000 },
        { month: "Jun", amount: 90000 },
      ],
      revenueBySource: [
        { source: "Credit Sales", amount: 80000 },
        { source: "Transaction Fees", amount: 30000 },
        { source: "Subscription", amount: 15000 },
      ],
    },
    trading: {
      totalOrders: 350,
      activeOrders: 75,
      completedOrders: 275,
      ordersByType: [
        { type: "Buy", count: 200 },
        { type: "Sell", count: 150 },
      ],
      priceHistory: [
        { date: "Jan", price: 12.5 },
        { date: "Feb", price: 13.2 },
        { date: "Mar", price: 12.8 },
        { date: "Apr", price: 14.1 },
        { date: "May", price: 15.3 },
        { date: "Jun", price: 16.0 },
      ],
      marketTrends: [
        { metric: "Average Price", value: 15.5, change: 2.5 },
        { metric: "Volume", value: 75000, change: 12000 },
        { metric: "Active Listings", value: 120, change: 15 },
      ],
    },
  };

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto py-6">
          <div className="mb-6 flex items-center justify-between">
            <PageHeaderWithBreadcrumb
              title="Analytics Dashboard"
              description="View and analyze your carbon trading metrics"
              breadcrumbItems={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Analytics", href: "/dashboard/analytics", isCurrent: true }
              ]}
            />
            <div className="flex items-center space-x-2">
              <Select value={timeRange} onValueChange={handleTimeRangeChange}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <AnimatedButton
                variant="outline"
                onClick={() => fetchAnalyticsData(true)}
                disabled={isRefreshing}
                animationVariant="buttonTap"
              >
                {isRefreshing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </AnimatedButton>
            </div>
          </div>

          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="carbon-impact" className="flex items-center justify-center">
                <Leaf className="mr-2 h-4 w-4" />
                Carbon Impact
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center justify-center">
                <DollarSign className="mr-2 h-4 w-4" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="trading" className="flex items-center justify-center">
                <TrendingUp className="mr-2 h-4 w-4" />
                Trading Activity
              </TabsTrigger>
            </TabsList>

            <TabsContent value="carbon-impact" className="space-y-6">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Card key={i} className="w-full h-[200px] animate-pulse bg-muted" />
                  ))}
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Emissions Reduced</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {mockData.carbonImpact.totalEmissionsReduced.toLocaleString()} tCO₂e
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Credits Retired</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {mockData.carbonImpact.totalCreditsRetired.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Impact Equivalent</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {Math.round(mockData.carbonImpact.totalEmissionsReduced / 4.6).toLocaleString()} cars
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Annual emissions from passenger vehicles
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Emissions Reduction Over Time</CardTitle>
                        <CardDescription>
                          Monthly emissions reduction in tCO₂e
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <LineChart
                          data={mockData.carbonImpact.emissionsReductionByMonth.map((item) => ({
                            name: item.month,
                            "Emissions Reduced": item.amount,
                          }))}
                          categories={["Emissions Reduced"]}
                          colors={["#10b981"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Credits by Standard</CardTitle>
                        <CardDescription>
                          Distribution of carbon credits by standard
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <PieChart
                          data={mockData.carbonImpact.creditsByStandard.map((item) => ({
                            name: item.standard,
                            value: item.count,
                          }))}
                          colors={["#3b82f6", "#10b981", "#f59e0b"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="financial" className="space-y-6">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Card key={i} className="w-full h-[200px] animate-pulse bg-muted" />
                  ))}
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          ${mockData.financial.totalTransactionVolume.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          ${mockData.financial.totalRevenue.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Carbon Asset Value</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          ${mockData.financial.carbonAssetValue.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Transactions by Month</CardTitle>
                        <CardDescription>
                          Monthly transaction volume in USD
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <BarChart
                          data={mockData.financial.transactionsByMonth.map((item) => ({
                            name: item.month,
                            "Volume": item.amount,
                          }))}
                          categories={["Volume"]}
                          colors={["#3b82f6"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Revenue by Source</CardTitle>
                        <CardDescription>
                          Distribution of revenue by source
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <PieChart
                          data={mockData.financial.revenueBySource.map((item) => ({
                            name: item.source,
                            value: item.amount,
                          }))}
                          colors={["#3b82f6", "#10b981", "#f59e0b"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="trading" className="space-y-6">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Card key={i} className="w-full h-[200px] animate-pulse bg-muted" />
                  ))}
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {mockData.trading.totalOrders.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Active Orders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {mockData.trading.activeOrders.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Completed Orders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {mockData.trading.completedOrders.toLocaleString()}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Price History</CardTitle>
                        <CardDescription>
                          Average carbon credit price over time
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <LineChart
                          data={mockData.trading.priceHistory.map((item) => ({
                            name: item.date,
                            "Price": item.price,
                          }))}
                          categories={["Price"]}
                          colors={["#3b82f6"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Orders by Type</CardTitle>
                        <CardDescription>
                          Distribution of orders by type
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <PieChart
                          data={mockData.trading.ordersByType.map((item) => ({
                            name: item.type,
                            value: item.count,
                          }))}
                          colors={["#3b82f6", "#10b981"]}
                          height={300}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
