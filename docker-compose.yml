services:
  app-dev:
    build:
      context: .
      target: deps
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=**************************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=postgres              # Added
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key-here
      - EMAIL_SERVER=smtp.example.com           # Added
      - EMAIL_FROM=<EMAIL>          # Added
      - ALCHEMY_API_KEY=your-alchemy-api-key
      - ALCHEMY_NETWORK=eth-sepolia
      - ALCHEMY_GAS_MANAGER_POLICY_ID=your-gas-manager-policy-id
      - ETHEREUM_NETWORK=sepolia
      - POLYGON_NETWORK=mumbai
      - OPTIMISM_NETWORK=optimism-sepolia
      - ARBITRUM_NETWORK=arbitrum-sepolia
      - BASE_NETWORK=base-sepolia
      - NODE_ENV=development
      - WALLET_ENCRYPTION_KEY=dev-wallet-encryption-key
    volumes:
      - ./:/app
      - /app/.next
    command: /app/scripts/start-dev.sh

  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=**************************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-postgres}          # Added
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-your-secret-key-here}
      - EMAIL_SERVER=${EMAIL_SERVER:-smtp.example.com}         # Added
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}          # Added
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY:-your-alchemy-api-key}
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-mainnet}
      - ALCHEMY_GAS_MANAGER_POLICY_ID=${ALCHEMY_GAS_MANAGER_POLICY_ID:-your-gas-manager-policy-id}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-mainnet}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-polygon}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum}
      - BASE_NETWORK=${BASE_NETWORK:-base}
      - NODE_ENV=production
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-prod-wallet-encryption-key}
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh

  db:
    image: postgres:16
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=carbon_exchange
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
