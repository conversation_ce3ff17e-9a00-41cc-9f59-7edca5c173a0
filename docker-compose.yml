services:
  # Backend API service
  backend-dev:
    build:
      context: ./backend
      target: development
    ports:
      - "3001:3001"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - PORT=3001
      - NODE_ENV=development
      - FRONTEND_URL=http://localhost:3000
      - DATABASE_URL=**************************************/carbon_exchange
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_NAME=carbon_exchange
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=postgres
      - JWT_SECRET=your-super-secret-jwt-key-here
      - JWT_EXPIRES_IN=7d
      - JWT_REFRESH_EXPIRES_IN=30d
      - EMAIL_SERVER=smtp.example.com
      - EMAIL_FROM=<EMAIL>
      - ALCHEMY_API_KEY=your-alchemy-api-key
      - ALCHEMY_NETWORK=eth-sepolia
      - ALCHEMY_GAS_MANAGER_POLICY_ID=your-gas-manager-policy-id
      - ETHEREUM_NETWORK=sepolia
      - POLYGON_NETWORK=mumbai
      - OPTIMISM_NETWORK=optimism-sepolia
      - ARBITRUM_NETWORK=arbitrum-sepolia
      - BASE_NETWORK=base-sepolia
      - WALLET_ENCRYPTION_KEY=dev-wallet-encryption-key
      - LOG_LEVEL=info
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    command: npm run dev

  # Frontend service (Next.js)
  frontend-dev:
    build:
      context: .
      target: deps
    ports:
      - "3000:3000"
    depends_on:
      - backend-dev
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001/api
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key-here
      - NODE_ENV=development
    volumes:
      - ./:/app
      - /app/.next
    command: /app/scripts/start-dev.sh

  # Backend API service (production)
  backend:
    build:
      context: ./backend
      target: production
    ports:
      - "3001:3001"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - PORT=3001
      - NODE_ENV=production
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
      - DATABASE_URL=${DATABASE_URL:-**************************************/carbon_exchange}
      - DATABASE_HOST=${DATABASE_HOST:-db}
      - DATABASE_PORT=${DATABASE_PORT:-5432}
      - DATABASE_NAME=${DATABASE_NAME:-carbon_exchange}
      - DATABASE_USER=${DATABASE_USER:-postgres}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-postgres}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-here}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - JWT_REFRESH_EXPIRES_IN=${JWT_REFRESH_EXPIRES_IN:-30d}
      - EMAIL_SERVER=${EMAIL_SERVER:-smtp.example.com}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY:-your-alchemy-api-key}
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-mainnet}
      - ALCHEMY_GAS_MANAGER_POLICY_ID=${ALCHEMY_GAS_MANAGER_POLICY_ID:-your-gas-manager-policy-id}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-mainnet}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-polygon}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum}
      - BASE_NETWORK=${BASE_NETWORK:-base}
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-prod-wallet-encryption-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}

  # Frontend service (production)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:3001/api}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3000}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-your-secret-key-here}
      - NODE_ENV=production
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh

  db:
    image: postgres:16
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=carbon_exchange
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
