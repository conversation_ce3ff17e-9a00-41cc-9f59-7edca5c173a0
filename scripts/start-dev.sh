#!/bin/sh

# Wait for the database to be ready
echo "Waiting for database to be ready..."
/bin/sh -c 'until pg_isready -h $DATABASE_HOST -p $DATABASE_PORT -U $DATABASE_USER; do sleep 1; done'

# Run database migrations
echo "Running database migrations..."
pnpm exec prisma migrate dev

# Generate Prisma client
echo "Generating Prisma client..."
pnpm exec prisma generate

# Start the development server
echo "Starting the development server..."
pnpm dev
