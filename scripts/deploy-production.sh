#!/bin/bash

# Production Deployment Script for Carbonix
# This script automates the deployment of the Carbonix application
# in a production environment using Docker and Docker Compose.

set -e  # Exit on error

# Color codes for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Carbonix Production Deployment ===${NC}"
echo -e "${BLUE}Starting deployment process at $(date)${NC}"

# Check if .env file exists
if [ ! -f ".env" ]; then
  echo -e "${RED}Error: .env file not found. Please create it first.${NC}"
  echo -e "Use the template in the README or copy .env.example if it exists."
  exit 1
fi

# Check if domain is set
DOMAIN_NAME=$(grep NEXTAUTH_URL .env | cut -d'=' -f2 | sed 's/https:\/\///' | sed 's/\/.*//')
if [ -z "$DOMAIN_NAME" ]; then
  echo -e "${YELLOW}No domain found in NEXTAUTH_URL. Please enter your domain name:${NC}"
  read -p "Domain (e.g., example.com): " DOMAIN_NAME
  
  if [ -z "$DOMAIN_NAME" ]; then
    echo -e "${RED}No domain provided. Exiting.${NC}"
    exit 1
  fi
  
  # Update NEXTAUTH_URL in .env
  sed -i.bak "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN_NAME|g" .env
  rm -f .env.bak
fi

export DOMAIN_NAME

# Step 1: Build the Docker images
echo -e "\n${BLUE}Step 1: Building Docker images...${NC}"
docker-compose -f docker-compose.prod.yml build

# Step 2: Initialize SSL certificates
echo -e "\n${BLUE}Step 2: Initializing SSL certificates...${NC}"
echo -e "${YELLOW}Note: This step requires that your domain ($DOMAIN_NAME) points to this server and port 80 is accessible.${NC}"
read -p "Do you want to initialize SSL certificates now? (y/n): " SSL_INIT

if [ "$SSL_INIT" = "y" ] || [ "$SSL_INIT" = "Y" ]; then
  bash ./scripts/init-letsencrypt.sh "$DOMAIN_NAME"
else
  echo -e "${YELLOW}Skipping SSL certificate initialization.${NC}"
  echo -e "${YELLOW}You can run ./scripts/init-letsencrypt.sh manually later.${NC}"
  
  # Create required directories for Nginx
  mkdir -p ./nginx/certbot/conf/live/"$DOMAIN_NAME"
  mkdir -p ./nginx/certbot/www
  
  # Create self-signed certificates for development
  echo -e "${YELLOW}Creating self-signed certificates for development...${NC}"
  openssl req -x509 -nodes -newkey rsa:4096 -days 365 \
    -keyout ./nginx/certbot/conf/live/"$DOMAIN_NAME"/privkey.pem \
    -out ./nginx/certbot/conf/live/"$DOMAIN_NAME"/fullchain.pem \
    -subj "/CN=$DOMAIN_NAME"
  
  # Update Nginx configuration with the domain
  export DOMAIN_NAME=$DOMAIN_NAME
  envsubst '${DOMAIN_NAME}' < ./nginx/conf/app.conf > ./nginx/conf/app_updated.conf
  mv ./nginx/conf/app_updated.conf ./nginx/conf/app.conf
fi

# Step 3: Start the production environment
echo -e "\n${BLUE}Step 3: Starting production environment...${NC}"
docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml up -d

# Wait for services to start
echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 10

# Check if all services are running
echo -e "\n${BLUE}Step 4: Checking service status...${NC}"
SERVICES_RUNNING=$(docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml ps --services --filter "status=running" | wc -l)
SERVICES_TOTAL=$(docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml ps --services | wc -l)

if [ "$SERVICES_RUNNING" -eq "$SERVICES_TOTAL" ]; then
  echo -e "${GREEN}All services are running successfully!${NC}"
else
  echo -e "${RED}Warning: Some services might not be running.${NC}"
  docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml ps
fi

# Step 5: Schedule database backup
echo -e "\n${BLUE}Step 5: Scheduling database backups...${NC}"
echo -e "${YELLOW}Setting up daily database backups at 2 AM...${NC}"
(crontab -l 2>/dev/null; echo "0 2 * * * cd $(pwd) && docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml run --rm backup") | crontab -

echo -e "\n${GREEN}=== Deployment Complete ===${NC}"
echo -e "${GREEN}Carbonix has been successfully deployed to production!${NC}"
echo -e "You can access your application at: https://$DOMAIN_NAME"
echo -e "\n${BLUE}Useful commands:${NC}"
echo -e "  View logs:            docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml logs -f"
echo -e "  Restart all services: docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml restart"
echo -e "  Stop all services:    docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml down"
echo -e "  Backup database:      docker-compose -f docker-compose.prod.yml -f docker-compose.override.yml run --rm backup"
echo -e "\n${YELLOW}Don't forget to:${NC}"
echo -e "1. Set up a firewall allowing only ports 80, 443, 3001 (Grafana), and 9090 (Prometheus)"
echo -e "2. Set up monitoring alerts in Grafana (accessible at https://$DOMAIN_NAME:3001)"
echo -e "3. Test your application thoroughly to ensure everything works as expected"
echo -e "4. Create a backup policy and disaster recovery plan"

echo -e "\n${BLUE}Deployment finished at $(date)${NC}"