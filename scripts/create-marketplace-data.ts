import { CarbonCreditStatus, VerificationStatus, UserRole, OrganizationStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating marketplace data...');

  // Check if we already have carbon credits
  const existingCredits = await prisma.carbonCredit.count();

  if (existingCredits > 0) {
    console.log(`Database already has ${existingCredits} carbon credits. Skipping creation.`);
    return;
  }

  // 1. Create a sample admin user if it doesn't exist
  let adminUser = await prisma.user.findFirst({
    where: { role: UserRole.ADMIN }
  });

  if (!adminUser) {
    const hashedPassword = await bcrypt.hash('Admin123!', 10);

    // Check if we have a platform organization
    let platformOrg = await prisma.organization.findFirst({
      where: {
        name: 'Carbon Exchange Platform'
      }
    });

    // Create platform organization if it doesn't exist
    if (!platformOrg) {
      platformOrg = await prisma.organization.create({
        data: {
          name: 'Carbon Exchange Platform',
          description: 'Platform administration organization',
          status: OrganizationStatus.ACTIVE,
          verificationStatus: VerificationStatus.VERIFIED
        }
      });
      console.log(`Created platform organization: ${platformOrg.name} (${platformOrg.id})`);
    }

    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: hashedPassword,
        role: UserRole.ADMIN,
        emailVerified: new Date(),
        organization: {
          connect: {
            id: platformOrg.id
          }
        }
      }
    });
    console.log(`Created admin user: ${adminUser.email}`);
  } else {
    console.log(`Using existing admin user: ${adminUser.email}`);
  }

  // 2. Create sample organizations
  const organizations = [
    {
      name: 'Green Earth Solutions',
      description: 'A leading provider of sustainable environmental solutions',
      website: 'https://greenearth.example.com',
      status: OrganizationStatus.ACTIVE,
      verificationStatus: VerificationStatus.VERIFIED,
      country: 'United States',
      industry: 'Environmental Services',
    },
    {
      name: 'EcoTech Innovations',
      description: 'Developing innovative technologies for a sustainable future',
      website: 'https://ecotech.example.com',
      status: OrganizationStatus.ACTIVE,
      verificationStatus: VerificationStatus.VERIFIED,
      country: 'Germany',
      industry: 'Technology',
    },
    {
      name: 'Sustainable Futures',
      description: 'Committed to creating a sustainable future for all',
      website: 'https://sustainablefutures.example.com',
      status: OrganizationStatus.ACTIVE,
      verificationStatus: VerificationStatus.VERIFIED,
      country: 'Canada',
      industry: 'Renewable Energy',
    }
  ];

  const createdOrgs = [];
  const createdUsers = [];

  for (const orgData of organizations) {
    // Check if organization already exists
    let organization = await prisma.organization.findFirst({
      where: { name: orgData.name }
    });

    if (!organization) {
      organization = await prisma.organization.create({
        data: orgData
      });
      console.log(`Created organization: ${organization.name}`);
    } else {
      console.log(`Using existing organization: ${organization.name}`);
    }

    createdOrgs.push(organization);

    // Create an organization admin user
    const orgAdminEmail = `admin@${organization.name.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`;
    let orgAdmin = await prisma.user.findUnique({
      where: { email: orgAdminEmail }
    });

    if (!orgAdmin) {
      const hashedPassword = await bcrypt.hash('Password123!', 10);
      orgAdmin = await prisma.user.create({
        data: {
          email: orgAdminEmail,
          name: `${organization.name} Admin`,
          password: hashedPassword,
          role: UserRole.ORGANIZATION_ADMIN,
          emailVerified: new Date(),
          organization: {
            connect: {
              id: organization.id
            }
          }
        }
      });
      console.log(`Created organization admin: ${orgAdmin.email}`);
    } else {
      console.log(`Using existing organization admin: ${orgAdmin.email}`);
    }

    createdUsers.push(orgAdmin);
  }

  // 3. Create sample carbon credits
  const carbonCredits = [
    {
      name: 'Amazon Rainforest Conservation Project',
      description: 'This project focuses on preserving the Amazon rainforest by preventing deforestation and promoting sustainable land use practices.',
      quantity: 10000,
      availableQuantity: 10000,
      price: 15.50,
      minPurchaseQuantity: 10,
      vintage: 2022,
      standard: 'Verra',
      methodology: 'Forestry and Land Use',
      location: 'Amazon Basin',
      country: 'Brazil',
      projectId: 'VCS-123456',
      serialNumber: 'VCS-123456-789012',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: createdOrgs[0].id,
      userId: createdUsers[0].id
    },
    {
      name: 'Wind Farm Project - Clean Energy Initiative',
      description: 'A large-scale wind farm project that generates renewable energy and reduces dependency on fossil fuels.',
      quantity: 5000,
      availableQuantity: 5000,
      price: 12.75,
      minPurchaseQuantity: 5,
      vintage: 2023,
      standard: 'Gold Standard',
      methodology: 'Renewable Energy',
      location: 'Midwest Region',
      country: 'United States',
      projectId: 'GS-789012',
      serialNumber: 'GS-789012-345678',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: createdOrgs[1].id,
      userId: createdUsers[1].id
    },
    {
      name: 'Mangrove Restoration Project',
      description: 'Restoration of mangrove ecosystems to enhance carbon sequestration and protect coastal communities.',
      quantity: 7500,
      availableQuantity: 7500,
      price: 18.25,
      minPurchaseQuantity: 15,
      vintage: 2022,
      standard: 'Plan Vivo',
      methodology: 'Forestry and Land Use',
      location: 'Coastal Region',
      country: 'Indonesia',
      projectId: 'PV-345678',
      serialNumber: 'PV-345678-901234',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: createdOrgs[2].id,
      userId: createdUsers[2].id
    },
    {
      name: 'Solar Power Plant Development',
      description: 'Development of a large-scale solar power plant to generate clean energy and reduce greenhouse gas emissions.',
      quantity: 12000,
      availableQuantity: 12000,
      price: 14.00,
      minPurchaseQuantity: 20,
      vintage: 2023,
      standard: 'American Carbon Registry',
      methodology: 'Renewable Energy',
      location: 'Southwestern Region',
      country: 'United States',
      projectId: 'ACR-901234',
      serialNumber: 'ACR-901234-567890',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: createdOrgs[0].id,
      userId: createdUsers[0].id
    },
    {
      name: 'Methane Capture from Landfill',
      description: 'Capture and utilization of methane gas from landfills to generate energy and reduce greenhouse gas emissions.',
      quantity: 8000,
      availableQuantity: 8000,
      price: 11.50,
      minPurchaseQuantity: 10,
      vintage: 2022,
      standard: 'Climate Action Reserve',
      methodology: 'Waste Management',
      location: 'Eastern Region',
      country: 'Canada',
      projectId: 'CAR-567890',
      serialNumber: 'CAR-567890-123456',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: createdOrgs[1].id,
      userId: createdUsers[1].id
    },
  ];

  // Create carbon credits
  for (const creditData of carbonCredits) {
    const credit = await prisma.carbonCredit.create({
      data: creditData
    });

    console.log(`Created carbon credit: ${credit.name} (${credit.id})`);
  }

  console.log('Marketplace data created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating marketplace data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
