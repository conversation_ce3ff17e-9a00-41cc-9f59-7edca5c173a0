#!/bin/bash

# This script sets up the development environment with dockerized PostgreSQL

# Colors for console output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Setting up development environment...${NC}"

# Step 1: Start the PostgreSQL container
echo -e "${YELLOW}Starting PostgreSQL container...${NC}"
./scripts/start-db.sh

# Step 2: Run database migrations
echo -e "${YELLOW}Running database migrations...${NC}"
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/carbon_exchange" pnpm exec prisma migrate dev

# Step 3: Generate Prisma client
echo -e "${YELLOW}Generating Prisma client...${NC}"
pnpm exec prisma generate

# Step 4: Create uploads directory
echo -e "${YELLOW}Creating uploads directory...${NC}"
mkdir -p public/uploads

# Step 5: Start the Next.js development server
echo -e "${GREEN}Starting Next.js development server...${NC}"
pnpm dev
