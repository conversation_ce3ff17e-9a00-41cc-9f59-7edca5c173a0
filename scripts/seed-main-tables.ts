import { UserRole, OrganizationStatus, VerificationStatus, CarbonCreditStatus, OrderStatus, OrderType, TransactionType, TransactionStatus, SubscriptionPlan } from '@prisma/client';
import { db as prisma } from '../src/lib/db';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('Starting to seed main tables...');

  // Check if users already exist
  const existingKvs = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  const existingWill = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (existingKvs && existingWill) {
    console.log('Users already exist. Skipping user creation.');
  } else {
    console.log('Creating users and organizations...');
    await seedUsersAndOrganizations();
  }

  // Seed carbon credits for both organizations
  await seedCarbonCredits();

  // Seed orders and transactions
  await seedOrdersAndTransactions();

  // Seed wallets
  await seedWallets();

  // Seed subscriptions
  await seedSubscriptions();

  console.log('Seed completed successfully!');
}

async function seedUsersAndOrganizations() {
  const hashedPassword = await bcrypt.hash('Password123!', 10);

  // Create first organization
  const greenTech = await prisma.organization.create({
    data: {
      name: 'GreenTech Solutions',
      description: 'Innovative solutions for a sustainable future',
      website: 'https://greentech.example.com',
      status: OrganizationStatus.ACTIVE,
      verificationStatus: VerificationStatus.VERIFIED,
      country: 'United States',
      industry: 'Renewable Energy',
      size: 'MEDIUM',
      foundedYear: 2010,
      primaryContact: 'KVS',
      primaryContactEmail: '<EMAIL>',
      primaryContactPhone: '+1234567890',
    }
  });

  console.log(`Created organization: ${greenTech.name} (${greenTech.id})`);

  // Create KVS user
  const kvsUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'KVS',
      password: hashedPassword,
      role: UserRole.ORGANIZATION_ADMIN,
      emailVerified: new Date(),
      jobTitle: 'CEO',
      departmentName: 'Executive',
      phoneNumber: '+1234567890',
      organization: {
        connect: {
          id: greenTech.id
        }
      }
    }
  });

  console.log(`Created user: ${kvsUser.email} (${kvsUser.id})`);

  // Create second organization
  const carbonTrading = await prisma.organization.create({
    data: {
      name: 'Carbon Trading Inc',
      description: 'Accelerating sustainability goals with secure, transparent carbon credit trading',
      website: 'https://carbontrading.example.com',
      status: OrganizationStatus.ACTIVE,
      verificationStatus: VerificationStatus.VERIFIED,
      country: 'United Kingdom',
      industry: 'Environmental Services',
      size: 'LARGE',
      foundedYear: 2008,
      primaryContact: 'Will Smith',
      primaryContactEmail: '<EMAIL>',
      primaryContactPhone: '+9876543210',
    }
  });

  console.log(`Created organization: ${carbonTrading.name} (${carbonTrading.id})`);

  // Create Will Smith user
  const willUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Will Smith',
      password: hashedPassword,
      role: UserRole.ORGANIZATION_ADMIN,
      emailVerified: new Date(),
      jobTitle: 'CTO',
      departmentName: 'Technology',
      phoneNumber: '+9876543210',
      organization: {
        connect: {
          id: carbonTrading.id
        }
      }
    }
  });

  console.log(`Created user: ${willUser.email} (${willUser.id})`);

  return { greenTech, carbonTrading, kvsUser, willUser };
}

async function seedCarbonCredits() {
  // Get the organizations and users
  const greenTech = await prisma.organization.findFirst({
    where: { name: 'GreenTech Solutions' }
  });

  const carbonTrading = await prisma.organization.findFirst({
    where: { name: 'Carbon Trading Inc' }
  });

  const kvsUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  const willUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!greenTech || !carbonTrading || !kvsUser || !willUser) {
    console.error('Missing required organizations or users');
    return;
  }

  // Check if carbon credits already exist
  const existingCredits = await prisma.carbonCredit.count({
    where: {
      OR: [
        { organizationId: greenTech.id },
        { organizationId: carbonTrading.id }
      ]
    }
  });

  if (existingCredits > 0) {
    console.log(`${existingCredits} carbon credits already exist. Skipping carbon credit creation.`);
    return;
  }

  // Carbon credits for GreenTech Solutions
  const greenTechCredits = [
    {
      name: 'Solar Farm Project',
      description: 'Large-scale solar farm project generating renewable energy and reducing carbon emissions.',
      quantity: 10000,
      availableQuantity: 8000,
      price: 15.75,
      minPurchaseQuantity: 10,
      vintage: 2023,
      standard: 'Gold Standard',
      methodology: 'Renewable Energy',
      location: 'Arizona',
      country: 'United States',
      projectId: 'GT-SOL-2023',
      serialNumber: 'GS-123456-789012',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: greenTech.id,
      userId: kvsUser.id
    },
    {
      name: 'Reforestation Initiative',
      description: 'Reforestation project in previously deforested areas to restore biodiversity and capture carbon.',
      quantity: 5000,
      availableQuantity: 5000,
      price: 18.50,
      minPurchaseQuantity: 5,
      vintage: 2022,
      standard: 'Verra',
      methodology: 'Forestry and Land Use',
      location: 'Amazon Basin',
      country: 'Brazil',
      projectId: 'GT-FOR-2022',
      serialNumber: 'VCS-234567-890123',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: greenTech.id,
      userId: kvsUser.id
    },
    {
      name: 'Methane Capture Project',
      description: 'Capturing methane emissions from landfills and converting them to energy.',
      quantity: 7500,
      availableQuantity: 7500,
      price: 12.25,
      minPurchaseQuantity: 20,
      vintage: 2023,
      standard: 'American Carbon Registry',
      methodology: 'Waste Management',
      location: 'Texas',
      country: 'United States',
      projectId: 'GT-MET-2023',
      serialNumber: 'ACR-345678-901234',
      status: CarbonCreditStatus.PENDING,
      verificationStatus: VerificationStatus.IN_REVIEW,
      organizationId: greenTech.id,
      userId: kvsUser.id
    }
  ];

  // Carbon credits for Carbon Trading Inc
  const carbonTradingCredits = [
    {
      name: 'Wind Energy Development',
      description: 'Development of wind farms to generate clean energy and reduce dependency on fossil fuels.',
      quantity: 12000,
      availableQuantity: 10000,
      price: 14.25,
      minPurchaseQuantity: 10,
      vintage: 2023,
      standard: 'Gold Standard',
      methodology: 'Renewable Energy',
      location: 'Scotland',
      country: 'United Kingdom',
      projectId: 'CT-WIND-2023',
      serialNumber: 'GS-456789-012345',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: carbonTrading.id,
      userId: willUser.id
    },
    {
      name: 'Mangrove Conservation',
      description: 'Conservation of mangrove ecosystems to protect biodiversity and enhance carbon sequestration.',
      quantity: 8000,
      availableQuantity: 8000,
      price: 19.75,
      minPurchaseQuantity: 5,
      vintage: 2022,
      standard: 'Plan Vivo',
      methodology: 'Forestry and Land Use',
      location: 'Coastal Region',
      country: 'Indonesia',
      projectId: 'CT-MAN-2022',
      serialNumber: 'PV-567890-123456',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: carbonTrading.id,
      userId: willUser.id
    },
    {
      name: 'Sustainable Agriculture',
      description: 'Implementing sustainable agricultural practices to reduce emissions and enhance soil carbon.',
      quantity: 6000,
      availableQuantity: 6000,
      price: 16.50,
      minPurchaseQuantity: 10,
      vintage: 2023,
      standard: 'Verra',
      methodology: 'Agriculture',
      location: 'Rural Region',
      country: 'India',
      projectId: 'CT-AGR-2023',
      serialNumber: 'VCS-678901-234567',
      status: CarbonCreditStatus.VERIFIED,
      verificationStatus: VerificationStatus.VERIFIED,
      organizationId: carbonTrading.id,
      userId: willUser.id
    }
  ];

  // Create carbon credits for GreenTech Solutions
  for (const creditData of greenTechCredits) {
    const credit = await prisma.carbonCredit.create({
      data: creditData
    });
    console.log(`Created carbon credit: ${credit.name} (${credit.id}) for ${greenTech.name}`);
  }

  // Create carbon credits for Carbon Trading Inc
  for (const creditData of carbonTradingCredits) {
    const credit = await prisma.carbonCredit.create({
      data: creditData
    });
    console.log(`Created carbon credit: ${credit.name} (${credit.id}) for ${carbonTrading.name}`);
  }
}

async function seedOrdersAndTransactions() {
  // Get the users
  const kvsUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { organization: true }
  });

  const willUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { organization: true }
  });

  if (!kvsUser || !willUser) {
    console.error('Missing required users');
    return;
  }

  // Check if orders already exist
  const existingOrders = await prisma.order.count();
  if (existingOrders > 0) {
    console.log(`${existingOrders} orders already exist. Skipping order creation.`);
    return;
  }

  // Get carbon credits from each organization
  const kvsCarbonCredit = await prisma.carbonCredit.findFirst({
    where: {
      organizationId: kvsUser.organization.id,
      status: CarbonCreditStatus.LISTED
    }
  });

  const willCarbonCredit = await prisma.carbonCredit.findFirst({
    where: {
      organizationId: willUser.organization.id,
      status: CarbonCreditStatus.LISTED
    }
  });

  if (!kvsCarbonCredit || !willCarbonCredit) {
    console.error('Missing required carbon credits');
    return;
  }

  // Create buy order (Will buys from KVS)
  const buyOrder = await prisma.order.create({
    data: {
      type: OrderType.BUY,
      quantity: 100,
      price: kvsCarbonCredit.price,
      status: OrderStatus.COMPLETED,
      buyer: {
        connect: {
          id: willUser.id
        }
      },
      seller: {
        connect: {
          id: kvsUser.id
        }
      },
      carbonCredit: {
        connect: {
          id: kvsCarbonCredit.id
        }
      }
    }
  });

  console.log(`Created buy order: ${buyOrder.id} for ${buyOrder.quantity} tons at $${buyOrder.price} per ton`);

  // Create sell order (KVS sells to Will)
  const sellOrder = await prisma.order.create({
    data: {
      type: OrderType.SELL,
      quantity: 50,
      price: willCarbonCredit.price,
      status: OrderStatus.PENDING,
      seller: {
        connect: {
          id: willUser.id
        }
      },
      buyer: {
        connect: {
          id: kvsUser.id
        }
      },
      carbonCredit: {
        connect: {
          id: willCarbonCredit.id
        }
      }
    }
  });

  console.log(`Created sell order: ${sellOrder.id} for ${sellOrder.quantity} tons at $${sellOrder.price} per ton`);

  // Update carbon credit available quantity for the completed order
  await prisma.carbonCredit.update({
    where: {
      id: kvsCarbonCredit.id
    },
    data: {
      availableQuantity: {
        decrement: buyOrder.quantity
      }
    }
  });

  console.log(`Updated carbon credit available quantity for ${kvsCarbonCredit.name}`);
}

async function seedWallets() {
  // Get the users
  const kvsUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { organization: true }
  });

  const willUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { organization: true }
  });

  if (!kvsUser || !willUser) {
    console.error('Missing required users');
    return;
  }

  // Check if wallets already exist
  const existingWallets = await prisma.wallet.count({
    where: {
      OR: [
        { userId: kvsUser.id },
        { userId: willUser.id }
      ]
    }
  });

  if (existingWallets > 0) {
    console.log(`${existingWallets} wallets already exist. Skipping wallet creation.`);
    return;
  }

  // Create wallet for KVS
  const kvsWallet = await prisma.wallet.create({
    data: {
      address: `0x${Math.random().toString(16).substring(2, 42)}`,
      network: 'ethereum',
      chainId: 1,
      balance: 15000,
      user: {
        connect: {
          id: kvsUser.id
        }
      },
      organization: {
        connect: {
          id: kvsUser.organization.id
        }
      }
    }
  });

  console.log(`Created wallet for ${kvsUser.email}: ${kvsWallet.address}`);

  // Create wallet for Will
  const willWallet = await prisma.wallet.create({
    data: {
      address: `0x${Math.random().toString(16).substring(2, 42)}`,
      network: 'ethereum',
      chainId: 1,
      balance: 20000,
      user: {
        connect: {
          id: willUser.id
        }
      },
      organization: {
        connect: {
          id: willUser.organization.id
        }
      }
    }
  });

  console.log(`Created wallet for ${willUser.email}: ${willWallet.address}`);

  // Get the completed order
  const completedOrder = await prisma.order.findFirst({
    where: {
      status: OrderStatus.COMPLETED
    }
  });

  if (completedOrder) {
    // Create transactions for the completed order
    const buyerTransaction = await prisma.transaction.create({
      data: {
        amount: completedOrder.quantity * completedOrder.price,
        fee: completedOrder.quantity * completedOrder.price * 0.01, // 1% fee
        type: TransactionType.PURCHASE,
        status: TransactionStatus.COMPLETED,
        transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
        blockNumber: Math.floor(Math.random() * 1000000) + 15000000,
        network: 'ethereum',
        chainId: 1,
        wallet: {
          connect: {
            id: willWallet.id
          }
        },
        order: {
          connect: {
            id: completedOrder.id
          }
        }
      }
    });

    console.log(`Created buyer transaction: ${buyerTransaction.id} for $${buyerTransaction.amount}`);

    const sellerTransaction = await prisma.transaction.create({
      data: {
        amount: completedOrder.quantity * completedOrder.price,
        fee: completedOrder.quantity * completedOrder.price * 0.01, // 1% fee
        type: TransactionType.SALE,
        status: TransactionStatus.COMPLETED,
        transactionHash: `0x${Math.random().toString(16).substring(2, 66)}`,
        blockNumber: Math.floor(Math.random() * 1000000) + 15000000,
        network: 'ethereum',
        chainId: 1,
        wallet: {
          connect: {
            id: kvsWallet.id
          }
        },
        order: {
          connect: {
            id: completedOrder.id
          }
        }
      }
    });

    console.log(`Created seller transaction: ${sellerTransaction.id} for $${sellerTransaction.amount}`);
  }
}

async function seedSubscriptions() {
  // Get the organizations
  const greenTech = await prisma.organization.findFirst({
    where: { name: 'GreenTech Solutions' }
  });

  const carbonTrading = await prisma.organization.findFirst({
    where: { name: 'Carbon Trading Inc' }
  });

  if (!greenTech || !carbonTrading) {
    console.error('Missing required organizations');
    return;
  }

  // Check if subscriptions already exist
  const existingSubscriptions = await prisma.subscription.count({
    where: {
      OR: [
        { organizationId: greenTech.id },
        { organizationId: carbonTrading.id }
      ]
    }
  });

  if (existingSubscriptions > 0) {
    console.log(`${existingSubscriptions} subscriptions already exist. Skipping subscription creation.`);
    return;
  }

  // Create subscription for GreenTech Solutions
  const greenTechSubscription = await prisma.subscription.create({
    data: {
      plan: SubscriptionPlan.PREMIUM,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      organization: {
        connect: {
          id: greenTech.id
        }
      }
    }
  });

  console.log(`Created ${greenTechSubscription.plan} subscription for ${greenTech.name}`);

  // Create subscription for Carbon Trading Inc
  const carbonTradingSubscription = await prisma.subscription.create({
    data: {
      plan: SubscriptionPlan.ENTERPRISE,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      organization: {
        connect: {
          id: carbonTrading.id
        }
      }
    }
  });

  console.log(`Created ${carbonTradingSubscription.plan} subscription for ${carbonTrading.name}`);
}

main()
  .catch((e) => {
    console.error('Error seeding main tables:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
