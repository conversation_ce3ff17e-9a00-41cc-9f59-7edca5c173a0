import { OrderType, OrderStatus } from '@prisma/client';

import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating sample sell order...');

  // Find a user with an organization that has carbon credits
  const seller = await prisma.user.findFirst({
    where: {
      NOT: {
        organizationId: null
      },
      carbonCredits: {
        some: {
          status: 'LISTED'
        }
      }
    },
    include: {
      organization: true
    }
  });

  if (!seller || !seller.organization) {
    console.error('No seller user with an organization and carbon credits found.');
    return;
  }

  // Find a carbon credit from the seller's organization
  const carbonCredit = await prisma.carbonCredit.findFirst({
    where: {
      status: 'LISTED',
      organizationId: seller.organization.id
    }
  });

  if (!carbonCredit) {
    console.error('No suitable carbon credit found for selling.');
    return;
  }

  // Find a buyer user from a different organization
  const buyer = await prisma.user.findFirst({
    where: {
      AND: [
        {
          NOT: {
            organizationId: seller.organization.id
          }
        },
        {
          NOT: {
            organizationId: null
          }
        }
      ]
    },
    include: {
      organization: true
    }
  });

  if (!buyer) {
    console.error('No buyer user found from a different organization.');
    return;
  }

  console.log(`Found seller: ${seller.email} (${seller.organization.name})`);
  console.log(`Found carbon credit: ${carbonCredit.name}`);
  console.log(`Found buyer: ${buyer.email} (${buyer.organization.name})`);

  // Create a sell order
  const quantity = Math.min(50, carbonCredit.availableQuantity);
  const order = await prisma.order.create({
    data: {
      type: OrderType.SELL,
      quantity,
      price: carbonCredit.price,
      status: OrderStatus.PENDING,
      seller: {
        connect: {
          id: seller.id
        }
      },
      buyer: {
        connect: {
          id: buyer.id
        }
      },
      carbonCredit: {
        connect: {
          id: carbonCredit.id
        }
      }
    }
  });

  console.log(`Created sell order: ${order.id} for ${quantity} tons at $${order.price} per ton`);
  console.log('Sell order created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating sell order:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
