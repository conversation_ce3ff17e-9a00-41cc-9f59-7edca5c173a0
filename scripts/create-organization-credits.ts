import { CarbonCreditStatus, VerificationStatus } from '@prisma/client';

import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating carbon credits for current user organization...');

  // Find the current user's organization
  // Note: This assumes you're logged in and have a session
  // We'll try to find a user with an organization
  const user = await prisma.user.findFirst({
    where: {
      NOT: {
        organizationId: null
      }
    },
    include: {
      organization: true
    }
  });

  if (!user || !user.organization) {
    console.error('No user with an organization found. Please create an organization and associate it with a user first.');
    return;
  }

  console.log(`Found user: ${user.email} with organization: ${user.organization.name}`);

  // Check if the organization already has carbon credits
  const existingCredits = await prisma.carbonCredit.count({
    where: {
      organizationId: user.organization.id
    }
  });

  if (existingCredits > 0) {
    console.log(`Organization already has ${existingCredits} carbon credits. Skipping creation.`);
    return;
  }

  // Sample carbon credits for the organization
  const carbonCredits = [
    {
      name: 'Reforestation Project - Phase 1',
      description: 'A large-scale reforestation project in degraded forest areas to restore biodiversity and capture carbon.',
      quantity: 5000,
      availableQuantity: 3000,
      price: 18.75,
      minPurchaseQuantity: 10,
      vintage: 2023,
      standard: 'Verra',
      methodology: 'Forestry and Land Use',
      location: 'Northern Region',
      country: 'Costa Rica',
      projectId: 'ORG-123456',
      serialNumber: 'ORG-123456-789012',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: user.organization.id,
      userId: user.id
    },
    {
      name: 'Sustainable Agriculture Initiative',
      description: 'Promoting sustainable agricultural practices that reduce emissions and increase carbon sequestration in soils.',
      quantity: 3500,
      availableQuantity: 3500,
      price: 14.50,
      minPurchaseQuantity: 5,
      vintage: 2022,
      standard: 'Gold Standard',
      methodology: 'Agriculture',
      location: 'Central Region',
      country: 'Kenya',
      projectId: 'ORG-234567',
      serialNumber: 'ORG-234567-890123',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
      listingDate: new Date(),
      organizationId: user.organization.id,
      userId: user.id
    },
    {
      name: 'Community-Based Forest Conservation',
      description: 'Working with local communities to protect existing forests and prevent deforestation.',
      quantity: 8000,
      availableQuantity: 8000,
      price: 16.25,
      minPurchaseQuantity: 20,
      vintage: 2023,
      standard: 'Plan Vivo',
      methodology: 'Forestry and Land Use',
      location: 'Western Region',
      country: 'Peru',
      projectId: 'ORG-345678',
      serialNumber: 'ORG-345678-901234',
      status: CarbonCreditStatus.PENDING,
      verificationStatus: VerificationStatus.IN_REVIEW,
      organizationId: user.organization.id,
      userId: user.id
    },
    {
      name: 'Renewable Energy Grid Integration',
      description: 'Project to integrate renewable energy sources into the existing power grid, reducing reliance on fossil fuels.',
      quantity: 10000,
      availableQuantity: 10000,
      price: 12.00,
      minPurchaseQuantity: 50,
      vintage: 2022,
      standard: 'American Carbon Registry',
      methodology: 'Renewable Energy',
      location: 'Eastern Region',
      country: 'India',
      projectId: 'ORG-456789',
      serialNumber: 'ORG-456789-012345',
      status: CarbonCreditStatus.VERIFIED,
      verificationStatus: VerificationStatus.VERIFIED,
      organizationId: user.organization.id,
      userId: user.id
    },
    {
      name: 'Coastal Mangrove Restoration',
      description: 'Restoring mangrove ecosystems along coastal areas to enhance carbon sequestration and protect against erosion.',
      quantity: 6000,
      availableQuantity: 6000,
      price: 20.50,
      minPurchaseQuantity: 15,
      vintage: 2023,
      standard: 'Verra',
      methodology: 'Wetlands Restoration',
      location: 'Coastal Region',
      country: 'Indonesia',
      projectId: 'ORG-567890',
      serialNumber: 'ORG-567890-123456',
      status: CarbonCreditStatus.RETIRED,
      verificationStatus: VerificationStatus.VERIFIED,
      retirementDate: new Date(),
      retirementReason: 'Corporate carbon neutrality commitment',
      retirementBeneficiary: user.organization.name,
      organizationId: user.organization.id,
      userId: user.id
    }
  ];

  // Create carbon credits for the organization
  for (const creditData of carbonCredits) {
    const credit = await prisma.carbonCredit.create({
      data: creditData
    });

    console.log(`Created carbon credit: ${credit.name} (${credit.id}) with status ${credit.status}`);
  }

  console.log('Organization carbon credits created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating organization carbon credits:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
