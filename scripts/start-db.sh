#!/bin/bash

# This script starts the PostgreSQL container for local development

echo "Starting PostgreSQL container for development..."
docker compose up -d db

echo "Waiting for PostgreSQL to be ready..."
until docker compose exec db pg_isready -U postgres; do
  echo "PostgreSQL is not ready yet... waiting"
  sleep 2
done

echo "PostgreSQL is ready!"
echo "Connection details:"
echo "  Host: localhost"
echo "  Port: 5432"
echo "  User: postgres"
echo "  Password: postgres"
echo "  Database: carbon_exchange"
echo ""
echo "To connect using psql:"
echo "  docker compose exec db psql -U postgres -d carbon_exchange"
echo ""
echo "To stop the database:"
echo "  docker compose down db"
