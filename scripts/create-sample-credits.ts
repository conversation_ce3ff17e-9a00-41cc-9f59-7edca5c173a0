// This script is for development purposes only and is not used in production
// Commenting out to avoid build errors
/*
import { CarbonCreditStatus, VerificationStatus } from '@prisma/client';

import { db as prisma } from '../src/lib/db';

async function main() {
  console.log('Creating sample carbon credits...');

  // Check if we already have carbon credits
  const existingCredits = await prisma.carbonCredit.count();

  if (existingCredits > 0) {
    console.log(`Database already has ${existingCredits} carbon credits. Skipping creation.`);
    return;
  }

  // Find an organization to associate with the carbon credits
  const organization = await prisma.organization.findFirst({
    where: {
      status: 'ACTIVE'
    }
  });

  if (!organization) {
    console.error('No active organization found. Please create an organization first.');
    return;
  }

  // Find a user from the organization to associate with the carbon credits
  const user = await prisma.user.findFirst({
    where: {
      organizationId: organization.id
    }
  });

  if (!user) {
    console.error('No user found for the organization. Please create a user first.');
    return;
  }

  console.log(`Using organization: ${organization.name} (${organization.id})`);
  console.log(`Using user: ${user.email} (${user.id})`);

  // Sample carbon credits
  const carbonCredits = [
    {
      name: 'Amazon Rainforest Conservation Project',
      description: 'This project focuses on preserving the Amazon rainforest by preventing deforestation and promoting sustainable land use practices.',
      quantity: 10000,
      availableQuantity: 10000,
      price: 15.50,
      minPurchaseQuantity: 10,
      vintage: 2022,
      standard: 'Verra',
      methodology: 'Forestry and Land Use',
      location: 'Amazon Basin',
      country: 'Brazil',
      projectId: 'VCS-123456',
      serialNumber: 'VCS-123456-789012',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
    },
    {
      name: 'Wind Farm Project - Clean Energy Initiative',
      description: 'A large-scale wind farm project that generates renewable energy and reduces dependency on fossil fuels.',
      quantity: 5000,
      availableQuantity: 5000,
      price: 12.75,
      minPurchaseQuantity: 5,
      vintage: 2023,
      standard: 'Gold Standard',
      methodology: 'Renewable Energy',
      location: 'Midwest Region',
      country: 'United States',
      projectId: 'GS-789012',
      serialNumber: 'GS-789012-345678',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
    },
    {
      name: 'Mangrove Restoration Project',
      description: 'Restoration of mangrove ecosystems to enhance carbon sequestration and protect coastal communities.',
      quantity: 7500,
      availableQuantity: 7500,
      price: 18.25,
      minPurchaseQuantity: 15,
      vintage: 2022,
      standard: 'Plan Vivo',
      methodology: 'Forestry and Land Use',
      location: 'Coastal Region',
      country: 'Indonesia',
      projectId: 'PV-345678',
      serialNumber: 'PV-345678-901234',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
    },
    {
      name: 'Solar Power Plant Development',
      description: 'Development of a large-scale solar power plant to generate clean energy and reduce greenhouse gas emissions.',
      quantity: 12000,
      availableQuantity: 12000,
      price: 14.00,
      minPurchaseQuantity: 20,
      vintage: 2023,
      standard: 'American Carbon Registry',
      methodology: 'Renewable Energy',
      location: 'Southwestern Region',
      country: 'United States',
      projectId: 'ACR-901234',
      serialNumber: 'ACR-901234-567890',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
    },
    {
      name: 'Methane Capture from Landfill',
      description: 'Capture and utilization of methane gas from landfills to generate energy and reduce greenhouse gas emissions.',
      quantity: 8000,
      availableQuantity: 8000,
      price: 11.50,
      minPurchaseQuantity: 10,
      vintage: 2022,
      standard: 'Climate Action Reserve',
      methodology: 'Waste Management',
      location: 'Eastern Region',
      country: 'Canada',
      projectId: 'CAR-567890',
      serialNumber: 'CAR-567890-123456',
      status: CarbonCreditStatus.LISTED,
      verificationStatus: VerificationStatus.VERIFIED,
    },
  ];

  // First, check if we have projects
  const projects = await prisma.project.findMany({
    take: 5,
  });

  if (projects.length === 0) {
    console.log('No projects found. Creating sample projects first...');

    // Create sample projects
    for (const creditData of carbonCredits) {
      await prisma.project.create({
        data: {
          id: creditData.projectId, // Use the same ID as referenced in carbon credits
          name: `Project for ${creditData.name}`,
          description: `Project supporting ${creditData.description}`,
          type: 'FORESTRY',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED',
          organization: {
            connect: {
              id: organization.id
            }
          }
        }
      });
      console.log(`Created project with ID: ${creditData.projectId}`);
    }
  } else {
    console.log(`Found ${projects.length} existing projects`);
  }

  // Create carbon credits with organization and user association
  for (const creditData of carbonCredits) {
    // Create a new object with only the fields that are in the CarbonCredit model
    const creditDataWithoutProjectId = { ...creditData };
    delete creditDataWithoutProjectId.projectId; // Remove projectId from the spread object

    const credit = await prisma.carbonCredit.create({
      data: {
        ...creditDataWithoutProjectId,
        user: {
          connect: {
            id: user.id
          }
        },
        organization: {
          connect: {
            id: organization.id
          }
        },
        project: {
          connect: {
            id: creditData.projectId
          }
        }
      },
    });

    console.log(`Created carbon credit: ${credit.name} (${credit.id})`);
  }

  console.log('Sample carbon credits created successfully!');
}

main()
  .catch((e) => {
    console.error('Error creating sample carbon credits:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
*/
