import { db as prisma } from '../../src/lib/db';

// Function to generate a random Ethereum address
function generateEthAddress() {
  return `0x${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
}

// Function to generate a random transaction hash
function generateTxHash() {
  return `0x${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
}

// Networks to use for wallets
const networks = [
  { name: 'ethereum', chainId: 1, isTestnet: false },
  { name: 'polygon', chainId: 137, isTestnet: false },
  { name: 'arbitrum', chainId: 42161, isTestnet: false },
  { name: 'optimism', chainId: 10, isTestnet: false },
  { name: 'base', chainId: 8453, isTestnet: false },
  { name: 'sepolia', chainId: 11155111, isTestnet: true },
  { name: 'mumbai', chainId: 80001, isTestnet: true }
];

// Function to seed wallets and transactions
export async function seedWalletsAndTransactions(organizations: any[], users: any[][], projects: any[], carbonCredits: any[]) {
  console.log('Starting to seed wallets and transactions...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  console.log(`Received users: ${users ? users.flat().length : 0}`);
  console.log(`Received projects: ${projects ? projects.length : 0}`);
  console.log(`Received carbon credits: ${carbonCredits ? carbonCredits.length : 0}`);

  const wallets = [];
  const transactions = [];

  try {

  // Create organization wallets
  for (const organization of organizations) {
    // Create 1-3 wallets per organization
    const walletCount = Math.floor(Math.random() * 3) + 1;

    for (let i = 0; i < walletCount; i++) {
      // Select a random network
      const network = networks[Math.floor(Math.random() * networks.length)];

      // Generate wallet name
      const walletTypes = ['Main', 'Treasury', 'Trading', 'Retirement', 'Tokenization'];
      const walletName = `${organization.name} ${walletTypes[i % walletTypes.length]} Wallet`;

      // Check if wallet already exists
      const existingWallet = await prisma.wallet.findFirst({
        where: {
          name: walletName,
          organizationId: organization.id
        }
      });

      if (existingWallet) {
        console.log(`Wallet ${walletName} already exists for ${organization.name}. Skipping creation.`);
        wallets.push(existingWallet);
        continue;
      }

      // Create wallet
      const newWallet = await prisma.wallet.create({
        data: {
          name: walletName,
          address: generateEthAddress(),
          network: network.name,
          chainId: network.chainId,
          isTestnet: network.isTestnet,
          walletType: i === 0 ? "GENERAL" :
                i === 1 ? "TRADING" :
                i === 2 ? "RETIREMENT" : "TOKENIZATION",
          purpose: i === 0 ? 'General' :
                  i === 1 ? 'Trading' :
                  i === 2 ? 'Retirement' : 'Tokenization',
          balance: Math.random() * 10, // Random ETH balance
          organization: {
            connect: {
              id: organization.id
            }
          }
        }
      });

      console.log(`Created wallet: ${newWallet.name} (${newWallet.id}) for ${organization.name}`);
      wallets.push(newWallet);

      // Create wallet security settings
      await prisma.walletSecuritySetting.create({
        data: {
          twoFactorEnabled: Math.random() > 0.5,
          twoFactorType: 'app',
          whitelistedAddresses: [],
          blacklistedAddresses: [],
          delayedWithdrawals: Math.random() > 0.7,
          withdrawalDelayHours: Math.random() > 0.7 ? 24 : null,
          notificationsEnabled: true,
          autoLockEnabled: Math.random() > 0.6,
          autoLockTimeoutMinutes: Math.random() > 0.6 ? 30 : null,
          wallet: {
            connect: {
              id: newWallet.id
            }
          }
        }
      });

      // Create tokens in the wallet (50% chance)
      if (Math.random() > 0.5) {
        const tokenCount = Math.floor(Math.random() * 3) + 1; // 1-3 tokens
        const tokenSymbols = ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC'];

        for (let j = 0; j < tokenCount; j++) {
          const symbol = tokenSymbols[j % tokenSymbols.length];
          const decimals = symbol === 'WBTC' ? 8 : 6;
          const balance = Math.floor(Math.random() * 100000) + 1000; // 1,000 - 101,000 tokens

          await prisma.token.create({
            data: {
              contractAddress: generateEthAddress(),
              name: symbol === 'USDC' ? 'USD Coin' :
                   symbol === 'USDT' ? 'Tether' :
                   symbol === 'DAI' ? 'Dai Stablecoin' :
                   symbol === 'WETH' ? 'Wrapped Ether' : 'Wrapped Bitcoin',
              symbol,
              decimals,
              balance: balance.toString(),
              wallet: {
                connect: {
                  id: newWallet.id
                }
              }
            }
          });

          console.log(`Added ${symbol} token to wallet ${newWallet.name}`);
        }
      }

      // Create NFTs in the wallet (30% chance)
      if (Math.random() > 0.7) {
        const nftCount = Math.floor(Math.random() * 2) + 1; // 1-2 NFTs

        for (let j = 0; j < nftCount; j++) {
          await prisma.nFT.create({
            data: {
              contractAddress: generateEthAddress(),
              tokenId: Math.floor(Math.random() * 10000).toString(),
              name: `Carbon Credit NFT #${Math.floor(Math.random() * 1000)}`,
              description: 'NFT representing verified carbon credits',
              // Removed imageUrl field as it doesn't exist in the schema
              metadata: {
                attributes: [
                  { trait_type: 'Project Type', value: 'Forestry' },
                  { trait_type: 'Vintage', value: '2023' },
                  { trait_type: 'Standard', value: 'Verra' }
                ]
              },
              wallet: {
                connect: {
                  id: newWallet.id
                }
              }
            }
          });

          console.log(`Added NFT to wallet ${newWallet.name}`);
        }
      }

      // Create wallet access controls for users in the organization
      const orgUsers = users.flat().filter(user => user.organizationId === organization.id);

      for (const user of orgUsers) {
        // Admin gets full access, others get limited access
        const isAdmin = user.role === 'ORGANIZATION_ADMIN';

        await prisma.walletAccessControl.create({
          data: {
            wallet: {
              connect: {
                id: newWallet.id
              }
            },
            userId: user.id,
            accessLevel: isAdmin ? 'ADMIN' : Math.random() > 0.5 ? 'MANAGER' : 'VIEWER',
            canApprove: isAdmin || Math.random() > 0.7,
            canInitiate: isAdmin || Math.random() > 0.5,
            canView: true,
            customLimits: isAdmin ? null : {
              dailyLimit: Math.floor(Math.random() * 10000) + 1000,
              transactionLimit: Math.floor(Math.random() * 1000) + 100
            }
          }
        });

        console.log(`Added access control for ${user.name} to wallet ${newWallet.name}`);
      }

      // Create wallet audit logs
      const auditCount = Math.floor(Math.random() * 5) + 2; // 2-6 audit logs
      const auditActions = ['Wallet created', 'Funds deposited', 'Transaction initiated', 'Security settings updated', 'Access granted'];

      // Only create audit logs if there are users in the organization
      if (orgUsers.length > 0) {
        for (let j = 0; j < auditCount; j++) {
          const action = auditActions[j % auditActions.length];
          const user = orgUsers[Math.floor(Math.random() * orgUsers.length)];

          await prisma.walletAuditLog.create({
            data: {
              wallet: {
                connect: {
                  id: newWallet.id
                }
              },
              action,
              userId: user.id,
              ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              details: {
                timestamp: new Date().toISOString(),
                success: true
              },
              timestamp: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000) // 0-30 days ago
          }
        });
        }
      }

      // Create transactions for the wallet (only if there are users in the organization)
      if (orgUsers.length > 0) {
        const txCount = Math.floor(Math.random() * 10) + 5; // 5-14 transactions

        for (let j = 0; j < txCount; j++) {
        // Determine transaction type
        const txTypes = [
          "DEPOSIT",
          "WITHDRAWAL",
          "PURCHASE",
          "SALE",
          "TRANSFER",
          "RETIREMENT",
          "TOKENIZATION"
        ];
        const txType = txTypes[Math.floor(Math.random() * txTypes.length)];

        // Determine amount based on type
        let amount;
        if (txType === "DEPOSIT" || txType === "WITHDRAWAL") {
          amount = Math.random() * 5 + 0.1; // 0.1-5.1 ETH
        } else if (txType === "PURCHASE" || txType === "SALE") {
          amount = Math.floor(Math.random() * 1000) + 100; // 100-1100 credits
        } else {
          amount = Math.floor(Math.random() * 500) + 50; // 50-550 units
        }

        // Determine status (mostly COMPLETED, but some with other statuses)
        let status = "COMPLETED";
        const statusRoll = Math.random();
        if (statusRoll < 0.1) status = "PENDING";
        else if (statusRoll < 0.15) status = "FAILED";
        else if (statusRoll < 0.2) status = "CANCELLED";

        // Create transaction
        const user = orgUsers[Math.floor(Math.random() * orgUsers.length)];
        const txDate = new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000); // 0-90 days ago

        const newTransaction = await prisma.transaction.create({
          data: {
            amount,
            fee: amount * 0.01, // 1% fee
            gasPrice: Math.random() * 100 + 10, // 10-110 Gwei
            gasLimit: 21000 + Math.floor(Math.random() * 100000), // 21000-121000
            gasUsed: status === "COMPLETED" ? 21000 + Math.floor(Math.random() * 80000) : null, // 21000-101000
            maxFeePerGas: Math.random() * 150 + 20, // 20-170 Gwei
            maxPriorityFeePerGas: Math.random() * 10 + 1, // 1-11 Gwei
            type: txType,
            status,
            transactionHash: status === "COMPLETED" ? generateTxHash() : null,
            blockNumber: status === "COMPLETED" ? 15000000 + Math.floor(Math.random() * 1000000) : null,
            network: network.name,
            category: txType === "DEPOSIT" || txType === "SALE" ? "INCOME" :
                     txType === "WITHDRAWAL" || txType === "PURCHASE" ? "EXPENSE" :
                     txType === "TRANSFER" ? "TRANSFER" :
                     txType === "RETIREMENT" ? "RETIREMENT" :
                     txType === "TOKENIZATION" ? "TOKENIZATION" : "OTHER",
            notes: `${txType.charAt(0) + txType.slice(1).toLowerCase()} transaction`,
            createdAt: txDate,
            wallet: {
              connect: {
                id: newWallet.id
              }
            },
            user: {
              connect: {
                id: user.id
              }
            },
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });

        console.log(`Created transaction: ${newTransaction.type} (${newTransaction.id}) for wallet ${newWallet.name}`);
        transactions.push(newTransaction);

        // Create transaction audit (30% chance)
        if (Math.random() > 0.7) {
          await prisma.transactionAudit.create({
            data: {
              status: 'VERIFIED',
              auditDate: new Date(txDate.getTime() + 24 * 60 * 60 * 1000), // 1 day after transaction
              auditor: 'Compliance Team',
              notes: 'Regular compliance check',
              transaction: {
                connect: {
                  id: newTransaction.id
                }
              }
            }
          });
        }
      }
      }

      // Create bridge transactions (20% chance)
      if (Math.random() > 0.8) {
        // Pick a different network for destination
        const destNetworkIndex = (networks.indexOf(network) + 1 + Math.floor(Math.random() * (networks.length - 1))) % networks.length;
        const destNetwork = networks[destNetworkIndex];

        await prisma.bridgeTransaction.create({
          data: {
            sourceWallet: {
              connect: {
                id: newWallet.id
              }
            },
            sourceNetwork: network.name,
            sourceChainId: network.chainId,
            destinationAddress: generateEthAddress(),
            destinationNetwork: destNetwork.name,
            destinationChainId: destNetwork.chainId,
            tokenAddress: null, // Native token
            tokenSymbol: network.name === 'ethereum' ? 'ETH' :
                        network.name === 'polygon' ? 'MATIC' :
                        network.name === 'arbitrum' ? 'ETH' :
                        network.name === 'optimism' ? 'ETH' :
                        network.name === 'base' ? 'ETH' :
                        network.name === 'sepolia' ? 'ETH' : 'MATIC',
            amount: (Math.random() * 2 + 0.1).toString(), // 0.1-2.1 tokens
            fee: (Math.random() * 0.01 + 0.001).toString(), // 0.001-0.011 tokens
            status: Math.random() > 0.8 ? 'PENDING' : 'COMPLETED',
            sourceTxHash: generateTxHash(),
            destinationTxHash: Math.random() > 0.2 ? generateTxHash() : null,
            bridgeProvider: ['Connext', 'Hop Protocol', 'Across', 'Stargate'][Math.floor(Math.random() * 4)],
            // Use createdAt and updatedAt instead of initiatedAt and completedAt
            createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // 0-30 days ago
            updatedAt: Math.random() > 0.2 ? new Date(Date.now() - Math.floor(Math.random() * 29) * 24 * 60 * 60 * 1000) : new Date()
          }
        });

        console.log(`Created bridge transaction for wallet ${newWallet.name}`);
      }

      // Create gas settings
      await prisma.gasSetting.create({
        data: {
          defaultGasPrice: Math.floor(Math.random() * 100) + 10, // 10-110 Gwei
          maxGasPrice: Math.floor(Math.random() * 100) + 100, // 100-200 Gwei
          defaultMaxPriorityFee: Math.floor(Math.random() * 10) + 1, // 1-11 Gwei
          defaultMaxFeePerGas: Math.floor(Math.random() * 100) + 50, // 50-150 Gwei
          gasLimitMultiplier: 1.1,
          optimizationEnabled: true,
          alertThreshold: Math.floor(Math.random() * 100) + 150, // 150-250 Gwei
          alertEnabled: Math.random() > 0.7,
          wallet: {
            connect: {
              id: newWallet.id
            }
          }
        }
      });
    }
  }

  // Create user wallets
  for (const userGroup of users) {
    for (const user of userGroup) {
      // 70% chance to create a personal wallet
      if (Math.random() > 0.3) {
        // Select a random network
        const network = networks[Math.floor(Math.random() * networks.length)];

        // Check if wallet already exists
        const existingWallet = await prisma.wallet.findFirst({
          where: {
            userId: user.id,
            organizationId: null
          }
        });

        if (existingWallet) {
          console.log(`Personal wallet already exists for ${user.name}. Skipping creation.`);
          wallets.push(existingWallet);
          continue;
        }

        // Create wallet
        const newWallet = await prisma.wallet.create({
          data: {
            name: `${user.name}'s Personal Wallet`,
            address: generateEthAddress(),
            network: network.name,
            chainId: network.chainId,
            isTestnet: network.isTestnet,
            walletType: "GENERAL",
            purpose: 'Personal',
            balance: Math.random() * 5, // Random ETH balance
            user: {
              connect: {
                id: user.id
              }
            }
          }
        });

        console.log(`Created personal wallet: ${newWallet.name} (${newWallet.id}) for ${user.name}`);
        wallets.push(newWallet);

        // Create a few transactions
        const txCount = Math.floor(Math.random() * 5) + 2; // 2-6 transactions

        for (let i = 0; i < txCount; i++) {
          // Simple deposit or withdrawal
          const txType = Math.random() > 0.5 ? "DEPOSIT" : "WITHDRAWAL";
          const amount = Math.random() * 1 + 0.05; // 0.05-1.05 ETH

          const newTransaction = await prisma.transaction.create({
            data: {
              amount,
              fee: amount * 0.01, // 1% fee
              gasPrice: Math.random() * 100 + 10, // 10-110 Gwei
              gasLimit: 21000,
              gasUsed: 21000,
              type: txType,
              status: "COMPLETED",
              transactionHash: generateTxHash(),
              blockNumber: 15000000 + Math.floor(Math.random() * 1000000),
              network: network.name,
              category: txType === "DEPOSIT" ? "INCOME" : "EXPENSE",
              notes: `${txType.charAt(0) + txType.slice(1).toLowerCase()} to personal wallet`,
              createdAt: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000), // 0-60 days ago
              wallet: {
                connect: {
                  id: newWallet.id
                }
              },
              // Removed user relation as it doesn't exist in the Transaction model
            }
          });

          console.log(`Created transaction: ${newTransaction.type} (${newTransaction.id}) for personal wallet of ${user.name}`);
          transactions.push(newTransaction);
        }
      }
    }
  }

  // Create project wallets
  for (const project of projects) {
    // 50% chance to create a project wallet
    if (Math.random() > 0.5) {
      // Find the organization that owns this project
      const organization = organizations.find(org => org.id === project.organizationId);
      if (!organization) continue;

      // Select a random network
      const network = networks[Math.floor(Math.random() * networks.length)];

      // Check if wallet already exists
      const existingWallet = await prisma.wallet.findFirst({
        where: {
          projectId: project.id
        }
      });

      if (existingWallet) {
        console.log(`Project wallet already exists for ${project.name}. Skipping creation.`);
        wallets.push(existingWallet);
        continue;
      }

      // Create wallet
      const newWallet = await prisma.wallet.create({
        data: {
          name: `${project.name} Project Wallet`,
          address: generateEthAddress(),
          network: network.name,
          chainId: network.chainId,
          isTestnet: network.isTestnet,
          walletType: "PROJECT",
          purpose: 'Project',
          balance: Math.random() * 3, // Random ETH balance
          organization: {
            connect: {
              id: organization.id
            }
          },
          project: {
            connect: {
              id: project.id
            }
          }
        }
      });

      console.log(`Created project wallet: ${newWallet.name} (${newWallet.id}) for ${project.name}`);
      wallets.push(newWallet);

      // Create a few transactions related to the project
      const txCount = Math.floor(Math.random() * 3) + 1; // 1-3 transactions

      for (let i = 0; i < txCount; i++) {
        // Project-related transactions
        const txTypes = ["DEPOSIT", "TOKENIZATION"];
        const txType = txTypes[Math.floor(Math.random() * txTypes.length)];
        const amount = txType === "DEPOSIT" ? Math.random() * 2 + 0.5 : Math.floor(Math.random() * 1000) + 100;

        // Find a user from the organization
        const orgUsers = users.flat().filter(user => user.organizationId === organization.id);
        const user = orgUsers.length > 0 ? orgUsers[Math.floor(Math.random() * orgUsers.length)] : null;

        if (!user) continue;

        const newTransaction = await prisma.transaction.create({
          data: {
            amount,
            fee: amount * (txType === "DEPOSIT" ? 0.01 : 0.02), // 1-2% fee
            gasPrice: Math.random() * 100 + 10, // 10-110 Gwei
            gasLimit: txType === "DEPOSIT" ? 21000 : 100000,
            gasUsed: txType === "DEPOSIT" ? 21000 : Math.floor(Math.random() * 50000) + 50000,
            type: txType,
            status: "COMPLETED",
            transactionHash: generateTxHash(),
            blockNumber: 15000000 + Math.floor(Math.random() * 1000000),
            network: network.name,
            category: txType === "DEPOSIT" ? "INCOME" : "TOKENIZATION",
            notes: txType === "DEPOSIT" ?
                        'Project funding deposit' :
                        'Tokenization of carbon credits',
            createdAt: new Date(Date.now() - Math.floor(Math.random() * 180) * 24 * 60 * 60 * 1000), // 0-180 days ago
            wallet: {
              connect: {
                id: newWallet.id
              }
            },
            // Removed user relation as it doesn't exist in the Transaction model
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });

        console.log(`Created transaction: ${newTransaction.type} (${newTransaction.id}) for project wallet of ${project.name}`);
        transactions.push(newTransaction);
      }
    }
  }

    return { wallets, transactions };
  } catch (error) {
    console.error('Error in seedWalletsAndTransactions:', error);
    return { wallets, transactions };
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without organizations, users, projects, and carbon credits, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
