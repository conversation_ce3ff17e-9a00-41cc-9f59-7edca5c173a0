import { db as prisma } from '../../src/lib/db';

// Function to seed compliance data
export async function seedCompliance(organizations: any[], users: any[][]) {
  console.log('Starting to seed compliance data...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  console.log(`Received users: ${users ? users.flat().length : 0}`);

  try {

  // Create KYC verifications for organizations
  for (const organization of organizations) {
    // Check if KYC verification already exists
    const existingKyc = await prisma.kycVerification.findUnique({
      where: { organizationId: organization.id }
    });

    if (existingKyc) {
      console.log(`KYC verification already exists for ${organization.name}. Skipping creation.`);
      continue;
    }

    // Determine KYC level based on organization status
    let kycLevel = "NONE";
    let status = "PENDING";

    if (organization.status === 'ACTIVE') {
      if (organization.verificationStatus === 'VERIFIED') {
        kycLevel = Math.random() > 0.3 ? "ADVANCED" : "INTERMEDIATE";
        status = "APPROVED";
      } else if (organization.verificationStatus === 'IN_REVIEW') {
        kycLevel = "BASIC";
        status = "IN_REVIEW";
      } else {
        kycLevel = "BASIC";
        status = "PENDING";
      }
    }

    // Create KYC verification
    const kyc = await prisma.kycVerification.create({
      data: {
        status,
        level: kycLevel,
        lastChecked: new Date(),
        expiresAt: status === "APPROVED" ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) : null,
        verifier: status === "APPROVED" ? ['Jumio', 'Onfido', 'Sumsub', 'Veriff'][Math.floor(Math.random() * 4)] : null,
        additionalInfo: status === "APPROVED" ? {
          verificationId: `VID-${Math.floor(Math.random() * 1000000)}`,
          riskScore: Math.floor(Math.random() * 30),
          riskLevel: "LOW"
        } : null,
        // Removed notes field as it doesn't exist in the schema
        organization: {
          connect: {
            id: organization.id
          }
        }
      }
    });

    console.log(`Created KYC verification for ${organization.name} with level ${kycLevel}`);

    // Create KYC verification history
    await prisma.kycVerificationHistory.create({
      data: {
        status,
        timestamp: new Date(),
        notes: status === "APPROVED" ? 'Initial verification completed' :
              status === "IN_REVIEW" ? 'Verification in progress' : 'Verification pending',
        kycVerification: {
          connect: {
            id: kyc.id
          }
        }
      }
    });

    // Create compliance documents for the organization
    if (status !== "PENDING") {
      const docTypes = ['BUSINESS_REGISTRATION', 'TAX_CERTIFICATE', 'ADDRESS_PROOF', 'METHODOLOGY', 'VERIFICATION_REPORT'];
      const docStatuses = status === "APPROVED" ? ["APPROVED"] : ["IN_REVIEW", "PENDING"];

      for (const docType of docTypes) {
        const docStatus = docStatuses[Math.floor(Math.random() * docStatuses.length)];

        // Find an admin user from the organization
        const adminUser = users.flat().find(user => user.organizationId === organization.id && user.role === 'ORGANIZATION_ADMIN');
        if (!adminUser) continue;

        await prisma.complianceDocument.create({
          data: {
            type: docType,
            name: `${docType.replace(/_/g, ' ')} - ${organization.name}`,
            url: `https://example.com/documents/${organization.id}/${docType.toLowerCase()}.pdf`,
            status: docStatus,
            notes: docStatus === "IN_REVIEW" ? 'Under review by compliance team' : null,
            organization: {
              connect: {
                id: organization.id
              }
            },
            user: {
              connect: {
                id: adminUser.id
              }
            }
          }
        });

        console.log(`Created compliance document ${docType} for ${organization.name}`);
      }
    }

    // Create AML check for the organization
    await prisma.amlCheck.create({
      data: {
        status: status,
        riskLevel: status === "APPROVED" ? "LOW" :
                  status === "IN_REVIEW" ? "MEDIUM" : "HIGH",
        lastChecked: status === "APPROVED" ? new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000) : null,
        expiresAt: status === "APPROVED" ? new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) : null,
        checkMethod: 'THIRD_PARTY',
        checkProvider: ['Chainalysis', 'Elliptic', 'Coinfirm', 'ComplyAdvantage'][Math.floor(Math.random() * 4)],
        referenceId: `AML-${Math.floor(Math.random() * 1000000)}`,
        findings: {
          riskScore: status === "APPROVED" ? Math.floor(Math.random() * 30) : Math.floor(Math.random() * 30) + 30,
          matches: [],
          warnings: status === "IN_REVIEW" ? ['Incomplete information'] : [],
          errors: [],
          checkDate: new Date().toISOString()
        },
        actionTaken: status === "APPROVED" ? 'Approved' :
                    status === "IN_REVIEW" ? 'Flagged for review' : 'Pending',
        organization: {
          connect: {
            id: organization.id
          }
        }
      }
    });

    console.log(`Created AML check for ${organization.name}`);
  }

  // Create KYC verifications for users
  for (const userGroup of users) {
    for (const user of userGroup) {
      // Skip users without an organization
      if (!user.organizationId) continue;

      // Find the organization
      const organization = organizations.find(org => org.id === user.organizationId);
      if (!organization) continue;

      // Check if KYC verification already exists
      const existingKyc = await prisma.kycVerification.findFirst({
        where: { userId: user.id }
      });

      if (existingKyc) {
        console.log(`KYC verification already exists for ${user.name}. Skipping creation.`);
        continue;
      }

      // Determine KYC level based on user role and organization status
      let kycLevel = "NONE";
      let status = "PENDING";

      if (organization.status === 'ACTIVE') {
        if (user.role === 'ORGANIZATION_ADMIN') {
          kycLevel = Math.random() > 0.2 ? "ADVANCED" : "INTERMEDIATE";
          status = "APPROVED";
        } else {
          kycLevel = Math.random() > 0.5 ? "INTERMEDIATE" : "BASIC";
          status = Math.random() > 0.7 ? "APPROVED" : "IN_REVIEW";
        }
      }

      // Create KYC verification
      const kyc = await prisma.kycVerification.create({
        data: {
          status,
          level: kycLevel,
          lastChecked: new Date(),
          expiresAt: status === "APPROVED" ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) : null,
          verifier: status === "APPROVED" ? ['Jumio', 'Onfido', 'Sumsub', 'Veriff'][Math.floor(Math.random() * 4)] : null,
          additionalInfo: status === "APPROVED" ? {
            verificationId: `VID-${Math.floor(Math.random() * 1000000)}`,
            riskScore: Math.floor(Math.random() * 30),
            riskLevel: "LOW"
          } : null,
          // Removed notes field as it doesn't exist in the schema
          user: {
            connect: {
              id: user.id
            }
          }
        }
      });

      console.log(`Created KYC verification for ${user.name} with level ${kycLevel}`);

      // Create KYC verification history
      await prisma.kycVerificationHistory.create({
        data: {
          status,
          timestamp: new Date(),
          notes: status === "APPROVED" ? 'Initial verification completed' :
                status === "IN_REVIEW" ? 'Verification in progress' : 'Verification pending',
          kycVerification: {
            connect: {
              id: kyc.id
            }
          }
        }
      });

      // Create compliance documents for the user
      if (status !== "PENDING") {
        const docTypes = ['IDENTITY', 'ADDRESS_PROOF', 'BUSINESS_REGISTRATION', 'TAX_CERTIFICATE'];
        const docStatuses = status === "APPROVED" ? ["APPROVED"] : ["IN_REVIEW", "PENDING"];

        for (const docType of docTypes) {
          const docStatus = docStatuses[Math.floor(Math.random() * docStatuses.length)];

          await prisma.complianceDocument.create({
            data: {
              type: docType,
              name: `${docType.replace(/_/g, ' ')} - ${user.name}`,
              url: `https://example.com/documents/${user.id}/${docType.toLowerCase()}.pdf`,
              status: docStatus,
              notes: docStatus === "IN_REVIEW" ? 'Under review by compliance team' : null,
              user: {
                connect: {
                  id: user.id
                }
              },
              organization: {
                connect: {
                  id: organization.id
                }
              }
            }
          });

          console.log(`Created compliance document ${docType} for ${user.name}`);
        }
      }

      // Create AML check for the user
      await prisma.amlCheck.create({
        data: {
          status: status,
          riskLevel: status === "APPROVED" ? "LOW" :
                    status === "IN_REVIEW" ? "MEDIUM" : "HIGH",
          lastChecked: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000),
          expiresAt: status === "APPROVED" ? new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) : null,
          checkMethod: 'THIRD_PARTY',
          checkProvider: ['Chainalysis', 'Elliptic', 'Coinfirm', 'ComplyAdvantage'][Math.floor(Math.random() * 4)],
          referenceId: `AML-${Math.floor(Math.random() * 1000000)}`,
          findings: {
            riskScore: status === "APPROVED" ? Math.floor(Math.random() * 30) : Math.floor(Math.random() * 30) + 30,
            matches: [],
            warnings: status === "IN_REVIEW" ? ['Incomplete information'] : [],
            errors: [],
            checkDate: new Date().toISOString()
          },
          actionTaken: status === "APPROVED" ? 'Approved' :
                      status === "IN_REVIEW" ? 'Flagged for review' : 'Pending',
          user: {
            connect: {
              id: user.id
            }
          }
        }
      });

      console.log(`Created AML check for ${user.name}`);

      // Create compliance checks (50% chance)
      if (Math.random() > 0.5) {
        const checkCount = Math.floor(Math.random() * 3) + 1; // 1-3 checks

        for (let i = 0; i < checkCount; i++) {
          const checkTypes = ['KYC', 'AML', 'TRANSACTION_SCREENING', 'SANCTIONS_CHECK', 'PEP_CHECK'];
          const checkType = checkTypes[Math.floor(Math.random() * checkTypes.length)];

          await prisma.complianceCheck.create({
            data: {
              type: checkType,
              result: Math.random() > 0.8 ? "MANUAL_REVIEW" : "PASS",
              riskLevel: Math.random() > 0.8 ? "MEDIUM" : "LOW",
              details: {
                checkDate: new Date().toISOString(),
                provider: ['Chainalysis', 'Elliptic', 'Coinfirm', 'ComplyAdvantage'][Math.floor(Math.random() * 4)],
                referenceId: `CHK-${Math.floor(Math.random() * 1000000)}`
              },
              user: {
                connect: {
                  id: user.id
                }
              },
              organization: {
                connect: {
                  id: organization.id
                }
              }
            }
          });

          console.log(`Created compliance check ${checkType} for ${user.name}`);
        }
      }

      // Create tax reports (30% chance)
      if (Math.random() > 0.7) {
        const reportYear = new Date().getFullYear() - 1;

        await prisma.taxReport.create({
          data: {
            year: reportYear,
            reportType: 'ANNUAL',
            status: 'GENERATED',
            generatedAt: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000),
            downloadUrl: `https://example.com/tax-reports/${user.id}/${reportYear}.pdf`,
            data: {
              totalTransactions: Math.floor(Math.random() * 50) + 10,
              totalVolume: Math.floor(Math.random() * 100000) + 10000,
              totalFees: Math.floor(Math.random() * 5000) + 500,
              totalTaxableGain: Math.floor(Math.random() * 20000) + 1000
            },
            user: {
              connect: {
                id: user.id
              }
            }
          }
        });

        console.log(`Created tax report for ${user.name} for year ${reportYear}`);
      }
    }
  }

  // Create compliance reports
  const reportCount = Math.floor(Math.random() * 5) + 5; // 5-9 reports

  for (let i = 0; i < reportCount; i++) {
    const reportTypes = ['MONTHLY_COMPLIANCE_SUMMARY', 'QUARTERLY_AML_REPORT', 'ANNUAL_COMPLIANCE_REVIEW', 'RISK_ASSESSMENT', 'SUSPICIOUS_ACTIVITY_REPORT'];
    const reportType = reportTypes[i % reportTypes.length];

    // Find a random admin user
    const adminUsers = users.flat().filter(user => user.role === 'ADMIN');
    if (adminUsers.length === 0) continue;

    const adminUser = adminUsers[Math.floor(Math.random() * adminUsers.length)];

    // Create report date (within the last year)
    const reportDate = new Date();
    reportDate.setMonth(reportDate.getMonth() - (i + 1));

    await prisma.complianceReport.create({
      data: {
        title: `${reportType.replace(/_/g, ' ')} - ${reportDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}`,
        type: reportType,
        status: 'COMPLETED',
        generatedAt: reportDate,
        content: {
          summary: 'This report provides an overview of compliance activities and findings.',
          keyFindings: [
            'All KYC procedures were followed correctly',
            'No major compliance issues were identified',
            'Minor improvements recommended for documentation process'
          ],
          recommendations: [
            'Continue monitoring high-risk customers',
            'Update compliance training materials',
            'Enhance documentation verification process'
          ],
          statistics: {
            totalUsers: Math.floor(Math.random() * 100) + 50,
            verifiedUsers: Math.floor(Math.random() * 80) + 20,
            pendingVerifications: Math.floor(Math.random() * 20) + 5,
            rejectedVerifications: Math.floor(Math.random() * 5),
            highRiskCustomers: Math.floor(Math.random() * 10),
            suspiciousActivities: Math.floor(Math.random() * 3)
          }
        },
        user: {
          connect: {
            id: adminUser.id
          }
        }
      }
    });

    console.log(`Created compliance report: ${reportType} for ${reportDate.toLocaleDateString()}`);
  }

    return true;
  } catch (error) {
    console.error('Error in seedCompliance:', error);
    return false;
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without organizations and users, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
