import { db as prisma } from '../../src/lib/db';

// Project data with various types and statuses
const projectData = [
  // Renewable Energy Projects
  {
    name: 'Solar Power Plant - Arizona',
    description: 'Large-scale solar power plant in Arizona desert generating clean energy',
    type: 'RENEWABLE_ENERGY',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2021-03-15'),
    endDate: new Date('2041-03-15'),
    location: 'Arizona Desert',
    country: 'United States',
    coordinates: '33.4484° N, 112.0740° W',
    area: 2500.5,
    externalProjectId: 'SPP-AZ-001',
    registryId: 'VCS-RE-12345',
    standard: 'Verra',
    methodology: 'VM0042',
    estimatedReductions: 75000,
    validator: 'ClimateCheck Validation',
    verifier: 'EcoVerify Inc.',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/spp-az-001-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Validation Report',
        type: "VALIDATION_REPORT",
        url: 'https://example.com/documents/spp-az-001-validation.pdf',
        status: "APPROVED"
      }
    ]
  },
  {
    name: 'Wind Farm Project - North Sea',
    description: 'Offshore wind farm in the North Sea providing renewable energy to coastal communities',
    type: 'RENEWABLE_ENERGY',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2020-06-10'),
    endDate: new Date('2040-06-10'),
    location: 'North Sea',
    country: 'United Kingdom',
    coordinates: '55.5° N, 1.5° E',
    area: 3800.0,
    externalProjectId: 'WFP-NS-002',
    registryId: 'GS-RE-67890',
    standard: 'Gold Standard',
    methodology: 'GS-RE-01',
    estimatedReductions: 120000,
    validator: 'GreenCert Validation',
    verifier: 'ClimateAudit Ltd.',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/wfp-ns-002-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Environmental Impact Assessment',
        type: "ENVIRONMENTAL_IMPACT",
        url: 'https://example.com/documents/wfp-ns-002-eia.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Forestry Projects
  {
    name: 'Amazon Rainforest Conservation',
    description: 'Conservation of primary rainforest in the Amazon basin to prevent deforestation',
    type: 'FORESTRY',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2019-01-20'),
    endDate: new Date('2049-01-20'),
    location: 'Amazon Basin',
    country: 'Brazil',
    coordinates: '3.4653° S, 62.2159° W',
    area: 50000.0,
    externalProjectId: 'ARC-BR-003',
    registryId: 'VCS-FOR-23456',
    standard: 'Verra',
    methodology: 'VM0015',
    estimatedReductions: 200000,
    validator: 'RainforestCert',
    verifier: 'Global Forest Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/arc-br-003-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Stakeholder Consultation Report',
        type: "STAKEHOLDER_CONSULTATION",
        url: 'https://example.com/documents/arc-br-003-stakeholder.pdf',
        status: "APPROVED"
      }
    ]
  },
  {
    name: 'Reforestation Project - Kenya',
    description: 'Reforestation of degraded lands in Kenya with native tree species',
    type: 'FORESTRY',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2020-03-05'),
    endDate: new Date('2050-03-05'),
    location: 'Central Highlands',
    country: 'Kenya',
    coordinates: '0.4252° S, 36.7517° E',
    area: 12000.0,
    externalProjectId: 'RPK-KE-004',
    registryId: 'GS-FOR-34567',
    standard: 'Gold Standard',
    methodology: 'GS-FOR-02',
    estimatedReductions: 45000,
    validator: 'AfriCert',
    verifier: 'EcoStandard East Africa',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/rpk-ke-004-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Monitoring Plan',
        type: "MONITORING_PLAN",
        url: 'https://example.com/documents/rpk-ke-004-monitoring.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Methane Reduction Projects
  {
    name: 'Landfill Gas Capture - California',
    description: 'Capture and utilization of methane gas from landfill for energy generation',
    type: 'METHANE_REDUCTION',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2021-05-12'),
    endDate: new Date('2036-05-12'),
    location: 'Southern California',
    country: 'United States',
    coordinates: '34.0522° N, 118.2437° W',
    area: 150.0,
    externalProjectId: 'LGC-CA-005',
    registryId: 'CAR-MR-45678',
    standard: 'Climate Action Reserve',
    methodology: 'CAR-LFG-01',
    estimatedReductions: 80000,
    validator: 'WestCert',
    verifier: 'ClimateCheck Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/lgc-ca-005-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Verification Report',
        type: "VERIFICATION_REPORT",
        url: 'https://example.com/documents/lgc-ca-005-verification.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Energy Efficiency Projects
  {
    name: 'Industrial Energy Efficiency - Germany',
    description: 'Implementation of energy efficiency measures in manufacturing facilities',
    type: 'ENERGY_EFFICIENCY',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2022-01-15'),
    endDate: new Date('2032-01-15'),
    location: 'Ruhr Region',
    country: 'Germany',
    coordinates: '51.5167° N, 7.4500° E',
    area: 75.0,
    externalProjectId: 'IEE-DE-006',
    registryId: 'GS-EE-56789',
    standard: 'Gold Standard',
    methodology: 'GS-EE-01',
    estimatedReductions: 35000,
    validator: 'EuroCert',
    verifier: 'TÜV Rheinland',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/iee-de-006-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Baseline Assessment',
        type: "BASELINE_ASSESSMENT",
        url: 'https://example.com/documents/iee-de-006-baseline.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Waste Management Projects
  {
    name: 'Waste-to-Energy Facility - Singapore',
    description: 'Conversion of municipal solid waste to energy, reducing methane emissions and fossil fuel use',
    type: 'WASTE_MANAGEMENT',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2021-08-20'),
    endDate: new Date('2041-08-20'),
    location: 'Tuas',
    country: 'Singapore',
    coordinates: '1.3521° N, 103.8198° E',
    area: 25.0,
    externalProjectId: 'WTE-SG-007',
    registryId: 'VCS-WM-67890',
    standard: 'Verra',
    methodology: 'VM0018',
    estimatedReductions: 60000,
    validator: 'AsiaCert',
    verifier: 'SGS Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/wte-sg-007-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Environmental Impact Assessment',
        type: "ENVIRONMENTAL_IMPACT",
        url: 'https://example.com/documents/wte-sg-007-eia.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Agriculture Projects
  {
    name: 'Sustainable Rice Farming - Vietnam',
    description: 'Implementation of sustainable rice cultivation practices to reduce methane emissions',
    type: 'AGRICULTURE',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2020-04-10'),
    endDate: new Date('2030-04-10'),
    location: 'Mekong Delta',
    country: 'Vietnam',
    coordinates: '10.0341° N, 105.7749° E',
    area: 5000.0,
    externalProjectId: 'SRF-VN-008',
    registryId: 'VCS-AG-78901',
    standard: 'Verra',
    methodology: 'VM0033',
    estimatedReductions: 25000,
    validator: 'MekongCert',
    verifier: 'Asia Carbon Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/srf-vn-008-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Methodology Description',
        type: "METHODOLOGY",
        url: 'https://example.com/documents/srf-vn-008-methodology.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Transportation Projects
  {
    name: 'Electric Bus Fleet - Colombia',
    description: 'Replacement of diesel buses with electric buses in public transportation',
    type: 'TRANSPORTATION',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2021-11-05'),
    endDate: new Date('2031-11-05'),
    location: 'Bogotá',
    country: 'Colombia',
    coordinates: '4.7110° N, 74.0721° W',
    area: 0.0, // Not applicable for transportation
    externalProjectId: 'EBF-CO-009',
    registryId: 'GS-TR-89012',
    standard: 'Gold Standard',
    methodology: 'GS-TR-01',
    estimatedReductions: 40000,
    validator: 'LatinCert',
    verifier: 'TransportVerify',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/ebf-co-009-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Monitoring Plan',
        type: "MONITORING_PLAN",
        url: 'https://example.com/documents/ebf-co-009-monitoring.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Industrial Projects
  {
    name: 'Industrial Process Improvement - China',
    description: 'Implementation of advanced technologies to reduce emissions in cement production',
    type: 'INDUSTRIAL',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2021-02-25'),
    endDate: new Date('2031-02-25'),
    location: 'Hebei Province',
    country: 'China',
    coordinates: '38.0428° N, 114.5149° E',
    area: 100.0,
    externalProjectId: 'IPI-CN-010',
    registryId: 'VCS-IN-90123',
    standard: 'Verra',
    methodology: 'VM0024',
    estimatedReductions: 150000,
    validator: 'ChinaCert',
    verifier: 'Industrial Carbon Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/ipi-cn-010-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Verification Report',
        type: "VERIFICATION_REPORT",
        url: 'https://example.com/documents/ipi-cn-010-verification.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Other Projects
  {
    name: 'Blue Carbon - Mangrove Restoration',
    description: 'Restoration of mangrove ecosystems for carbon sequestration and coastal protection',
    type: 'OTHER',
    status: "ACTIVE",
    verificationStatus: "VERIFIED",
    startDate: new Date('2020-09-15'),
    endDate: new Date('2050-09-15'),
    location: 'Coastal Region',
    country: 'Indonesia',
    coordinates: '0.7893° S, 113.9213° E',
    area: 2000.0,
    externalProjectId: 'BCM-ID-011',
    registryId: 'VCS-BC-01234',
    standard: 'Verra',
    methodology: 'VM0033',
    estimatedReductions: 30000,
    validator: 'CoastalCert',
    verifier: 'Blue Carbon Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/bcm-id-011-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Social Impact Assessment',
        type: "SOCIAL_IMPACT",
        url: 'https://example.com/documents/bcm-id-011-social.pdf',
        status: "APPROVED"
      }
    ]
  },

  // Projects with different statuses
  {
    name: 'Geothermal Power Plant - Iceland',
    description: 'Development of geothermal power plant for clean energy generation',
    type: 'RENEWABLE_ENERGY',
    status: "PENDING",
    verificationStatus: "PENDING",
    startDate: new Date('2023-06-01'),
    endDate: new Date('2043-06-01'),
    location: 'Reykjanes Peninsula',
    country: 'Iceland',
    coordinates: '63.8333° N, 22.6667° W',
    area: 50.0,
    externalProjectId: 'GPP-IS-012',
    registryId: 'Pending',
    standard: 'Verra',
    methodology: 'VM0042',
    estimatedReductions: 90000,
    validator: 'NordicCert',
    verifier: 'Pending',
    documents: [
      {
        name: 'Project Design Document - Draft',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/gpp-is-012-pdd-draft.pdf',
        status: "PENDING"
      }
    ]
  },
  {
    name: 'Afforestation Project - Australia',
    description: 'Large-scale tree planting on degraded farmland',
    type: 'FORESTRY',
    status: "COMPLETED",
    verificationStatus: "VERIFIED",
    startDate: new Date('2015-03-10'),
    endDate: new Date('2022-03-10'),
    location: 'Victoria',
    country: 'Australia',
    coordinates: '36.8333° S, 145.0000° E',
    area: 8000.0,
    externalProjectId: 'AFP-AU-013',
    registryId: 'ACR-FOR-12345',
    standard: 'American Carbon Registry',
    methodology: 'ACR-FOR-01',
    estimatedReductions: 40000,
    validator: 'AusCert',
    verifier: 'Pacific Verification',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/afp-au-013-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Final Verification Report',
        type: "VERIFICATION_REPORT",
        url: 'https://example.com/documents/afp-au-013-final-verification.pdf',
        status: "APPROVED"
      }
    ]
  },
  {
    name: 'Biogas from Agricultural Waste',
    description: 'Collection and utilization of biogas from agricultural waste',
    type: 'METHANE_REDUCTION',
    status: "SUSPENDED",
    verificationStatus: "IN_REVIEW",
    startDate: new Date('2020-07-15'),
    endDate: new Date('2030-07-15'),
    location: 'Punjab',
    country: 'India',
    coordinates: '31.1471° N, 75.3412° E',
    area: 200.0,
    externalProjectId: 'BAW-IN-014',
    registryId: 'GS-MR-23456',
    standard: 'Gold Standard',
    methodology: 'GS-MR-01',
    estimatedReductions: 20000,
    validator: 'IndiaCert',
    verifier: 'Under Review',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/baw-in-014-pdd.pdf',
        status: "APPROVED"
      },
      {
        name: 'Compliance Review',
        type: "LEGAL_DOCUMENT",
        url: 'https://example.com/documents/baw-in-014-compliance.pdf',
        status: "PENDING"
      }
    ]
  },
  {
    name: 'Urban Forestry Initiative - New York',
    description: 'Planting and maintenance of trees in urban areas to increase carbon sequestration',
    type: 'FORESTRY',
    status: "CANCELLED",
    verificationStatus: "REJECTED",
    startDate: new Date('2021-04-01'),
    endDate: new Date('2031-04-01'),
    location: 'New York City',
    country: 'United States',
    coordinates: '40.7128° N, 74.0060° W',
    area: 500.0,
    externalProjectId: 'UFI-NY-015',
    registryId: 'Cancelled',
    standard: 'Climate Action Reserve',
    methodology: 'CAR-UF-01',
    estimatedReductions: 5000,
    validator: 'UrbanCert',
    verifier: 'Rejected',
    documents: [
      {
        name: 'Project Design Document',
        type: "PROJECT_DESIGN",
        url: 'https://example.com/documents/ufi-ny-015-pdd.pdf',
        status: "REJECTED"
      },
      {
        name: 'Rejection Report',
        type: "OTHER",
        url: 'https://example.com/documents/ufi-ny-015-rejection.pdf',
        status: "APPROVED"
      }
    ]
  }
];

// Function to seed projects
export async function seedProjects(organizations: any[]) {
  console.log('Starting to seed projects...');

  // Debug information
  console.log(`Received organizations: ${organizations ? organizations.length : 0}`);
  if (organizations && organizations.length > 0) {
    console.log(`First organization: ${organizations[0].name} (${organizations[0].id})`);
  }

  const projects = [];

  // Check if we have enough organizations
  if (!organizations || organizations.length < 2) {
    console.log('Not enough organizations to seed projects. Need at least 2 organizations.');
    return projects;
  }

  try {
    // Distribute projects among organizations
    for (let i = 0; i < projectData.length; i++) {
      try {
        // Assign to organizations in a round-robin fashion, skipping the platform admin org
        const orgIndex = i % (organizations.length - 1);
        const organization = organizations[orgIndex];

        // Skip if organization is undefined
        if (!organization) {
          console.log(`Organization at index ${orgIndex} is undefined. Skipping project ${projectData[i].name}.`);
          continue;
        }

        console.log(`Creating/checking project ${projectData[i].name} for organization ${organization.name} (${organization.id})`);

        // Check if project already exists
        const existingProject = await prisma.project.findFirst({
          where: {
            name: projectData[i].name,
            organizationId: organization.id
          }
        });

        if (existingProject) {
          console.log(`Project ${projectData[i].name} already exists for ${organization.name}. Skipping creation.`);
          projects.push(existingProject);
          continue;
        }

        // Extract documents from project data
        const { documents, ...projectInfo } = projectData[i];

        // Create project
        const newProject = await prisma.project.create({
          data: {
            ...projectInfo,
            organization: {
              connect: {
                id: organization.id
              }
            }
          }
        });

        console.log(`Created project: ${newProject.name} (${newProject.id}) for ${organization.name}`);
        projects.push(newProject);

        // Create project documents
        if (documents && documents.length > 0) {
          for (const doc of documents) {
            await prisma.projectDocument.create({
              data: {
                ...doc,
                project: {
                  connect: {
                    id: newProject.id
                  }
                }
              }
            });
            console.log(`Added document: ${doc.name} to project ${newProject.name}`);
          }
        }

        // Create project verification history
        await prisma.projectVerification.create({
          data: {
            status: newProject.verificationStatus,
            verifier: 'Verification Authority',
            verifierEmail: '<EMAIL>',
            notes: 'Initial verification',
            project: {
              connect: {
                id: newProject.id
              }
            }
          }
        });

        // Create project financial metrics
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 3); // 3 months ago
        const endDate = new Date(); // Today

        await prisma.projectFinancialMetric.create({
          data: {
            metricType: 'TRANSACTION_VOLUME',
            name: 'Quarterly Transaction Volume',
            value: Math.random() * 1000000,
            previousValue: Math.random() * 800000,
            changePercent: Math.random() * 20 - 10, // -10% to +10%
            currency: 'USD',
            period: 'quarterly',
            startDate,
            endDate,
            target: Math.random() * 1200000,
            status: Math.random() > 0.5 ? 'ON_TARGET' : 'BELOW_TARGET',
            notes: 'Quarterly financial metrics',
            project: {
              connect: {
                id: newProject.id
              }
            }
          }
        });
      } catch (projectError) {
        console.error(`Error creating project ${projectData[i].name}:`, projectError);
        // Continue with the next project
      }
    }

    return projects;
  } catch (error) {
    console.error('Error in seedProjects:', error);
    return projects;
  }
}

// For direct execution of this script
if (require.main === module) {
  // This will fail without organizations, so this script should be called from the master script
  console.error('This script should be called from the master seed script.');
  process.exit(1);
}
