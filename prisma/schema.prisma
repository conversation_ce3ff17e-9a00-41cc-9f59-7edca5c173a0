// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id                     String                  @id @default(cuid())
  email                  String                  @unique
  name                   String?
  password               String?
  role                   UserRole                @default(USER)
  emailVerified          DateTime? // Track if email is verified
  jobTitle               String? // Job title in the organization
  departmentName         String? // Legacy department name (for backward compatibility)
  phoneNumber            String? // Contact phone number
  profileImage           String? // URL to profile image
  bio                    String? // Short bio or description
  lastLoginAt            DateTime? // Last login timestamp
  twoFactorEnabled       Boolean                 @default(false) // Whether 2FA is enabled
  preferences            Json? // User preferences as JSON
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  organization           Organization?           @relation(fields: [organizationId], references: [id])
  organizationId         String?
  wallets                Wallet[]
  carbonCredits          CarbonCredit[]
  retirements            Retirement[]
  tokenizations          Tokenization[]
  buyOrders              Order[]                 @relation("BuyerOrders")
  sellOrders             Order[]                 @relation("SellerOrders")
  notifications          Notification[]
  resetTokens            PasswordResetToken[]
  verificationTokens     VerificationToken[]
  teamMemberships        TeamMember[] // Teams the user belongs to
  auditLogs              AuditLog[] // Audit logs created by this user
  notificationPreference NotificationPreference?
  kycVerification        KycVerification?
  amlCheck               AmlCheck?
  complianceDocuments    ComplianceDocument[]
  complianceChecks       ComplianceCheck[]
  taxReports             TaxReport[]
  onboardingState        OnboardingState?
  customRoles            UserCustomRole[] // Custom roles assigned to the user
  permissionGrants       PermissionGrant[] // Direct permission grants
  temporaryPermissions   TemporaryPermission[] // Temporary permissions
  permissionRequests     PermissionRequest[] // Permission requests made by the user
  permissionApprovals    PermissionRequest[]     @relation("Approver") // Permission requests approved by the user
  department             Department?             @relation("UserDepartment", fields: [departmentId], references: [id])
  departmentId           String?
  division               Division?               @relation("UserDivision", fields: [divisionId], references: [id])
  divisionId             String?
  complianceReports      ComplianceReport[]
  MarketplaceListing     MarketplaceListing[]
  MarketplaceWatchlist   MarketplaceWatchlist[]
  organizationDraft      OrganizationDraft?
}

// Add OnboardingState model and relation to User model

model OnboardingState {
  id             String   @id @default(cuid())
  userId         String   @unique
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentStep    String   @default("organization_details")
  organizationId String?
  skippedSteps   Json     @default("[]") // Array of steps that were skipped
  completedSteps Json     @default("[]") // Array of steps that were completed
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([userId])
}

// Organization Draft model for saving organization creation progress
model OrganizationDraft {
  id                 String            @id @default(cuid())
  userId             String            @unique
  user               User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  name               String?
  description        String?
  website            String?
  industry           String?
  size               OrganizationSize?
  legalName          String?
  registrationNumber String?
  taxId              String?
  country            String?
  address            String?
  city               String?
  state              String?
  postalCode         String?
  phoneNumber        String?
  email              String?
  foundedYear        Int?
  currentStep        Int?              @default(1)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt

  @@index([userId])
}

enum UserRole {
  ADMIN
  ORGANIZATION_ADMIN
  USER
}

// Organization model
model Organization {
  id                    String                    @id @default(cuid())
  name                  String
  description           String?
  website               String?
  logo                  String?
  status                OrganizationStatus        @default(PENDING)
  verificationStatus    VerificationStatus        @default(PENDING)
  legalName             String? // Legal registered name
  registrationNumber    String? // Business registration number
  taxId                 String? // Tax ID or VAT number
  country               String? // Country of registration
  address               String? // Physical address
  city                  String?
  state                 String?
  postalCode            String?
  phoneNumber           String?
  industry              String? // Industry sector
  size                  OrganizationSize? // Size of the organization
  foundedYear           Int? // Year the organization was founded
  primaryContact        String? // Primary contact person
  primaryContactEmail   String? // Primary contact email
  primaryContactPhone   String? // Primary contact phone
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  users                 User[]
  projects              Project[] // Projects owned by the organization
  wallets               Wallet[]
  carbonCredits         CarbonCredit[]
  retirements           Retirement[]
  tokenizations         Tokenization[]
  subscription          Subscription?
  transactionFeeRate    Float                     @default(0.01) // 1% default
  listingFeeRate        Float                     @default(0.005) // 0.5% default
  subscriptionFee       Float                     @default(0.0) // Subscription fee
  invitations           Invitation[]
  teams                 Team[]
  documents             Document[]
  paymentMethods        PaymentMethod[]
  billingHistory        BillingRecord[]
  auditLogs             AuditLog[]
  notifications         Notification[]
  kycVerification       KycVerification?
  amlCheck              AmlCheck?
  complianceDocuments   ComplianceDocument[]
  complianceChecks      ComplianceCheck[]
  taxReports            TaxReport[]
  customRoles           CustomRole[] // Custom roles for this organization
  departments           Department[] // Departments for enterprise organizations
  divisions             Division[] // Divisions for enterprise organizations
  rbacSettings          OrganizationRbacSettings? // RBAC settings for this organization
  feeHistory            OrganizationFeeHistory[] // History of fee changes
  financialMetrics      FinancialMetric[] // Financial metrics
  financialReports      FinancialReport[] // Financial reports
  periodComparisons     PeriodComparison[] // Period comparisons
  complianceReports     ComplianceReport[]
  marketplaceWatchlists MarketplaceWatchlist[] // Marketplace watchlists
  MarketplaceListing    MarketplaceListing[]
}

// Organization Fee History model
model OrganizationFeeHistory {
  id                 String       @id @default(cuid())
  listingFeeRate     Float
  transactionFeeRate Float
  subscriptionFee    Float
  effectiveFrom      DateTime
  notes              String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  organization       Organization @relation(fields: [organizationId], references: [id])
  organizationId     String
}

enum OrganizationStatus {
  PENDING
  ACTIVE
  SUSPENDED
  INACTIVE
}

enum VerificationStatus {
  PENDING
  IN_REVIEW
  VERIFIED
  REJECTED
}

enum OrganizationSize {
  SMALL // <50 employees
  MEDIUM // 50-250 employees
  LARGE // 251-1000 employees
  ENTERPRISE // >1000 employees
}

// Project model
model Project {
  id                  String                   @id @default(cuid())
  name                String
  description         String?
  type                ProjectType
  status              ProjectStatus            @default(PENDING)
  verificationStatus  VerificationStatus       @default(PENDING)
  startDate           DateTime?
  endDate             DateTime?
  location            String? // Geographic location
  country             String?
  coordinates         String? // GPS coordinates
  area                Float? // Area in hectares or square kilometers
  externalProjectId   String? // External project ID
  registryId          String? // Registry ID
  standard            String? // e.g., "Verra", "Gold Standard"
  methodology         String? // e.g., "VM0015", "AMS-III.D"
  methodologyVersion  String? // Version of the methodology
  estimatedReductions Float? // Estimated emissions reductions
  actualReductions    Float? // Actual emissions reductions
  verifier            String? // Verification body
  validator           String? // Validation body
  images              String[] // URLs to project images
  documents           ProjectDocument[] // Project documents
  budget              Float? // Project budget
  roi                 Float? // Return on investment
  sdgs                String[] // Sustainable Development Goals
  tags                String[] // Tags for categorization
  metadata            Json? // Additional metadata
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt
  organization        Organization             @relation(fields: [organizationId], references: [id])
  organizationId      String
  carbonCredits       CarbonCredit[] // Carbon credits generated by this project
  wallets             Wallet[] // Wallets associated with this project
  verificationHistory ProjectVerification[] // Verification history
  financialMetrics    ProjectFinancialMetric[] // Financial metrics
  complianceDocuments ComplianceDocument[] // Compliance documents
}

enum ProjectType {
  RENEWABLE_ENERGY
  FORESTRY
  METHANE_REDUCTION
  ENERGY_EFFICIENCY
  WASTE_MANAGEMENT
  AGRICULTURE
  TRANSPORTATION
  INDUSTRIAL
  OTHER
}

enum ProjectStatus {
  PENDING
  ACTIVE
  COMPLETED
  SUSPENDED
  CANCELLED
}

// Project Document model
model ProjectDocument {
  id        String              @id @default(cuid())
  name      String
  type      ProjectDocumentType
  url       String
  status    DocumentStatus      @default(PENDING)
  notes     String?
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  project   Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
}

enum ProjectDocumentType {
  PROJECT_DESIGN
  METHODOLOGY
  BASELINE_ASSESSMENT
  MONITORING_PLAN
  VALIDATION_REPORT
  VERIFICATION_REPORT
  LEGAL_DOCUMENT
  STAKEHOLDER_CONSULTATION
  ENVIRONMENTAL_IMPACT
  SOCIAL_IMPACT
  FINANCIAL_DOCUMENT
  OTHER
}

// Project Verification model
model ProjectVerification {
  id            String             @id @default(cuid())
  status        VerificationStatus
  verifier      String? // Name of the verifier
  verifierEmail String? // Email of the verifier
  notes         String?
  timestamp     DateTime           @default(now())
  metadata      Json?
  project       Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     String
}

// Project Financial Metric model
model ProjectFinancialMetric {
  id            String              @id @default(cuid())
  metricType    FinancialMetricType
  name          String
  value         Float
  previousValue Float?
  changePercent Float?
  currency      String              @default("USD")
  period        String // e.g., "daily", "weekly", "monthly", "quarterly", "yearly"
  startDate     DateTime
  endDate       DateTime
  target        Float? // Target value if applicable
  status        MetricStatus? // Status compared to target
  notes         String?
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  project       Project             @relation(fields: [projectId], references: [id])
  projectId     String
}

// Wallet model
model Wallet {
  id                    String                 @id @default(cuid())
  name                  String? // Friendly name for the wallet
  address               String
  network               String // Blockchain network (ethereum, polygon, etc.)
  chainId               Int // Chain ID of the network
  isTestnet             Boolean                @default(true) // Whether this is a testnet wallet
  walletType            WalletType             @default(GENERAL) // Type of wallet (general, project-specific)
  purpose               String? // Purpose of the wallet (e.g., "tokenization", "trading")
  encryptedKey          String? // Encrypted private key
  isSmartWallet         Boolean                @default(false) // Whether this is a smart contract wallet
  smartAccountAddress   String? // Smart account address for smart wallets
  ownerAddress          String? // Owner address for smart wallets
  factoryAddress        String? // Factory address for smart wallets
  implementationAddress String? // Implementation address for smart wallets
  balance               Float                  @default(0)
  lastSyncedAt          DateTime? // Last time the balance was synced with the blockchain
  securityScore         Int? // Security health score (0-100)
  recoveryEnabled       Boolean                @default(false) // Whether recovery options are enabled
  recoveryType          WalletRecoveryType? // Type of recovery method
  recoveryData          Json? // Recovery-related data (encrypted)
  transactionLimitDaily Float? // Daily transaction limit
  transactionLimitPerTx Float? // Per-transaction limit
  requireApprovals      Boolean                @default(false) // Whether transactions require approvals
  approvalThreshold     Int? // Number of approvals required
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  user                  User?                  @relation(fields: [userId], references: [id])
  userId                String?
  organization          Organization?          @relation(fields: [organizationId], references: [id])
  organizationId        String?
  project               Project?               @relation(fields: [projectId], references: [id])
  projectId             String?
  transactions          Transaction[]
  tokens                Token[] // ERC-20 tokens in the wallet
  nfts                  NFT[] // NFTs in the wallet
  securitySettings      WalletSecuritySetting?
  accessControls        WalletAccessControl[] // Access control entries
  auditLogs             WalletAuditLog[] // Wallet-specific audit logs
  bridgeTransactions    BridgeTransaction[] // Cross-chain bridge transactions
  gasSettings           GasSetting? // Gas settings for this wallet
  tokenizations         Tokenization[] // Tokenizations performed with this wallet
  guardians             WalletGuardian[] // Guardians for social recovery
  recoveryRequests      WalletRecovery[] // Recovery requests

  @@unique([address, network, chainId]) // A wallet address is unique per network and chain
}

enum WalletType {
  GENERAL
  PROJECT
  TOKENIZATION
  TRADING
  RETIREMENT
  CUSTODY
}

enum WalletRecoveryType {
  SOCIAL_RECOVERY
  SEED_PHRASE
  HARDWARE_BACKUP
  MULTI_SIG
  GUARDIAN
}

// Wallet Security Settings model
model WalletSecuritySetting {
  id                       String    @id @default(cuid())
  twoFactorEnabled         Boolean   @default(false) // Whether 2FA is required for transactions
  twoFactorType            String? // Type of 2FA (app, SMS, etc.)
  whitelistedAddresses     Json? // List of whitelisted addresses
  blacklistedAddresses     Json? // List of blacklisted addresses
  delayedWithdrawals       Boolean   @default(false) // Whether withdrawals have a time delay
  withdrawalDelayHours     Int? // Hours to delay withdrawals
  notificationsEnabled     Boolean   @default(true) // Whether to send notifications for all transactions
  autoLockEnabled          Boolean   @default(false) // Whether to auto-lock the wallet after inactivity
  autoLockTimeoutMinutes   Int? // Minutes of inactivity before auto-lock
  spendingNotifications    Boolean   @default(true) // Whether to notify on spending
  unusualActivityDetection Boolean   @default(true) // Whether to detect unusual activity
  lastSecurityReview       DateTime?
  securityReviewFrequency  Int? // Days between security reviews
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  wallet                   Wallet    @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId                 String    @unique
}

// Wallet Access Control model
model WalletAccessControl {
  id           String            @id @default(cuid())
  wallet       Wallet            @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId     String
  userId       String // User who has access
  accessLevel  WalletAccessLevel
  canApprove   Boolean           @default(false) // Whether user can approve transactions
  canInitiate  Boolean           @default(false) // Whether user can initiate transactions
  canView      Boolean           @default(true) // Whether user can view wallet details
  customLimits Json? // Custom transaction limits for this user
  expiresAt    DateTime? // When this access expires (if temporary)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt

  @@unique([walletId, userId])
}

enum WalletAccessLevel {
  ADMIN
  MANAGER
  APPROVER
  VIEWER
}

// Wallet Audit Log model
model WalletAuditLog {
  id        String   @id @default(cuid())
  wallet    Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId  String
  action    String // Action performed
  userId    String? // User who performed the action
  ipAddress String? // IP address
  userAgent String? // User agent
  details   Json? // Additional details
  timestamp DateTime @default(now())
}

// Bridge Transaction model for cross-chain transfers
model BridgeTransaction {
  id                   String       @id @default(cuid())
  sourceWallet         Wallet       @relation(fields: [sourceWalletId], references: [id])
  sourceWalletId       String
  sourceNetwork        String
  sourceChainId        Int
  destinationAddress   String
  destinationNetwork   String
  destinationChainId   Int
  tokenAddress         String? // Token contract address (null for native token)
  tokenSymbol          String?
  amount               String // Amount as string to handle large numbers
  fee                  String? // Bridge fee
  status               BridgeStatus @default(PENDING)
  sourceTxHash         String? // Source transaction hash
  destinationTxHash    String? // Destination transaction hash
  errorMessage         String? // Error message if failed
  bridgeProvider       String // Bridge provider used (e.g., "Connext", "Hop", "Across")
  estimatedTimeMinutes Int? // Estimated completion time in minutes
  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @updatedAt
}

enum BridgeStatus {
  PENDING
  INITIATED
  IN_PROGRESS
  COMPLETED
  FAILED
  RECOVERY_NEEDED
}

// Gas Settings model
model GasSetting {
  id                    String   @id @default(cuid())
  wallet                Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId              String   @unique
  defaultGasPrice       Float? // Default gas price in Gwei
  maxGasPrice           Float? // Maximum gas price willing to pay
  defaultMaxPriorityFee Float? // Default max priority fee (EIP-1559)
  defaultMaxFeePerGas   Float? // Default max fee per gas (EIP-1559)
  gasLimitMultiplier    Float    @default(1.1) // Multiplier for estimated gas limit
  optimizationEnabled   Boolean  @default(true) // Whether gas optimization is enabled
  alertThreshold        Float? // Gas price threshold for alerts
  alertEnabled          Boolean  @default(false) // Whether gas price alerts are enabled
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

// Carbon Credit model
model CarbonCredit {
  id                    String                     @id @default(cuid())
  name                  String
  description           String?
  quantity              Float
  availableQuantity     Float // Quantity available for sale
  retiredQuantity       Float                      @default(0) // Quantity that has been retired
  price                 Float
  minPurchaseQuantity   Float? // Minimum purchase quantity
  vintage               Int // Year the credit was generated
  standard              String // e.g., "Verra", "Gold Standard"
  methodology           String // e.g., "Renewable Energy", "Forestry"
  location              String? // Geographic location of the project
  country               String? // Country of the project
  externalProjectId     String? // External project ID
  serialNumber          String? // Serial number of the credit
  certificationDate     DateTime? // Date of certification
  expirationDate        DateTime? // Expiration date if applicable
  verificationBody      String? // Organization that verified the credit
  status                CarbonCreditStatus         @default(PENDING)
  verificationStatus    VerificationStatus         @default(PENDING)
  listingDate           DateTime? // Date when listed on marketplace
  retirementDate        DateTime? // Date when retired (if applicable)
  retirementReason      String? // Reason for retirement
  retirementBeneficiary String? // Beneficiary of retirement
  images                String[] // URLs to project images
  documents             CarbonCreditDocument[] // Supporting documents
  complianceDocuments   ComplianceDocument[]       @relation("CarbonCreditCompliance") // Compliance-related documents
  tokenId               String? // Blockchain token ID if tokenized
  contractAddress       String? // Contract address if tokenized
  chainId               Int? // Chain ID if tokenized
  metadata              Json? // Additional metadata
  createdAt             DateTime                   @default(now())
  updatedAt             DateTime                   @updatedAt
  user                  User                       @relation(fields: [userId], references: [id])
  userId                String
  organization          Organization               @relation(fields: [organizationId], references: [id])
  organizationId        String
  project               Project                    @relation(fields: [projectId], references: [id])
  projectId             String
  orders                Order[]
  verificationHistory   CarbonCreditVerification[]
  priceHistory          CarbonCreditPrice[]
  retirements           Retirement[]
  tokenizations         Tokenization[]
  valuations            AssetValuation[]
  marketplaceListings   MarketplaceListing[] // Marketplace listings
  ComplianceDocument    ComplianceDocument[]
}

enum CarbonCreditStatus {
  PENDING
  VERIFIED
  LISTED
  SOLD
  RETIRED
  TOKENIZED
}

// Tokenization model for tracking carbon credit tokenization
model Tokenization {
  id                 String             @id @default(cuid())
  tokenId            String // Token ID
  amount             Float // Amount of carbon credits tokenized
  network            String // Blockchain network
  chainId            Int? // Chain ID
  contractAddress    String? // Contract address
  transactionHash    String? // Blockchain transaction hash
  status             TokenizationStatus @default(PENDING)
  tokenStandard      TokenStandard      @default(ERC20)
  tokenName          String?
  tokenSymbol        String?
  tokenDecimals      Int                @default(18)
  tokenizationMethod TokenizationMethod @default(STANDARD)
  metadata           Json? // Additional metadata
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt

  // Relations
  carbonCredit      CarbonCredit       @relation(fields: [carbonCreditId], references: [id])
  carbonCreditId    String
  organization      Organization       @relation(fields: [organizationId], references: [id])
  organizationId    String
  user              User               @relation(fields: [userId], references: [id])
  userId            String
  wallet            Wallet?            @relation(fields: [walletId], references: [id])
  walletId          String?
  tokenizationSteps TokenizationStep[]
}

enum TokenizationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum TokenStandard {
  ERC20
  ERC1155
  CUSTOM
}

enum TokenizationMethod {
  STANDARD
  BATCH
  CUSTOM
}

// Tokenization Step model
model TokenizationStep {
  id             String       @id @default(cuid())
  step           String // Step name
  status         String       @default("PENDING") // Status of the step
  details        Json? // Step details
  errorMessage   String? // Error message if failed
  startedAt      DateTime     @default(now())
  completedAt    DateTime?
  tokenization   Tokenization @relation(fields: [tokenizationId], references: [id], onDelete: Cascade)
  tokenizationId String
}

// Marketplace Listing model
model MarketplaceListing {
  id                  String            @id @default(cuid())
  title               String
  description         String?
  status              MarketplaceStatus @default(PENDING)
  quantity            Float
  availableQuantity   Float
  minPurchaseQuantity Float?
  price               Float?
  pricingStrategy     PricingStrategy   @default(FIXED)
  auctionEndTime      DateTime?
  auctionReservePrice Float?
  auctionMinIncrement Float?
  dynamicPricingRules Json?
  tieredPricingRules  Json?
  visibility          ListingVisibility @default(PUBLIC)
  featured            Boolean           @default(false)
  tags                String[]
  metadata            Json?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  user                User              @relation(fields: [userId], references: [id])
  userId              String
  organization        Organization      @relation(fields: [organizationId], references: [id])
  organizationId      String
  carbonCredit        CarbonCredit      @relation(fields: [carbonCreditId], references: [id])
  carbonCreditId      String
  orders              Order[]
  watchlistItems      WatchlistItem[]
}

enum MarketplaceStatus {
  PENDING
  ACTIVE
  PAUSED
  SOLD
  EXPIRED
  CANCELLED
}

enum PricingStrategy {
  FIXED
  AUCTION
  DYNAMIC
  TIERED
}

enum ListingVisibility {
  PUBLIC
  PRIVATE
  INVITE_ONLY
}

// Marketplace Watchlist model
model MarketplaceWatchlist {
  id             String          @id @default(cuid())
  name           String
  description    String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  user           User            @relation(fields: [userId], references: [id])
  userId         String
  organization   Organization?   @relation(fields: [organizationId], references: [id])
  organizationId String?
  watchlistItems WatchlistItem[]
}

// Watchlist Item model
model WatchlistItem {
  id                  String               @id @default(cuid())
  priceAlertEnabled   Boolean              @default(false)
  priceAlertThreshold Float?
  priceAlertDirection PriceAlertDirection?
  notes               String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  watchlist           MarketplaceWatchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  watchlistId         String
  listing             MarketplaceListing   @relation(fields: [listingId], references: [id], onDelete: Cascade)
  listingId           String
}

enum PriceAlertDirection {
  ABOVE
  BELOW
  BOTH
}

// Retirement model for tracking carbon credit retirements
model Retirement {
  id              String   @id @default(cuid())
  amount          Float // Amount of carbon credits retired
  reason          String? // Reason for retirement
  beneficiary     String? // Beneficiary of retirement
  tokenId         String? // Token ID if retired on-chain
  network         String? // Blockchain network
  chainId         Int? // Chain ID
  contractAddress String? // Contract address
  transactionHash String? // Blockchain transaction hash
  status          String   @default("COMPLETED") // Status of retirement
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  carbonCredit   CarbonCredit @relation(fields: [carbonCreditId], references: [id])
  carbonCreditId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  user           User         @relation(fields: [userId], references: [id])
  userId         String
}

// Order model
model Order {
  id                   String              @id @default(cuid())
  type                 OrderType
  quantity             Float
  price                Float
  status               OrderStatus         @default(PENDING)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  buyer                User                @relation("BuyerOrders", fields: [buyerId], references: [id])
  buyerId              String
  seller               User                @relation("SellerOrders", fields: [sellerId], references: [id])
  sellerId             String
  carbonCredit         CarbonCredit        @relation(fields: [carbonCreditId], references: [id])
  carbonCreditId       String
  transactions         Transaction[]
  MarketplaceListing   MarketplaceListing? @relation(fields: [marketplaceListingId], references: [id])
  marketplaceListingId String?
}

enum OrderType {
  BUY
  SELL
}

enum OrderStatus {
  PENDING
  MATCHED
  COMPLETED
  CANCELLED
}

// Transaction model
model Transaction {
  id                   String               @id @default(cuid())
  amount               Float
  fee                  Float
  gasPrice             Float? // Gas price in Gwei
  gasLimit             Int? // Gas limit for the transaction
  gasUsed              Int? // Actual gas used
  maxFeePerGas         Float? // Max fee per gas (EIP-1559)
  maxPriorityFeePerGas Float? // Max priority fee per gas (EIP-1559)
  type                 TransactionType
  status               TransactionStatus    @default(PENDING)
  transactionHash      String? // Blockchain transaction hash
  blockNumber          Int? // Block number where transaction was included
  network              String? // Blockchain network
  chainId              Int? // Chain ID of the network
  tokenAddress         String? // Token contract address (for token transfers)
  tokenSymbol          String? // Token symbol
  tokenDecimals        Int? // Token decimals
  counterpartyAddress  String? // Address of the counterparty
  counterpartyName     String? // Name of the counterparty if known
  purpose              String? // Purpose of the transaction
  category             TransactionCategory? // Category for financial reporting
  notes                String? // Additional notes
  tags                 String[] // Tags for categorization
  isRecurring          Boolean              @default(false) // Whether this is a recurring transaction
  recurringId          String? // ID to group recurring transactions
  createdAt            DateTime             @default(now())
  updatedAt            DateTime             @updatedAt
  wallet               Wallet               @relation(fields: [walletId], references: [id])
  walletId             String
  order                Order?               @relation(fields: [orderId], references: [id])
  orderId              String?
  transactionAudits    TransactionAudit[]   @relation("AuditTransaction")
  parentAudit          TransactionAudit?    @relation("ParentTransaction", fields: [transactionAuditId], references: [id], map: "Transaction_parentAudit_fkey")
  transactionAuditId   String?
  assetValuations      AssetValuation[]     @relation("TransactionValuation")
  parentValuation      AssetValuation?      @relation("ParentTransaction", fields: [assetValuationId], references: [id], map: "Transaction_parentValuation_fkey")
  assetValuationId     String?
  TransactionAudit     TransactionAudit?    @relation(fields: [transactionAuditId], references: [id])
  AssetValuation       AssetValuation?      @relation(fields: [assetValuationId], references: [id])
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  PURCHASE
  SALE
  FEE
  BRIDGE
  SWAP
  TRANSFER
  RETIREMENT
  TOKENIZATION
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REVERTED
}

enum TransactionCategory {
  INCOME
  EXPENSE
  INVESTMENT
  TRADING
  FEE
  TAX
  TRANSFER
  RETIREMENT
  TOKENIZATION
  OTHER
}

// Transaction Audit model
model TransactionAudit {
  id                String        @id @default(cuid())
  transaction       Transaction   @relation("AuditTransaction", fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId     String
  status            AuditStatus   @default(PENDING)
  verifiedBy        String? // User ID who verified the transaction
  verifiedAt        DateTime?
  notes             String?
  flagged           Boolean       @default(false)
  flagReason        String?
  flaggedBy         String? // User ID who flagged the transaction
  flaggedAt         DateTime?
  reconciled        Boolean       @default(false)
  reconciledBy      String? // User ID who reconciled the transaction
  reconciledAt      DateTime?
  documentUrls      String[] // URLs to supporting documents
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  transactions      Transaction[]
  childTransactions Transaction[] @relation("ParentTransaction")
}

enum AuditStatus {
  PENDING
  VERIFIED
  FLAGGED
  RECONCILED
  NEEDS_REVIEW
}

// Asset Valuation model
model AssetValuation {
  id                String          @id @default(cuid())
  assetType         AssetType
  assetId           String // ID of the asset (carbon credit ID, token address, etc.)
  valuationDate     DateTime        @default(now())
  valuationMethod   ValuationMethod
  valueAmount       Float
  valueCurrency     String          @default("USD")
  previousValue     Float?
  changePercentage  Float?
  valuationNotes    String?
  dataSource        String? // Source of valuation data
  confidence        Int? // Confidence level (0-100)
  approvedBy        String? // User ID who approved the valuation
  approvedAt        DateTime?
  transaction       Transaction?    @relation("TransactionValuation", fields: [transactionId], references: [id])
  transactionId     String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  CarbonCredit      CarbonCredit?   @relation(fields: [carbonCreditId], references: [id])
  carbonCreditId    String?
  transactions      Transaction[]
  childTransactions Transaction[]   @relation("ParentTransaction")
}

enum AssetType {
  CARBON_CREDIT
  TOKEN
  NFT
  WALLET
  PORTFOLIO
}

enum ValuationMethod {
  MARKET_PRICE
  HISTORICAL_COST
  FAIR_VALUE
  DISCOUNTED_CASH_FLOW
  COMPARABLE_SALES
  WEIGHTED_AVERAGE
  MANUAL
}

// Subscription model
model Subscription {
  id             String             @id @default(cuid())
  plan           SubscriptionPlan
  startDate      DateTime           @default(now())
  endDate        DateTime?
  status         SubscriptionStatus @default(ACTIVE)
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  organization   Organization       @relation(fields: [organizationId], references: [id])
  organizationId String             @unique
}

enum SubscriptionPlan {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}

// Notification model
model Notification {
  id             String               @id @default(cuid())
  title          String
  message        String
  read           Boolean              @default(false)
  type           NotificationType
  priority       NotificationPriority @default(NORMAL)
  actionUrl      String? // URL to redirect when notification is clicked
  actionLabel    String? // Label for the action button
  icon           String? // Icon to display with notification
  expiresAt      DateTime? // When the notification expires
  metadata       Json? // Additional data related to the notification
  createdAt      DateTime             @default(now())
  user           User                 @relation(fields: [userId], references: [id])
  userId         String
  organization   Organization?        @relation(fields: [organizationId], references: [id])
  organizationId String?
  emailSent      Boolean              @default(false) // Whether an email was sent
  emailSentAt    DateTime? // When the email was sent
  pushSent       Boolean              @default(false) // Whether a push notification was sent
  pushSentAt     DateTime? // When the push notification was sent
}

enum NotificationType {
  SYSTEM
  ORDER
  TRANSACTION
  CREDIT
  VERIFICATION
  SUBSCRIPTION
  PAYMENT
  BILLING
  TEAM
  SECURITY
  MARKETPLACE
  WALLET
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// Notification Preference model
model NotificationPreference {
  id        String   @id @default(cuid())
  email     Boolean  @default(true) // Receive email notifications
  push      Boolean  @default(true) // Receive push notifications
  inApp     Boolean  @default(true) // Receive in-app notifications
  types     Json? // Specific notification types preferences
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String   @unique
}

// Password Reset Token model
model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
}

// Email Verification Token model
model VerificationToken {
  id        String   @id @default(cuid())
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
  email     String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
}

// Token model (ERC-20)
model Token {
  id              String   @id @default(cuid())
  contractAddress String
  name            String?
  symbol          String?
  decimals        Int?
  balance         String // Stored as string to handle large numbers
  lastUpdated     DateTime @default(now())
  wallet          Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId        String

  @@unique([contractAddress, walletId])
}

// NFT model (ERC-721 and ERC-1155)
model NFT {
  id              String   @id @default(cuid())
  contractAddress String
  tokenId         String
  name            String?
  description     String?
  tokenType       String? // ERC-721 or ERC-1155
  metadata        Json? // Additional metadata
  lastUpdated     DateTime @default(now())
  wallet          Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId        String

  @@unique([contractAddress, tokenId, walletId])
}

// Team model for organization team management
model Team {
  id             String          @id @default(cuid())
  name           String
  description    String?
  permissions    Json? // Legacy permissions as JSON (for backward compatibility)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  organization   Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  members        TeamMember[]
  teamRoles      TeamRole[]
  resourceScopes ResourceScope[] // Resources this team has access to
}

// Team Member model
model TeamMember {
  id         String       @id @default(cuid())
  role       TeamRoleType @default(MEMBER)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  team       Team         @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId     String
  user       User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     String
  teamRole   TeamRole?    @relation(fields: [teamRoleId], references: [id])
  teamRoleId String?

  @@unique([teamId, userId])
}

// Team Role model - for custom team roles
model TeamRole {
  id          String       @id @default(cuid())
  name        String
  description String?
  team        Team         @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId      String
  permissions Json // Permissions for this role
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  members     TeamMember[]
}

// Resource Scope model - for team-specific resource access
model ResourceScope {
  id           String      @id @default(cuid())
  team         Team        @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId       String
  resourceType String // e.g., "carbon_credit", "wallet"
  resourceId   String? // Optional specific resource ID
  accessLevel  AccessLevel @default(READ)
  conditions   Json? // Optional conditions
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@unique([teamId, resourceType, resourceId])
}

enum TeamRoleType {
  ADMIN
  MANAGER
  MEMBER
  VIEWER
}

enum AccessLevel {
  READ
  WRITE
  ADMIN
  NONE
}

// Invitation model for inviting users to organizations
model Invitation {
  id             String           @id @default(cuid())
  email          String
  token          String           @unique
  role           UserRole         @default(USER)
  teamId         String?
  expires        DateTime
  status         InvitationStatus @default(PENDING)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

// Document model for organization verification and KYC
model Document {
  id             String         @id @default(cuid())
  name           String
  type           DocumentType
  url            String
  status         DocumentStatus @default(PENDING)
  notes          String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
}

enum DocumentType {
  BUSINESS_REGISTRATION
  TAX_CERTIFICATE
  IDENTITY_PROOF
  ADDRESS_PROOF
  BANK_STATEMENT
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

// Payment Method model
model PaymentMethod {
  id             String              @id @default(cuid())
  type           PaymentMethodType
  name           String
  status         PaymentMethodStatus @default(ACTIVE)
  isDefault      Boolean             @default(false)
  lastFour       String? // Last four digits for cards
  expiryMonth    Int? // Expiry month for cards
  expiryYear     Int? // Expiry year for cards
  billingAddress String?
  billingCity    String?
  billingState   String?
  billingZip     String?
  billingCountry String?
  metadata       Json? // Additional payment method data
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organization   Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  billingRecords BillingRecord[]
}

enum PaymentMethodType {
  CREDIT_CARD
  BANK_TRANSFER
  CRYPTO_WALLET
  INVOICE
}

enum PaymentMethodStatus {
  ACTIVE
  EXPIRED
  SUSPENDED
}

// Billing Record model
model BillingRecord {
  id              String         @id @default(cuid())
  amount          Float
  currency        String         @default("USD")
  description     String
  status          BillingStatus  @default(PENDING)
  dueDate         DateTime?
  paidDate        DateTime?
  invoiceNumber   String?
  invoiceUrl      String?
  receiptUrl      String?
  type            BillingType
  metadata        Json?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  organization    Organization   @relation(fields: [organizationId], references: [id])
  organizationId  String
  paymentMethod   PaymentMethod? @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId String?
}

enum BillingType {
  SUBSCRIPTION
  LISTING_FEE
  TRANSACTION_FEE
  PLATFORM_FEE
  REFUND
}

enum BillingStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  CANCELLED
}

// Carbon Credit Document model
model CarbonCreditDocument {
  id             String                   @id @default(cuid())
  name           String
  type           CarbonCreditDocumentType
  url            String
  status         DocumentStatus           @default(PENDING)
  notes          String?
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  carbonCredit   CarbonCredit             @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
  carbonCreditId String
}

enum CarbonCreditDocumentType {
  CERTIFICATE
  VERIFICATION_REPORT
  PROJECT_DESCRIPTION
  METHODOLOGY
  MONITORING_REPORT
  VALIDATION_REPORT
  OTHER
}

// Carbon Credit Verification model
model CarbonCreditVerification {
  id             String             @id @default(cuid())
  status         VerificationStatus
  verifier       String? // Name of the verifier
  verifierEmail  String? // Email of the verifier
  notes          String?
  timestamp      DateTime           @default(now())
  metadata       Json?
  carbonCredit   CarbonCredit       @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
  carbonCreditId String
}

// Carbon Credit Price History model
model CarbonCreditPrice {
  id             String       @id @default(cuid())
  price          Float
  timestamp      DateTime     @default(now())
  reason         String? // Reason for price change
  carbonCredit   CarbonCredit @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
  carbonCreditId String
}

// Audit Log model
model AuditLog {
  id             String        @id @default(cuid())
  type           AuditLogType
  description    String
  metadata       Json?
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime      @default(now())
  user           User?         @relation(fields: [userId], references: [id])
  userId         String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?
}

// Compliance models

// Compliance Report model
model ComplianceReport {
  id             String        @id @default(cuid())
  name           String
  description    String?
  type           String // "KYC", "AML", "VERIFICATION", "AUDIT", "CUSTOM"
  format         String        @default("PDF")
  startDate      DateTime
  endDate        DateTime
  filters        Json?
  data           Json?
  url            String?
  generatedBy    String // User ID who generated the report
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  user           User?         @relation(fields: [userId], references: [id])
  userId         String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?
}

// KYC Verification model
model KycVerification {
  id                  String                   @id @default(cuid())
  status              ComplianceStatus         @default(PENDING)
  level               KycLevel                 @default(NONE)
  lastChecked         DateTime                 @default(now())
  expiresAt           DateTime?
  verifier            String? // User ID who verified
  verificationDate    DateTime?
  rejectionReason     String?
  additionalInfo      Json?
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt
  user                User?                    @relation(fields: [userId], references: [id])
  userId              String?                  @unique
  organization        Organization?            @relation(fields: [organizationId], references: [id])
  organizationId      String?                  @unique
  documents           ComplianceDocument[]
  verificationHistory KycVerificationHistory[]
}

enum ComplianceStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  EXPIRED
  REQUIRES_UPDATE
}

enum KycLevel {
  NONE
  BASIC
  INTERMEDIATE
  ADVANCED
  ENTERPRISE
}

// KYC Verification History model
model KycVerificationHistory {
  id                String           @id @default(cuid())
  status            ComplianceStatus
  notes             String?
  verifier          String? // User ID who made the change
  timestamp         DateTime         @default(now())
  kycVerification   KycVerification  @relation(fields: [kycVerificationId], references: [id], onDelete: Cascade)
  kycVerificationId String
}

// AML Check model
model AmlCheck {
  id             String              @id @default(cuid())
  status         ComplianceStatus    @default(PENDING)
  riskLevel      ComplianceRiskLevel @default(LOW)
  lastChecked    DateTime            @default(now())
  expiresAt      DateTime?
  checkMethod    String? // Method used for the check
  checkProvider  String? // Provider used for the check
  referenceId    String? // External reference ID
  findings       Json? // Findings from the check
  actionTaken    String? // Action taken based on findings
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  user           User?               @relation(fields: [userId], references: [id])
  userId         String?             @unique
  organization   Organization?       @relation(fields: [organizationId], references: [id])
  organizationId String?             @unique
  amlAlerts      AmlAlert[]
  amlRules       AmlRule[]
}

enum ComplianceRiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// AML Alert model
model AmlAlert {
  id              String              @id @default(cuid())
  title           String
  description     String
  riskLevel       ComplianceRiskLevel
  status          AlertStatus         @default(OPEN)
  source          String // Source of the alert
  relatedEntity   String? // Related entity (user, organization, transaction)
  relatedEntityId String? // ID of the related entity
  assignedTo      String? // User ID assigned to handle the alert
  resolvedBy      String? // User ID who resolved the alert
  resolvedAt      DateTime?
  resolutionNotes String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  amlCheck        AmlCheck            @relation(fields: [amlCheckId], references: [id])
  amlCheckId      String
}

enum AlertStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  FALSE_POSITIVE
}

// AML Rule model
model AmlRule {
  id          String   @id @default(cuid())
  name        String
  description String?
  ruleType    String // Type of rule
  conditions  Json // Rule conditions
  actions     Json // Actions to take when rule is triggered
  isActive    Boolean  @default(true)
  priority    Int      @default(0) // Higher number = higher priority
  createdBy   String? // User ID who created the rule
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amlCheck    AmlCheck @relation(fields: [amlCheckId], references: [id])
  amlCheckId  String
}

// Compliance Document model
model ComplianceDocument {
  id                String                 @id @default(cuid())
  type              ComplianceDocumentType
  name              String
  url               String
  status            ComplianceStatus       @default(PENDING)
  notes             String?
  metadata          Json?
  expiresAt         DateTime?
  verifier          String? // User ID who verified the document
  verificationDate  DateTime?
  rejectionReason   String?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  user              User?                  @relation(fields: [userId], references: [id])
  userId            String?
  organization      Organization?          @relation(fields: [organizationId], references: [id])
  organizationId    String?
  kycVerification   KycVerification?       @relation(fields: [kycVerificationId], references: [id])
  kycVerificationId String?
  carbonCredit      CarbonCredit?          @relation("CarbonCreditCompliance", fields: [carbonCreditId], references: [id], map: "ComplianceDocument_carbonCreditId_compliance_fkey")
  carbonCreditId    String?
  project           Project?               @relation(fields: [projectId], references: [id])
  projectId         String?
  CarbonCredit      CarbonCredit?          @relation(fields: [carbonCreditId], references: [id])
}

enum ComplianceDocumentType {
  IDENTITY
  ADDRESS_PROOF
  BUSINESS_REGISTRATION
  TAX_CERTIFICATE
  BANK_STATEMENT
  PROJECT_DESCRIPTION
  METHODOLOGY
  VERIFICATION_REPORT
  VALIDATION_REPORT
  REGISTRY_CERTIFICATE
  MONITORING_REPORT
  OTHER
}

// Compliance Check model
model ComplianceCheck {
  id              String                @id @default(cuid())
  type            ComplianceCheckType
  result          ComplianceCheckResult
  riskLevel       ComplianceRiskLevel
  details         Json?
  walletAddress   String?
  transactionHash String?
  assetId         String? // ID of the asset being checked
  assetType       String? // Type of asset being checked
  checkDate       DateTime              @default(now())
  performedBy     String? // User ID who performed the check
  notes           String?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  user            User?                 @relation(fields: [userId], references: [id])
  userId          String?
  organization    Organization?         @relation(fields: [organizationId], references: [id])
  organizationId  String?
}

enum ComplianceCheckType {
  KYC
  AML
  TRANSACTION_SCREENING
  WALLET_SCREENING
  CARBON_CREDIT_VERIFICATION
  REGULATORY_CHECK
  SANCTIONS_CHECK
  PEP_CHECK
  CUSTOM
}

enum ComplianceCheckResult {
  PASS
  FAIL
  WARNING
  MANUAL_REVIEW
  INCONCLUSIVE
}

// Tax Report model
model TaxReport {
  id             String        @id @default(cuid())
  year           Int
  quarter        Int?
  format         String        @default("pdf")
  status         String        @default("PENDING")
  url            String?
  data           Json?
  taxableIncome  Float?
  taxableGains   Float?
  deductions     Float?
  taxOwed        Float?
  jurisdiction   String? // Tax jurisdiction
  filingDeadline DateTime? // Filing deadline
  filedDate      DateTime? // When the report was filed
  preparedBy     String? // Who prepared the report
  reviewedBy     String? // Who reviewed the report
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  user           User?         @relation(fields: [userId], references: [id])
  userId         String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?
}

// Financial Monitoring models

// Financial Metric model
model FinancialMetric {
  id             String              @id @default(cuid())
  metricType     FinancialMetricType
  name           String
  value          Float
  previousValue  Float?
  changePercent  Float?
  currency       String              @default("USD")
  period         String // e.g., "daily", "weekly", "monthly", "quarterly", "yearly"
  startDate      DateTime
  endDate        DateTime
  target         Float? // Target value if applicable
  status         MetricStatus? // Status compared to target
  notes          String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organization   Organization        @relation(fields: [organizationId], references: [id])
  organizationId String
}

enum FinancialMetricType {
  TRANSACTION_VOLUME
  REVENUE
  EXPENSE
  WALLET_BALANCE
  CARBON_ASSET_VALUE
  PROFIT_LOSS
  FEE_REVENUE
  TRADING_VOLUME
  AVERAGE_TRANSACTION_SIZE
  RETIREMENT_VOLUME
  TOKENIZATION_VOLUME
}

enum MetricStatus {
  ABOVE_TARGET
  ON_TARGET
  BELOW_TARGET
  CRITICAL
}

// Financial Report model
model FinancialReport {
  id             String              @id @default(cuid())
  reportType     FinancialReportType
  name           String
  description    String?
  period         String // e.g., "Q1 2023", "January 2023"
  startDate      DateTime
  endDate        DateTime
  status         ReportStatus        @default(DRAFT)
  url            String? // URL to the generated report
  data           Json? // Report data
  generatedBy    String? // User ID who generated the report
  approvedBy     String? // User ID who approved the report
  approvedAt     DateTime?
  scheduledId    String? // ID of the scheduled report if applicable
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organization   Organization        @relation(fields: [organizationId], references: [id])
  organizationId String
}

enum FinancialReportType {
  TRANSACTION_SUMMARY
  REVENUE_REPORT
  EXPENSE_REPORT
  ASSET_VALUATION
  PROFIT_LOSS
  BALANCE_SHEET
  CASH_FLOW
  TAX_SUMMARY
  TRADING_ACTIVITY
  CUSTOM
}

enum ReportStatus {
  DRAFT
  GENERATED
  APPROVED
  PUBLISHED
  ARCHIVED
}

// Period Comparison model
model PeriodComparison {
  id             String         @id @default(cuid())
  comparisonType ComparisonType
  name           String
  description    String?
  period1Start   DateTime
  period1End     DateTime
  period2Start   DateTime
  period2End     DateTime
  metrics        Json // Comparison metrics
  changePercent  Float? // Overall change percentage
  insights       String? // Insights from the comparison
  createdBy      String? // User ID who created the comparison
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organization   Organization   @relation(fields: [organizationId], references: [id])
  organizationId String
}

enum ComparisonType {
  MONTH_OVER_MONTH
  QUARTER_OVER_QUARTER
  YEAR_OVER_YEAR
  CUSTOM
}

enum AuditLogType {
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  ORGANIZATION_CREATED
  ORGANIZATION_UPDATED
  ORGANIZATION_DELETED
  CARBON_CREDIT_CREATED
  CARBON_CREDIT_UPDATED
  CARBON_CREDIT_DELETED
  CARBON_CREDIT_LISTED
  CARBON_CREDIT_UNLISTED
  CARBON_CREDIT_TOKENIZED
  CARBON_CREDIT_RETIRED
  BUY_ORDER_CREATED
  SELL_ORDER_CREATED
  ORDER_MATCHED
  ORDER_CANCELLED
  ORDER_UPDATED
  ORDER_DELETED
  TRANSACTION_CREATED
  TRANSACTION_UPDATED
  TRADE_EXECUTED
  WALLET_CREATED
  WALLET_UPDATED
  WALLET_CONNECTED
  WALLET_DISCONNECTED
  WALLET_SECURITY_UPDATED
  WALLET_ACCESS_GRANTED
  WALLET_ACCESS_REVOKED
  WALLET_RECOVERY_ENABLED
  WALLET_RECOVERY_DISABLED
  WALLET_LIMITS_UPDATED
  WALLET_APPROVAL_REQUIRED
  WALLET_APPROVAL_GRANTED
  WALLET_APPROVAL_REJECTED
  BRIDGE_TRANSACTION_INITIATED
  BRIDGE_TRANSACTION_COMPLETED
  BRIDGE_TRANSACTION_FAILED
  GAS_SETTINGS_UPDATED
  SUBSCRIPTION_CREATED
  SUBSCRIPTION_UPDATED
  LOGIN_SUCCESS
  LOGIN_FAILED
  PASSWORD_RESET
  EMAIL_VERIFIED
  TEAM_CREATED
  TEAM_UPDATED
  TEAM_DELETED
  INVITATION_SENT
  INVITATION_ACCEPTED
  DOCUMENT_UPLOADED
  DOCUMENT_VERIFIED
  PAYMENT_PROCESSED
  BILLING_CREATED
  VERIFICATION_REQUESTED
  VERIFICATION_APPROVED
  VERIFICATION_REJECTED
  TOKENIZATION_REQUESTED
  BATCH_VERIFICATION_REQUESTED
  KYC_VERIFICATION_REQUESTED
  KYC_VERIFICATION_APPROVED
  KYC_VERIFICATION_REJECTED
  AML_CHECK_PERFORMED
  TAX_REPORT_GENERATED
  TRANSACTION_AUDITED
  TRANSACTION_FLAGGED
  TRANSACTION_RECONCILED
  ASSET_VALUATION_CREATED
  ASSET_VALUATION_UPDATED
  ASSET_VALUATION_APPROVED
  FINANCIAL_METRIC_CREATED
  FINANCIAL_METRIC_UPDATED
  FINANCIAL_REPORT_GENERATED
  FINANCIAL_REPORT_APPROVED
  FINANCIAL_REPORT_PUBLISHED
  PERIOD_COMPARISON_CREATED
  PERMISSION_GRANTED
  PERMISSION_REVOKED
  ROLE_CREATED
  ROLE_UPDATED
  ROLE_DELETED
  PERMISSION_REQUESTED
  PERMISSION_REQUEST_APPROVED
  PERMISSION_REQUEST_REJECTED
  TEMPORARY_PERMISSION_GRANTED
  TEMPORARY_PERMISSION_EXPIRED
  ORGANIZATION_FEES_UPDATED
  SETTINGS_UPDATED
}

// Platform Settings model
model PlatformSettings {
  id                        String   @id @default(cuid())
  defaultListingFeeRate     Float    @default(2.5) // Default listing fee rate in percentage
  defaultTransactionFeeRate Float    @default(1.0) // Default transaction fee rate in percentage
  defaultSubscriptionFee    Float    @default(0.0) // Default subscription fee
  minListingFeeRate         Float    @default(0.5) // Minimum allowed listing fee rate
  maxListingFeeRate         Float    @default(5.0) // Maximum allowed listing fee rate
  minTransactionFeeRate     Float    @default(0.1) // Minimum allowed transaction fee rate
  maxTransactionFeeRate     Float    @default(3.0) // Maximum allowed transaction fee rate
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt
}

// Enhanced RBAC Models

// Permission model - atomic permissions
model Permission {
  id                 String                @id @default(cuid())
  name               String                @unique // e.g., "create:carbon_credit"
  displayName        String // e.g., "Create Carbon Credit"
  description        String?
  category           String // e.g., "carbon_credits", "organization", "wallet"
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  permissionGrants   PermissionGrant[]
  rolePermissions    RolePermission[]
  resourceTypes      ResourcePermission[]
  tempPermissions    TemporaryPermission[]
  permissionRequests PermissionRequest[]
}

// Custom Role model
model CustomRole {
  id             String           @id @default(cuid())
  name           String // e.g., "Carbon Credit Manager"
  description    String?
  isSystemRole   Boolean          @default(false) // Whether this is a system-defined role
  parentRoleId   String? // For role hierarchy
  parentRole     CustomRole?      @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles     CustomRole[]     @relation("RoleHierarchy")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization?    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?
  permissions    RolePermission[]
  userRoles      UserCustomRole[]
}

// Role Permission mapping
model RolePermission {
  id           String     @id @default(cuid())
  role         CustomRole @relation(fields: [roleId], references: [id], onDelete: Cascade)
  roleId       String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  conditions   Json? // Optional conditions for when this permission applies

  @@unique([roleId, permissionId])
}

// User Custom Role mapping
model UserCustomRole {
  id        String     @id @default(cuid())
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  role      CustomRole @relation(fields: [roleId], references: [id], onDelete: Cascade)
  roleId    String
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  expiresAt DateTime? // Optional expiration date for the role
  grantedBy String? // User ID who granted this role
  scope     Json? // Optional scope limitations (e.g., specific teams)

  @@unique([userId, roleId])
}

// Direct Permission Grant model
model PermissionGrant {
  id           String     @id @default(cuid())
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  expiresAt    DateTime? // Optional expiration date
  grantedBy    String? // User ID who granted this permission
  conditions   Json? // Optional conditions for when this permission applies
  scope        Json? // Optional scope limitations

  @@unique([userId, permissionId])
}

// Resource Permission model - for resource-level permissions
model ResourcePermission {
  id           String     @id @default(cuid())
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String
  resourceType String // e.g., "carbon_credit", "wallet", "team"
  resourceId   String? // Optional specific resource ID
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  conditions   Json? // Optional conditions

  @@unique([permissionId, resourceType, resourceId])
}

// Temporary Permission model
model TemporaryPermission {
  id           String     @id @default(cuid())
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String
  createdAt    DateTime   @default(now())
  expiresAt    DateTime // When this temporary permission expires
  reason       String // Why this temporary permission was granted
  grantedBy    String // User ID who granted this permission
  resourceType String? // Optional resource type
  resourceId   String? // Optional specific resource ID
  conditions   Json? // Optional conditions

  @@unique([userId, permissionId, resourceType, resourceId])
}

// Permission Request model
model PermissionRequest {
  id            String        @id @default(cuid())
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId        String
  permission    Permission    @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId  String
  status        RequestStatus @default(PENDING)
  reason        String // Why the user is requesting this permission
  resourceType  String? // Optional resource type
  resourceId    String? // Optional specific resource ID
  duration      Int? // Optional requested duration in hours
  approver      User?         @relation("Approver", fields: [approverId], references: [id])
  approverId    String?
  approvalNotes String? // Notes from the approver
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  expiresAt     DateTime? // When the request expires if not acted upon
}

enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
  EXPIRED
}

// Permission Usage Log model
model PermissionUsageLog {
  id           String   @id @default(cuid())
  userId       String // User who used the permission
  permissionId String // Permission that was used
  resourceType String? // Optional resource type
  resourceId   String? // Optional specific resource ID
  action       String // What action was performed
  success      Boolean // Whether the permission check succeeded
  timestamp    DateTime @default(now())
  ipAddress    String? // IP address of the user
  userAgent    String? // User agent of the user
  metadata     Json? // Additional metadata
}

// Enterprise-specific models

// Department model for enterprise organizations
model Department {
  id             String       @id @default(cuid())
  name           String
  description    String?
  code           String? // Department code
  parentId       String? // For hierarchical departments
  parent         Department?  @relation("DepartmentHierarchy", fields: [parentId], references: [id])
  children       Department[] @relation("DepartmentHierarchy")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  users          User[]       @relation("UserDepartment")
  divisions      Division[]
}

// Division model for enterprise organizations
model Division {
  id             String       @id @default(cuid())
  name           String
  description    String?
  code           String? // Division code
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  department     Department?  @relation(fields: [departmentId], references: [id])
  departmentId   String?
  users          User[]       @relation("UserDivision")
}

// Organization RBAC Settings model
model OrganizationRbacSettings {
  id                            String       @id @default(cuid())
  organization                  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId                String       @unique
  enableCustomRoles             Boolean      @default(false) // Whether custom roles are enabled
  enableResourcePermissions     Boolean      @default(false) // Whether resource-level permissions are enabled
  enableRoleHierarchy           Boolean      @default(false) // Whether role hierarchy is enabled
  enableTemporaryAccess         Boolean      @default(false) // Whether temporary access is enabled
  enablePermissionRequests      Boolean      @default(false) // Whether permission requests are enabled
  permissionRequestExpiry       Int          @default(72) // Hours until permission requests expire
  temporaryAccessMaxDuration    Int          @default(168) // Max hours for temporary access (1 week)
  requireApprovalForRoles       Boolean      @default(true) // Whether role assignments require approval
  requireApprovalForPermissions Boolean      @default(true) // Whether direct permission grants require approval
  createdAt                     DateTime     @default(now())
  updatedAt                     DateTime     @updatedAt
}

// Wallet Guardian model for social recovery
model WalletGuardian {
  id          String                   @id @default(cuid())
  wallet      Wallet                   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId    String
  address     String // Guardian's wallet address
  name        String? // Optional name for the guardian
  email       String? // Optional email for notifications
  status      String                   @default("ACTIVE") // ACTIVE, INACTIVE, REMOVED
  addedAt     DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt
  approvals   WalletRecoveryApproval[]
  description String? // Optional description of the guardian's relationship

  @@unique([walletId, address])
}

// Wallet Recovery model for social recovery
model WalletRecovery {
  id          String                   @id @default(cuid())
  wallet      Wallet                   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  walletId    String
  status      String                   @default("PENDING") // PENDING, APPROVED, EXECUTED, CANCELLED, EXPIRED
  initiator   String // Address that initiated the recovery
  timelock    DateTime // When the recovery can be executed (after timelock period)
  createdAt   DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt
  executedAt  DateTime? // When the recovery was executed
  approvals   WalletRecoveryApproval[]
  newOwner    String? // New owner address (set when executed)
  description String? // Optional description of the recovery reason
}

// Wallet Recovery Approval model
model WalletRecoveryApproval {
  id         String         @id @default(cuid())
  recovery   WalletRecovery @relation(fields: [recoveryId], references: [id], onDelete: Cascade)
  recoveryId String
  guardian   WalletGuardian @relation(fields: [guardianId], references: [id])
  guardianId String
  approvedAt DateTime       @default(now())
  signature  String? // Optional cryptographic signature
  notes      String? // Optional notes from the guardian

  @@unique([recoveryId, guardianId])
}
