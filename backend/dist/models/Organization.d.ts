import { Model, Sequelize, Association } from 'sequelize';
import { OrganizationStatus, OrganizationSize, VerificationStatus } from '@/types/enums';
export interface OrganizationAttributes {
    id: string;
    name: string;
    description?: string;
    website?: string;
    logo?: string;
    status: OrganizationStatus;
    verificationStatus: VerificationStatus;
    legalName?: string;
    registrationNumber?: string;
    taxId?: string;
    country?: string;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    phoneNumber?: string;
    industry?: string;
    size?: OrganizationSize;
    foundedYear?: number;
    primaryContact?: string;
    primaryContactEmail?: string;
    primaryContactPhone?: string;
    transactionFeeRate: number;
    listingFeeRate: number;
    subscriptionFee: number;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface OrganizationCreationAttributes extends Omit<OrganizationAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Organization extends Model<OrganizationAttributes, OrganizationCreationAttributes> implements OrganizationAttributes {
    id: string;
    name: string;
    description?: string;
    website?: string;
    logo?: string;
    status: OrganizationStatus;
    verificationStatus: VerificationStatus;
    legalName?: string;
    registrationNumber?: string;
    taxId?: string;
    country?: string;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    phoneNumber?: string;
    industry?: string;
    size?: OrganizationSize;
    foundedYear?: number;
    primaryContact?: string;
    primaryContactEmail?: string;
    primaryContactPhone?: string;
    transactionFeeRate: number;
    listingFeeRate: number;
    subscriptionFee: number;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        users: Association<Organization, any>;
        projects: Association<Organization, any>;
        carbonCredits: Association<Organization, any>;
        wallets: Association<Organization, any>;
        notifications: Association<Organization, any>;
        auditLogs: Association<Organization, any>;
    };
    static init(sequelize: Sequelize): typeof Organization;
    static associate(models: any): void;
}
//# sourceMappingURL=Organization.d.ts.map