"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Organization = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class Organization extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            name: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            website: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
                validate: {
                    isUrl: true,
                },
            },
            logo: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            status: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.OrganizationStatus)),
                allowNull: false,
                defaultValue: enums_1.OrganizationStatus.PENDING,
            },
            verificationStatus: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.VerificationStatus)),
                allowNull: false,
                defaultValue: enums_1.VerificationStatus.PENDING,
            },
            legalName: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            registrationNumber: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            taxId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            country: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            address: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            city: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            state: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            postalCode: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            phoneNumber: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            industry: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            size: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.OrganizationSize)),
                allowNull: true,
            },
            foundedYear: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
                validate: {
                    min: 1800,
                    max: new Date().getFullYear(),
                },
            },
            primaryContact: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            primaryContactEmail: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
                validate: {
                    isEmail: true,
                },
            },
            primaryContactPhone: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            transactionFeeRate: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0.01,
                validate: {
                    min: 0,
                    max: 1,
                },
            },
            listingFeeRate: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0.005,
                validate: {
                    min: 0,
                    max: 1,
                },
            },
            subscriptionFee: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0.0,
                validate: {
                    min: 0,
                },
            },
        }, {
            sequelize,
            tableName: 'organizations',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        Organization.hasMany(models.User, {
            foreignKey: 'organizationId',
            as: 'users',
        });
        Organization.hasMany(models.Project, {
            foreignKey: 'organizationId',
            as: 'projects',
        });
        Organization.hasMany(models.CarbonCredit, {
            foreignKey: 'organizationId',
            as: 'carbonCredits',
        });
        Organization.hasMany(models.Wallet, {
            foreignKey: 'organizationId',
            as: 'wallets',
        });
        Organization.hasMany(models.Notification, {
            foreignKey: 'organizationId',
            as: 'notifications',
        });
        Organization.hasMany(models.AuditLog, {
            foreignKey: 'organizationId',
            as: 'auditLogs',
        });
    }
}
exports.Organization = Organization;
//# sourceMappingURL=Organization.js.map