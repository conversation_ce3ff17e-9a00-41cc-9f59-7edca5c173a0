import { Model, Sequelize, Association } from 'sequelize';
import { ProjectType, ProjectStatus, VerificationStatus } from '@/types/enums';
export interface ProjectAttributes {
    id: string;
    name: string;
    description?: string;
    type: ProjectType;
    status: ProjectStatus;
    verificationStatus: VerificationStatus;
    startDate?: Date;
    endDate?: Date;
    location?: string;
    country?: string;
    coordinates?: string;
    area?: number;
    externalProjectId?: string;
    registryId?: string;
    standard?: string;
    methodology?: string;
    methodologyVersion?: string;
    estimatedReductions?: number;
    actualReductions?: number;
    verifier?: string;
    validator?: string;
    images?: string[];
    budget?: number;
    roi?: number;
    sdgs?: string[];
    tags?: string[];
    metadata?: any;
    organizationId: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface ProjectCreationAttributes extends Omit<ProjectAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Project extends Model<ProjectAttributes, ProjectCreationAttributes> implements ProjectAttributes {
    id: string;
    name: string;
    description?: string;
    type: ProjectType;
    status: ProjectStatus;
    verificationStatus: VerificationStatus;
    startDate?: Date;
    endDate?: Date;
    location?: string;
    country?: string;
    coordinates?: string;
    area?: number;
    externalProjectId?: string;
    registryId?: string;
    standard?: string;
    methodology?: string;
    methodologyVersion?: string;
    estimatedReductions?: number;
    actualReductions?: number;
    verifier?: string;
    validator?: string;
    images?: string[];
    budget?: number;
    roi?: number;
    sdgs?: string[];
    tags?: string[];
    metadata?: any;
    organizationId: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        organization: Association<Project, any>;
        carbonCredits: Association<Project, any>;
    };
    static init(sequelize: Sequelize): typeof Project;
    static associate(models: any): void;
}
//# sourceMappingURL=Project.d.ts.map