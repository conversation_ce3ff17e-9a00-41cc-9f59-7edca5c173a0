"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLog = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class AuditLog extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            action: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.AuditAction)),
                allowNull: false,
            },
            resource: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.AuditResource)),
                allowNull: false,
            },
            resourceId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            details: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            ipAddress: {
                type: sequelize_1.DataTypes.INET,
                allowNull: true,
            },
            userAgent: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            userId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'audit_logs',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        AuditLog.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user',
        });
        AuditLog.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
    }
}
exports.AuditLog = AuditLog;
//# sourceMappingURL=AuditLog.js.map