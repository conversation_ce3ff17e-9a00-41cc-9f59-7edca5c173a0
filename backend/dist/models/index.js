"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLog = exports.Notification = exports.Transaction = exports.Order = exports.Wallet = exports.CarbonCredit = exports.Project = exports.Organization = exports.User = exports.sequelize = void 0;
const database_1 = require("@/config/database");
Object.defineProperty(exports, "sequelize", { enumerable: true, get: function () { return database_1.sequelize; } });
const User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
const Organization_1 = require("./Organization");
Object.defineProperty(exports, "Organization", { enumerable: true, get: function () { return Organization_1.Organization; } });
const Project_1 = require("./Project");
Object.defineProperty(exports, "Project", { enumerable: true, get: function () { return Project_1.Project; } });
const CarbonCredit_1 = require("./CarbonCredit");
Object.defineProperty(exports, "CarbonCredit", { enumerable: true, get: function () { return CarbonCredit_1.CarbonCredit; } });
const Wallet_1 = require("./Wallet");
Object.defineProperty(exports, "Wallet", { enumerable: true, get: function () { return Wallet_1.Wallet; } });
const Order_1 = require("./Order");
Object.defineProperty(exports, "Order", { enumerable: true, get: function () { return Order_1.Order; } });
const Transaction_1 = require("./Transaction");
Object.defineProperty(exports, "Transaction", { enumerable: true, get: function () { return Transaction_1.Transaction; } });
const Notification_1 = require("./Notification");
Object.defineProperty(exports, "Notification", { enumerable: true, get: function () { return Notification_1.Notification; } });
const AuditLog_1 = require("./AuditLog");
Object.defineProperty(exports, "AuditLog", { enumerable: true, get: function () { return AuditLog_1.AuditLog; } });
const models = {
    User: User_1.User.init(database_1.sequelize),
    Organization: Organization_1.Organization.init(database_1.sequelize),
    Project: Project_1.Project.init(database_1.sequelize),
    CarbonCredit: CarbonCredit_1.CarbonCredit.init(database_1.sequelize),
    Wallet: Wallet_1.Wallet.init(database_1.sequelize),
    Order: Order_1.Order.init(database_1.sequelize),
    Transaction: Transaction_1.Transaction.init(database_1.sequelize),
    Notification: Notification_1.Notification.init(database_1.sequelize),
    AuditLog: AuditLog_1.AuditLog.init(database_1.sequelize),
};
Object.values(models).forEach((model) => {
    if (model.associate) {
        model.associate(models);
    }
});
exports.default = models;
//# sourceMappingURL=index.js.map