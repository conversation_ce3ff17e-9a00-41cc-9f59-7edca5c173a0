import { Model, Sequelize, Association } from 'sequelize';
export interface WalletAttributes {
    id: string;
    address: string;
    network: string;
    chainId?: number;
    type: string;
    name?: string;
    isActive: boolean;
    balance?: string;
    nativeBalance?: string;
    lastSyncAt?: Date;
    metadata?: any;
    userId?: string;
    organizationId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface WalletCreationAttributes extends Omit<WalletAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Wallet extends Model<WalletAttributes, WalletCreationAttributes> implements WalletAttributes {
    id: string;
    address: string;
    network: string;
    chainId?: number;
    type: string;
    name?: string;
    isActive: boolean;
    balance?: string;
    nativeBalance?: string;
    lastSyncAt?: Date;
    metadata?: any;
    userId?: string;
    organizationId?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        user: Association<Wallet, any>;
        organization: Association<Wallet, any>;
        transactions: Association<Wallet, any>;
    };
    static init(sequelize: Sequelize): typeof Wallet;
    static associate(models: any): void;
}
//# sourceMappingURL=Wallet.d.ts.map