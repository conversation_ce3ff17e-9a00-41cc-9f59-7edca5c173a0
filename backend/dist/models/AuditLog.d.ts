import { Model, Sequelize, Association } from 'sequelize';
import { AuditAction, AuditResource } from '@/types/enums';
export interface AuditLogAttributes {
    id: string;
    action: AuditAction;
    resource: AuditResource;
    resourceId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
    userId?: string;
    organizationId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface AuditLogCreationAttributes extends Omit<AuditLogAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class AuditLog extends Model<AuditLogAttributes, AuditLogCreationAttributes> implements AuditLogAttributes {
    id: string;
    action: AuditAction;
    resource: AuditResource;
    resourceId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
    userId?: string;
    organizationId?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        user: Association<AuditLog, any>;
        organization: Association<AuditLog, any>;
    };
    static init(sequelize: Sequelize): typeof AuditLog;
    static associate(models: any): void;
}
//# sourceMappingURL=AuditLog.d.ts.map