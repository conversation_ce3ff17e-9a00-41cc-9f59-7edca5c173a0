{"version": 3, "file": "CarbonCredit.js", "sourceRoot": "", "sources": ["../../src/models/CarbonCredit.ts"], "names": [], "mappings": ";;;AAAA,yCAAqE;AACrE,yCAAuE;AAyCvE,MAAa,YAAa,SAAQ,iBAA6D;IA+CtF,MAAM,CAAC,IAAI,CAAC,SAAoB;QACrC,OAAO,KAAK,CAAC,IAAI,CACf;YACE,EAAE,EAAE;gBACF,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,YAAY,EAAE,qBAAS,CAAC,MAAM;gBAC9B,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,CAAC;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,mBAAmB,EAAE;gBACnB,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,OAAO;gBACvB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE;oBACR,GAAG,EAAE,IAAI;oBACT,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;iBACnC;aACF;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,0BAAkB,CAAC,CAAC;gBAC1D,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,0BAAkB,CAAC,OAAO;aACzC;YACD,kBAAkB,EAAE;gBAClB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,0BAAkB,CAAC,CAAC;gBAC1D,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,0BAAkB,CAAC,OAAO;aACzC;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,qBAAqB,EAAE;gBACrB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,KAAK,CAAC,qBAAS,CAAC,MAAM,CAAC;gBACvC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,EAAE;aACjB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,OAAO;gBACvB,SAAS,EAAE,IAAI;aAChB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE;oBACV,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,IAAI;iBACV;aACF;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE;oBACV,KAAK,EAAE,eAAe;oBACtB,GAAG,EAAE,IAAI;iBACV;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE;oBACV,KAAK,EAAE,UAAU;oBACjB,GAAG,EAAE,IAAI;iBACV;aACF;SACF,EACD;YACE,SAAS;YACT,SAAS,EAAE,gBAAgB;YAC3B,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,IAAI;SACjB,CACF,CAAC;IACJ,CAAC;IAGM,MAAM,CAAC,SAAS,CAAC,MAAW;QACjC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE;YAClC,UAAU,EAAE,QAAQ;YACpB,EAAE,EAAE,MAAM;SACX,CAAC,CAAC;QAEH,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1C,UAAU,EAAE,gBAAgB;YAC5B,EAAE,EAAE,cAAc;SACnB,CAAC,CAAC;QAEH,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE;YACrC,UAAU,EAAE,WAAW;YACvB,EAAE,EAAE,SAAS;SACd,CAAC,CAAC;QAEH,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACjC,UAAU,EAAE,gBAAgB;YAC5B,EAAE,EAAE,QAAQ;SACb,CAAC,CAAC;IACL,CAAC;CACF;AAtPD,oCAsPC"}