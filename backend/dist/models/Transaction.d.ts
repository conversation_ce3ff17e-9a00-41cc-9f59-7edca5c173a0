import { Model, Sequelize, Association } from 'sequelize';
import { TransactionType, TransactionStatus } from '@/types/enums';
export interface TransactionAttributes {
    id: string;
    amount: number;
    fee: number;
    gasPrice?: number;
    gasLimit?: number;
    gasUsed?: number;
    maxFeePerGas?: number;
    maxPriorityFeePerGas?: number;
    type: TransactionType;
    status: TransactionStatus;
    transactionHash?: string;
    blockNumber?: number;
    network?: string;
    metadata?: any;
    walletId?: string;
    orderId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface TransactionCreationAttributes extends Omit<TransactionAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Transaction extends Model<TransactionAttributes, TransactionCreationAttributes> implements TransactionAttributes {
    id: string;
    amount: number;
    fee: number;
    gasPrice?: number;
    gasLimit?: number;
    gasUsed?: number;
    maxFeePerGas?: number;
    maxPriorityFeePerGas?: number;
    type: TransactionType;
    status: TransactionStatus;
    transactionHash?: string;
    blockNumber?: number;
    network?: string;
    metadata?: any;
    walletId?: string;
    orderId?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        wallet: Association<Transaction, any>;
        order: Association<Transaction, any>;
    };
    static init(sequelize: Sequelize): typeof Transaction;
    static associate(models: any): void;
}
//# sourceMappingURL=Transaction.d.ts.map