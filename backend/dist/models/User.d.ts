import { Model, Sequelize, Association } from 'sequelize';
import { UserRole } from '@/types/enums';
export interface UserAttributes {
    id: string;
    email: string;
    name?: string;
    password?: string;
    role: UserRole;
    emailVerified?: Date;
    jobTitle?: string;
    departmentName?: string;
    phoneNumber?: string;
    profileImage?: string;
    bio?: string;
    lastLoginAt?: Date;
    twoFactorEnabled: boolean;
    preferences?: any;
    organizationId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
    id: string;
    email: string;
    name?: string;
    password?: string;
    role: UserRole;
    emailVerified?: Date;
    jobTitle?: string;
    departmentName?: string;
    phoneNumber?: string;
    profileImage?: string;
    bio?: string;
    lastLoginAt?: Date;
    twoFactorEnabled: boolean;
    preferences?: any;
    organizationId?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        organization: Association<User, any>;
        wallets: Association<User, any>;
        carbonCredits: Association<User, any>;
        buyOrders: Association<User, any>;
        sellOrders: Association<User, any>;
        notifications: Association<User, any>;
        auditLogs: Association<User, any>;
    };
    validatePassword(password: string): Promise<boolean>;
    setPassword(password: string): Promise<void>;
    toJSON(): object;
    static init(sequelize: Sequelize): typeof User;
    static associate(models: any): void;
}
//# sourceMappingURL=User.d.ts.map