{"version": 3, "file": "Project.js", "sourceRoot": "", "sources": ["../../src/models/Project.ts"], "names": [], "mappings": ";;;AAAA,yCAAqE;AACrE,yCAA+E;AAqC/E,MAAa,OAAQ,SAAQ,iBAAmD;IAyCvE,MAAM,CAAC,IAAI,CAAC,SAAoB;QACrC,OAAO,KAAK,CAAC,IAAI,CACf;YACE,EAAE,EAAE;gBACF,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,YAAY,EAAE,qBAAS,CAAC,MAAM;gBAC9B,UAAU,EAAE,IAAI;aACjB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAW,CAAC,CAAC;gBACnD,SAAS,EAAE,KAAK;aACjB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,qBAAa,CAAC,CAAC;gBACrD,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,qBAAa,CAAC,OAAO;aACpC;YACD,kBAAkB,EAAE;gBAClB,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,0BAAkB,CAAC,CAAC;gBAC1D,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,0BAAkB,CAAC,OAAO;aACzC;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,kBAAkB,EAAE;gBAClB,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,mBAAmB,EAAE;gBACnB,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,qBAAS,CAAC,MAAM;gBACtB,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,KAAK,CAAC,qBAAS,CAAC,MAAM,CAAC;gBACvC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,EAAE;aACjB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;iBACP;aACF;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,KAAK,CAAC,qBAAS,CAAC,MAAM,CAAC;gBACvC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,EAAE;aACjB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,qBAAS,CAAC,KAAK,CAAC,qBAAS,CAAC,MAAM,CAAC;gBACvC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,EAAE;aACjB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,qBAAS,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI;aAChB;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,qBAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE;oBACV,KAAK,EAAE,eAAe;oBACtB,GAAG,EAAE,IAAI;iBACV;aACF;SACF,EACD;YACE,SAAS;YACT,SAAS,EAAE,UAAU;YACrB,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,IAAI;SACjB,CACF,CAAC;IACJ,CAAC;IAGM,MAAM,CAAC,SAAS,CAAC,MAAW;QACjC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE;YACrC,UAAU,EAAE,gBAAgB;YAC5B,EAAE,EAAE,cAAc;SACnB,CAAC,CAAC;QAEH,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE;YACnC,UAAU,EAAE,WAAW;YACvB,EAAE,EAAE,eAAe;SACpB,CAAC,CAAC;IACL,CAAC;CACF;AAxMD,0BAwMC"}