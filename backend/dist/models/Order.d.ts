import { Model, Sequelize, Association } from 'sequelize';
import { OrderType, OrderStatus } from '@/types/enums';
export interface OrderAttributes {
    id: string;
    type: OrderType;
    quantity: number;
    price: number;
    status: OrderStatus;
    buyerId: string;
    sellerId: string;
    carbonCreditId: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface OrderCreationAttributes extends Omit<OrderAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Order extends Model<OrderAttributes, OrderCreationAttributes> implements OrderAttributes {
    id: string;
    type: OrderType;
    quantity: number;
    price: number;
    status: OrderStatus;
    buyerId: string;
    sellerId: string;
    carbonCreditId: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        buyer: Association<Order, any>;
        seller: Association<Order, any>;
        carbonCredit: Association<Order, any>;
        transactions: Association<Order, any>;
    };
    static init(sequelize: Sequelize): typeof Order;
    static associate(models: any): void;
}
//# sourceMappingURL=Order.d.ts.map