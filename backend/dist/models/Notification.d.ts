import { Model, Sequelize, Association } from 'sequelize';
import { NotificationType, NotificationCategory } from '@/types/enums';
export interface NotificationAttributes {
    id: string;
    title: string;
    message: string;
    type: NotificationType;
    category: NotificationCategory;
    isRead: boolean;
    data?: any;
    userId?: string;
    organizationId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface NotificationCreationAttributes extends Omit<NotificationAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class Notification extends Model<NotificationAttributes, NotificationCreationAttributes> implements NotificationAttributes {
    id: string;
    title: string;
    message: string;
    type: NotificationType;
    category: NotificationCategory;
    isRead: boolean;
    data?: any;
    userId?: string;
    organizationId?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        user: Association<Notification, any>;
        organization: Association<Notification, any>;
    };
    static init(sequelize: Sequelize): typeof Notification;
    static associate(models: any): void;
}
//# sourceMappingURL=Notification.d.ts.map