{"version": 3, "file": "User.d.ts", "sourceRoot": "", "sources": ["../../src/models/User.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACrE,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAGzC,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,QAAQ,CAAC;IACf,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,IAAI,CAAC;IACnB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,WAAW,CAAC,EAAE,GAAG,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,SAAS,CAAC,EAAE,IAAI,CAAC;CAClB;AAED,MAAM,WAAW,sBAAuB,SAAQ,IAAI,CAAC,cAAc,EAAE,IAAI,GAAG,WAAW,GAAG,WAAW,CAAC;CAAG;AAEzG,qBAAa,IAAK,SAAQ,KAAK,CAAC,cAAc,EAAE,sBAAsB,CAAE,YAAW,cAAc;IACxF,EAAE,EAAG,MAAM,CAAC;IACZ,KAAK,EAAG,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,EAAG,QAAQ,CAAC;IAChB,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,IAAI,CAAC;IACnB,gBAAgB,EAAG,OAAO,CAAC;IAC3B,WAAW,CAAC,EAAE,GAAG,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;IAG/B,SAAgB,SAAS,EAAG,IAAI,CAAC;IACjC,SAAgB,SAAS,EAAG,IAAI,CAAC;IAGjC,OAAc,YAAY,EAAE;QAC1B,YAAY,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACrC,OAAO,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAChC,aAAa,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAClC,UAAU,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACnC,aAAa,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACtC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KACnC,CAAC;IAGW,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAKpD,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKlD,MAAM,IAAI,MAAM;WAOT,IAAI,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,IAAI;WAiGvC,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI;CAoC3C"}