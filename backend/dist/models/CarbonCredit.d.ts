import { Model, Sequelize, Association } from 'sequelize';
import { CarbonCreditStatus, VerificationStatus } from '@/types/enums';
export interface CarbonCreditAttributes {
    id: string;
    name: string;
    description?: string;
    quantity: number;
    availableQuantity: number;
    retiredQuantity: number;
    price: number;
    minPurchaseQuantity?: number;
    vintage: number;
    standard: string;
    methodology: string;
    location?: string;
    country?: string;
    externalProjectId?: string;
    serialNumber?: string;
    certificationDate?: Date;
    expirationDate?: Date;
    verificationBody?: string;
    status: CarbonCreditStatus;
    verificationStatus: VerificationStatus;
    listingDate?: Date;
    retirementDate?: Date;
    retirementReason?: string;
    retirementBeneficiary?: string;
    images?: string[];
    tokenId?: string;
    contractAddress?: string;
    chainId?: number;
    metadata?: any;
    userId: string;
    organizationId: string;
    projectId: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface CarbonCreditCreationAttributes extends Omit<CarbonCreditAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
export declare class CarbonCredit extends Model<CarbonCreditAttributes, CarbonCreditCreationAttributes> implements CarbonCreditAttributes {
    id: string;
    name: string;
    description?: string;
    quantity: number;
    availableQuantity: number;
    retiredQuantity: number;
    price: number;
    minPurchaseQuantity?: number;
    vintage: number;
    standard: string;
    methodology: string;
    location?: string;
    country?: string;
    externalProjectId?: string;
    serialNumber?: string;
    certificationDate?: Date;
    expirationDate?: Date;
    verificationBody?: string;
    status: CarbonCreditStatus;
    verificationStatus: VerificationStatus;
    listingDate?: Date;
    retirementDate?: Date;
    retirementReason?: string;
    retirementBeneficiary?: string;
    images?: string[];
    tokenId?: string;
    contractAddress?: string;
    chainId?: number;
    metadata?: any;
    userId: string;
    organizationId: string;
    projectId: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    static associations: {
        user: Association<CarbonCredit, any>;
        organization: Association<CarbonCredit, any>;
        project: Association<CarbonCredit, any>;
        orders: Association<CarbonCredit, any>;
    };
    static init(sequelize: Sequelize): typeof CarbonCredit;
    static associate(models: any): void;
}
//# sourceMappingURL=CarbonCredit.d.ts.map