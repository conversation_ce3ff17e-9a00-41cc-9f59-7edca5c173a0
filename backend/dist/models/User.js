"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class User extends sequelize_1.Model {
    async validatePassword(password) {
        if (!this.password)
            return false;
        return bcryptjs_1.default.compare(password, this.password);
    }
    async setPassword(password) {
        const saltRounds = 12;
        this.password = await bcryptjs_1.default.hash(password, saltRounds);
    }
    toJSON() {
        const values = { ...this.get() };
        delete values.password;
        return values;
    }
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            email: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
                unique: true,
                validate: {
                    isEmail: true,
                },
            },
            name: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            password: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            role: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.UserRole)),
                allowNull: false,
                defaultValue: enums_1.UserRole.USER,
            },
            emailVerified: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            jobTitle: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            departmentName: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            phoneNumber: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            profileImage: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            bio: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            lastLoginAt: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            twoFactorEnabled: {
                type: sequelize_1.DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            preferences: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'users',
            underscored: true,
            timestamps: true,
            hooks: {
                beforeCreate: async (user) => {
                    if (user.password) {
                        await user.setPassword(user.password);
                    }
                },
                beforeUpdate: async (user) => {
                    if (user.changed('password') && user.password) {
                        await user.setPassword(user.password);
                    }
                },
            },
        });
    }
    static associate(models) {
        User.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
        User.hasMany(models.Wallet, {
            foreignKey: 'userId',
            as: 'wallets',
        });
        User.hasMany(models.CarbonCredit, {
            foreignKey: 'userId',
            as: 'carbonCredits',
        });
        User.hasMany(models.Order, {
            foreignKey: 'buyerId',
            as: 'buyOrders',
        });
        User.hasMany(models.Order, {
            foreignKey: 'sellerId',
            as: 'sellOrders',
        });
        User.hasMany(models.Notification, {
            foreignKey: 'userId',
            as: 'notifications',
        });
        User.hasMany(models.AuditLog, {
            foreignKey: 'userId',
            as: 'auditLogs',
        });
    }
}
exports.User = User;
//# sourceMappingURL=User.js.map