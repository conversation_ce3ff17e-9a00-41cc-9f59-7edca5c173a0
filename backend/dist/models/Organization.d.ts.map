{"version": 3, "file": "Organization.d.ts", "sourceRoot": "", "sources": ["../../src/models/Organization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACrE,OAAO,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AAEzF,MAAM,WAAW,sBAAsB;IACrC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,kBAAkB,CAAC;IAC3B,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,gBAAgB,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;IACxB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,SAAS,CAAC,EAAE,IAAI,CAAC;CAClB;AAED,MAAM,WAAW,8BAA+B,SAAQ,IAAI,CAAC,sBAAsB,EAAE,IAAI,GAAG,WAAW,GAAG,WAAW,CAAC;CAAG;AAEzH,qBAAa,YAAa,SAAQ,KAAK,CAAC,sBAAsB,EAAE,8BAA8B,CAAE,YAAW,sBAAsB;IACxH,EAAE,EAAG,MAAM,CAAC;IACZ,IAAI,EAAG,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,EAAG,kBAAkB,CAAC;IAC5B,kBAAkB,EAAG,kBAAkB,CAAC;IACxC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,gBAAgB,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,kBAAkB,EAAG,MAAM,CAAC;IAC5B,cAAc,EAAG,MAAM,CAAC;IACxB,eAAe,EAAG,MAAM,CAAC;IAGhC,SAAgB,SAAS,EAAG,IAAI,CAAC;IACjC,SAAgB,SAAS,EAAG,IAAI,CAAC;IAGjC,OAAc,YAAY,EAAE;QAC1B,KAAK,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACtC,QAAQ,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACzC,aAAa,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC9C,OAAO,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACxC,aAAa,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC9C,SAAS,EAAE,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;KAC3C,CAAC;WAGY,IAAI,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,YAAY;WA6I/C,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI;CA+B3C"}