"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transaction = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class Transaction extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            amount: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
            },
            fee: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0,
            },
            gasPrice: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
            },
            gasLimit: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
            },
            gasUsed: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
            },
            maxFeePerGas: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
            },
            maxPriorityFeePerGas: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
            },
            type: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.TransactionType)),
                allowNull: false,
            },
            status: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.TransactionStatus)),
                allowNull: false,
                defaultValue: enums_1.TransactionStatus.PENDING,
            },
            transactionHash: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
                unique: true,
            },
            blockNumber: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
            },
            network: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            metadata: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            walletId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'wallets',
                    key: 'id',
                },
            },
            orderId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'orders',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'transactions',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        Transaction.belongsTo(models.Wallet, {
            foreignKey: 'walletId',
            as: 'wallet',
        });
        Transaction.belongsTo(models.Order, {
            foreignKey: 'orderId',
            as: 'order',
        });
    }
}
exports.Transaction = Transaction;
//# sourceMappingURL=Transaction.js.map