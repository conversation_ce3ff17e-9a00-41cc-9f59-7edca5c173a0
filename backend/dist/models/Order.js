"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Order = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class Order extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            type: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.OrderType)),
                allowNull: false,
            },
            quantity: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                validate: {
                    min: 0,
                },
            },
            price: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                validate: {
                    min: 0,
                },
            },
            status: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.OrderStatus)),
                allowNull: false,
                defaultValue: enums_1.OrderStatus.PENDING,
            },
            buyerId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            sellerId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            carbonCreditId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'carbon_credits',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'orders',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        Order.belongsTo(models.User, {
            foreignKey: 'buyerId',
            as: 'buyer',
        });
        Order.belongsTo(models.User, {
            foreignKey: 'sellerId',
            as: 'seller',
        });
        Order.belongsTo(models.CarbonCredit, {
            foreignKey: 'carbonCreditId',
            as: 'carbonCredit',
        });
        Order.hasMany(models.Transaction, {
            foreignKey: 'orderId',
            as: 'transactions',
        });
    }
}
exports.Order = Order;
//# sourceMappingURL=Order.js.map