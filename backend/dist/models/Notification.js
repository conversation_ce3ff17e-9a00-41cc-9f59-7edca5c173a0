"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class Notification extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            title: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            message: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: false,
            },
            type: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.NotificationType)),
                allowNull: false,
            },
            category: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.NotificationCategory)),
                allowNull: false,
            },
            isRead: {
                type: sequelize_1.DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            data: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            userId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'notifications',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        Notification.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user',
        });
        Notification.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
    }
}
exports.Notification = Notification;
//# sourceMappingURL=Notification.js.map