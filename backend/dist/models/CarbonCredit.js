"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CarbonCredit = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class CarbonCredit extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            name: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            quantity: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                validate: {
                    min: 0,
                },
            },
            availableQuantity: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                validate: {
                    min: 0,
                },
            },
            retiredQuantity: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                defaultValue: 0,
                validate: {
                    min: 0,
                },
            },
            price: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: false,
                validate: {
                    min: 0,
                },
            },
            minPurchaseQuantity: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
                validate: {
                    min: 0,
                },
            },
            vintage: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: false,
                validate: {
                    min: 1990,
                    max: new Date().getFullYear() + 10,
                },
            },
            standard: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            methodology: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            location: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            country: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            externalProjectId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            serialNumber: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            certificationDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            expirationDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            verificationBody: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            status: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.CarbonCreditStatus)),
                allowNull: false,
                defaultValue: enums_1.CarbonCreditStatus.PENDING,
            },
            verificationStatus: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.VerificationStatus)),
                allowNull: false,
                defaultValue: enums_1.VerificationStatus.PENDING,
            },
            listingDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            retirementDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            retirementReason: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            retirementBeneficiary: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            images: {
                type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
                allowNull: true,
                defaultValue: [],
            },
            tokenId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            contractAddress: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            chainId: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
            },
            metadata: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            userId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
            projectId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'projects',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'carbon_credits',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        CarbonCredit.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user',
        });
        CarbonCredit.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
        CarbonCredit.belongsTo(models.Project, {
            foreignKey: 'projectId',
            as: 'project',
        });
        CarbonCredit.hasMany(models.Order, {
            foreignKey: 'carbonCreditId',
            as: 'orders',
        });
    }
}
exports.CarbonCredit = CarbonCredit;
//# sourceMappingURL=CarbonCredit.js.map