"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Wallet = void 0;
const sequelize_1 = require("sequelize");
class Wallet extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            address: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
                unique: true,
            },
            network: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            chainId: {
                type: sequelize_1.DataTypes.INTEGER,
                allowNull: true,
            },
            type: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            name: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            isActive: {
                type: sequelize_1.DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: true,
            },
            balance: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
                defaultValue: '0',
            },
            nativeBalance: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
                defaultValue: '0',
            },
            lastSyncAt: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            metadata: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            userId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id',
                },
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: true,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'wallets',
            underscored: true,
            timestamps: true,
            indexes: [
                {
                    unique: true,
                    fields: ['address', 'network'],
                },
            ],
        });
    }
    static associate(models) {
        Wallet.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user',
        });
        Wallet.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
        Wallet.hasMany(models.Transaction, {
            foreignKey: 'walletId',
            as: 'transactions',
        });
    }
}
exports.Wallet = Wallet;
//# sourceMappingURL=Wallet.js.map