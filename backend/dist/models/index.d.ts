import { sequelize } from '@/config/database';
import { User } from './User';
import { Organization } from './Organization';
import { Project } from './Project';
import { CarbonCredit } from './CarbonCredit';
import { Wallet } from './Wallet';
import { Order } from './Order';
import { Transaction } from './Transaction';
import { Notification } from './Notification';
import { AuditLog } from './AuditLog';
declare const models: {
    User: typeof User;
    Organization: typeof Organization;
    Project: typeof Project;
    CarbonCredit: typeof CarbonCredit;
    Wallet: typeof Wallet;
    Order: typeof Order;
    Transaction: typeof Transaction;
    Notification: typeof Notification;
    AuditLog: typeof AuditLog;
};
export { sequelize };
export { User, Organization, Project, CarbonCredit, Wallet, Order, Transaction, Notification, AuditLog };
export default models;
//# sourceMappingURL=index.d.ts.map