"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Project = void 0;
const sequelize_1 = require("sequelize");
const enums_1 = require("@/types/enums");
class Project extends sequelize_1.Model {
    static init(sequelize) {
        return super.init({
            id: {
                type: sequelize_1.DataTypes.UUID,
                defaultValue: sequelize_1.DataTypes.UUIDV4,
                primaryKey: true,
            },
            name: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            },
            type: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.ProjectType)),
                allowNull: false,
            },
            status: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.ProjectStatus)),
                allowNull: false,
                defaultValue: enums_1.ProjectStatus.PENDING,
            },
            verificationStatus: {
                type: sequelize_1.DataTypes.ENUM(...Object.values(enums_1.VerificationStatus)),
                allowNull: false,
                defaultValue: enums_1.VerificationStatus.PENDING,
            },
            startDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            endDate: {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            },
            location: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            country: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            coordinates: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            area: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
                validate: {
                    min: 0,
                },
            },
            externalProjectId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            registryId: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            standard: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            methodology: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            methodologyVersion: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            estimatedReductions: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
                validate: {
                    min: 0,
                },
            },
            actualReductions: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
                validate: {
                    min: 0,
                },
            },
            verifier: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            validator: {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            },
            images: {
                type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
                allowNull: true,
                defaultValue: [],
            },
            budget: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
                validate: {
                    min: 0,
                },
            },
            roi: {
                type: sequelize_1.DataTypes.FLOAT,
                allowNull: true,
            },
            sdgs: {
                type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
                allowNull: true,
                defaultValue: [],
            },
            tags: {
                type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
                allowNull: true,
                defaultValue: [],
            },
            metadata: {
                type: sequelize_1.DataTypes.JSONB,
                allowNull: true,
            },
            organizationId: {
                type: sequelize_1.DataTypes.UUID,
                allowNull: false,
                references: {
                    model: 'organizations',
                    key: 'id',
                },
            },
        }, {
            sequelize,
            tableName: 'projects',
            underscored: true,
            timestamps: true,
        });
    }
    static associate(models) {
        Project.belongsTo(models.Organization, {
            foreignKey: 'organizationId',
            as: 'organization',
        });
        Project.hasMany(models.CarbonCredit, {
            foreignKey: 'projectId',
            as: 'carbonCredits',
        });
    }
}
exports.Project = Project;
//# sourceMappingURL=Project.js.map