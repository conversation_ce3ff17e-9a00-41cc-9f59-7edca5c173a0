export declare enum UserRole {
    USER = "USER",
    ADMIN = "ADMIN",
    SUPER_ADMIN = "SUPER_ADMIN",
    ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
    COMPLIANCE_OFFICER = "COMPLIANCE_OFFICER",
    AUDITOR = "AUDITOR",
    VERIFIER = "VERIFIER",
    MARKETPLACE_ADMIN = "MARKETPLACE_ADMIN",
    SUPPORT = "SUPPORT",
    DEVELOPER = "DEVELOPER",
    ANALYST = "ANALYST",
    FINANCE = "FINANCE",
    LEGAL = "LEGAL",
    SALES = "SALES",
    MARKETING = "MARKETING",
    OPERATIONS = "OPERATIONS",
    CUSTOM = "CUSTOM"
}
export declare enum OrganizationStatus {
    PENDING = "PENDING",
    ACTIVE = "ACTIVE",
    SUSPENDED = "SUSPENDED",
    INACTIVE = "INACTIVE"
}
export declare enum OrganizationSize {
    STARTUP = "STARTUP",
    SMALL = "SMALL",
    MEDIUM = "MEDIUM",
    LARGE = "LARGE",
    ENTERPRISE = "ENTERPRISE"
}
export declare enum VerificationStatus {
    PENDING = "PENDING",
    IN_REVIEW = "IN_REVIEW",
    VERIFIED = "VERIFIED",
    REJECTED = "REJECTED",
    EXPIRED = "EXPIRED"
}
export declare enum ProjectType {
    RENEWABLE_ENERGY = "RENEWABLE_ENERGY",
    FORESTRY = "FORESTRY",
    AGRICULTURE = "AGRICULTURE",
    WASTE_MANAGEMENT = "WASTE_MANAGEMENT",
    ENERGY_EFFICIENCY = "ENERGY_EFFICIENCY",
    TRANSPORTATION = "TRANSPORTATION",
    INDUSTRIAL = "INDUSTRIAL",
    BLUE_CARBON = "BLUE_CARBON",
    DIRECT_AIR_CAPTURE = "DIRECT_AIR_CAPTURE",
    BIOCHAR = "BIOCHAR",
    OTHER = "OTHER"
}
export declare enum ProjectStatus {
    PENDING = "PENDING",
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED",
    SUSPENDED = "SUSPENDED",
    CANCELLED = "CANCELLED"
}
export declare enum CarbonCreditStatus {
    PENDING = "PENDING",
    VERIFIED = "VERIFIED",
    LISTED = "LISTED",
    SOLD = "SOLD",
    RETIRED = "RETIRED",
    TOKENIZED = "TOKENIZED"
}
export declare enum OrderType {
    BUY = "BUY",
    SELL = "SELL"
}
export declare enum OrderStatus {
    PENDING = "PENDING",
    PARTIAL = "PARTIAL",
    FILLED = "FILLED",
    CANCELLED = "CANCELLED",
    EXPIRED = "EXPIRED"
}
export declare enum TransactionType {
    PURCHASE = "PURCHASE",
    SALE = "SALE",
    TRANSFER = "TRANSFER",
    RETIREMENT = "RETIREMENT",
    TOKENIZATION = "TOKENIZATION",
    FEE = "FEE",
    REFUND = "REFUND",
    DEPOSIT = "DEPOSIT",
    WITHDRAWAL = "WITHDRAWAL"
}
export declare enum TransactionStatus {
    PENDING = "PENDING",
    PROCESSING = "PROCESSING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    REFUNDED = "REFUNDED"
}
export declare enum NotificationType {
    INFO = "INFO",
    WARNING = "WARNING",
    ERROR = "ERROR",
    SUCCESS = "SUCCESS"
}
export declare enum NotificationCategory {
    SYSTEM = "SYSTEM",
    TRANSACTION = "TRANSACTION",
    ORDER = "ORDER",
    VERIFICATION = "VERIFICATION",
    COMPLIANCE = "COMPLIANCE",
    MARKETPLACE = "MARKETPLACE",
    WALLET = "WALLET",
    ORGANIZATION = "ORGANIZATION",
    PROJECT = "PROJECT",
    CARBON_CREDIT = "CARBON_CREDIT"
}
export declare enum AuditAction {
    CREATE = "CREATE",
    UPDATE = "UPDATE",
    DELETE = "DELETE",
    VIEW = "VIEW",
    LOGIN = "LOGIN",
    LOGOUT = "LOGOUT",
    APPROVE = "APPROVE",
    REJECT = "REJECT",
    TRANSFER = "TRANSFER",
    RETIRE = "RETIRE",
    TOKENIZE = "TOKENIZE"
}
export declare enum AuditResource {
    USER = "USER",
    ORGANIZATION = "ORGANIZATION",
    PROJECT = "PROJECT",
    CARBON_CREDIT = "CARBON_CREDIT",
    WALLET = "WALLET",
    ORDER = "ORDER",
    TRANSACTION = "TRANSACTION",
    NOTIFICATION = "NOTIFICATION",
    DOCUMENT = "DOCUMENT",
    COMPLIANCE = "COMPLIANCE"
}
//# sourceMappingURL=enums.d.ts.map