"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditResource = exports.AuditAction = exports.NotificationCategory = exports.NotificationType = exports.TransactionStatus = exports.TransactionType = exports.OrderStatus = exports.OrderType = exports.CarbonCreditStatus = exports.ProjectStatus = exports.ProjectType = exports.VerificationStatus = exports.OrganizationSize = exports.OrganizationStatus = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "USER";
    UserRole["ADMIN"] = "ADMIN";
    UserRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    UserRole["ORGANIZATION_ADMIN"] = "ORGANIZATION_ADMIN";
    UserRole["COMPLIANCE_OFFICER"] = "COMPLIANCE_OFFICER";
    UserRole["AUDITOR"] = "AUDITOR";
    UserRole["VERIFIER"] = "VERIFIER";
    UserRole["MARKETPLACE_ADMIN"] = "MARKETPLACE_ADMIN";
    UserRole["SUPPORT"] = "SUPPORT";
    UserRole["DEVELOPER"] = "DEVELOPER";
    UserRole["ANALYST"] = "ANALYST";
    UserRole["FINANCE"] = "FINANCE";
    UserRole["LEGAL"] = "LEGAL";
    UserRole["SALES"] = "SALES";
    UserRole["MARKETING"] = "MARKETING";
    UserRole["OPERATIONS"] = "OPERATIONS";
    UserRole["CUSTOM"] = "CUSTOM";
})(UserRole || (exports.UserRole = UserRole = {}));
var OrganizationStatus;
(function (OrganizationStatus) {
    OrganizationStatus["PENDING"] = "PENDING";
    OrganizationStatus["ACTIVE"] = "ACTIVE";
    OrganizationStatus["SUSPENDED"] = "SUSPENDED";
    OrganizationStatus["INACTIVE"] = "INACTIVE";
})(OrganizationStatus || (exports.OrganizationStatus = OrganizationStatus = {}));
var OrganizationSize;
(function (OrganizationSize) {
    OrganizationSize["STARTUP"] = "STARTUP";
    OrganizationSize["SMALL"] = "SMALL";
    OrganizationSize["MEDIUM"] = "MEDIUM";
    OrganizationSize["LARGE"] = "LARGE";
    OrganizationSize["ENTERPRISE"] = "ENTERPRISE";
})(OrganizationSize || (exports.OrganizationSize = OrganizationSize = {}));
var VerificationStatus;
(function (VerificationStatus) {
    VerificationStatus["PENDING"] = "PENDING";
    VerificationStatus["IN_REVIEW"] = "IN_REVIEW";
    VerificationStatus["VERIFIED"] = "VERIFIED";
    VerificationStatus["REJECTED"] = "REJECTED";
    VerificationStatus["EXPIRED"] = "EXPIRED";
})(VerificationStatus || (exports.VerificationStatus = VerificationStatus = {}));
var ProjectType;
(function (ProjectType) {
    ProjectType["RENEWABLE_ENERGY"] = "RENEWABLE_ENERGY";
    ProjectType["FORESTRY"] = "FORESTRY";
    ProjectType["AGRICULTURE"] = "AGRICULTURE";
    ProjectType["WASTE_MANAGEMENT"] = "WASTE_MANAGEMENT";
    ProjectType["ENERGY_EFFICIENCY"] = "ENERGY_EFFICIENCY";
    ProjectType["TRANSPORTATION"] = "TRANSPORTATION";
    ProjectType["INDUSTRIAL"] = "INDUSTRIAL";
    ProjectType["BLUE_CARBON"] = "BLUE_CARBON";
    ProjectType["DIRECT_AIR_CAPTURE"] = "DIRECT_AIR_CAPTURE";
    ProjectType["BIOCHAR"] = "BIOCHAR";
    ProjectType["OTHER"] = "OTHER";
})(ProjectType || (exports.ProjectType = ProjectType = {}));
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PENDING"] = "PENDING";
    ProjectStatus["ACTIVE"] = "ACTIVE";
    ProjectStatus["COMPLETED"] = "COMPLETED";
    ProjectStatus["SUSPENDED"] = "SUSPENDED";
    ProjectStatus["CANCELLED"] = "CANCELLED";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
var CarbonCreditStatus;
(function (CarbonCreditStatus) {
    CarbonCreditStatus["PENDING"] = "PENDING";
    CarbonCreditStatus["VERIFIED"] = "VERIFIED";
    CarbonCreditStatus["LISTED"] = "LISTED";
    CarbonCreditStatus["SOLD"] = "SOLD";
    CarbonCreditStatus["RETIRED"] = "RETIRED";
    CarbonCreditStatus["TOKENIZED"] = "TOKENIZED";
})(CarbonCreditStatus || (exports.CarbonCreditStatus = CarbonCreditStatus = {}));
var OrderType;
(function (OrderType) {
    OrderType["BUY"] = "BUY";
    OrderType["SELL"] = "SELL";
})(OrderType || (exports.OrderType = OrderType = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "PENDING";
    OrderStatus["PARTIAL"] = "PARTIAL";
    OrderStatus["FILLED"] = "FILLED";
    OrderStatus["CANCELLED"] = "CANCELLED";
    OrderStatus["EXPIRED"] = "EXPIRED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var TransactionType;
(function (TransactionType) {
    TransactionType["PURCHASE"] = "PURCHASE";
    TransactionType["SALE"] = "SALE";
    TransactionType["TRANSFER"] = "TRANSFER";
    TransactionType["RETIREMENT"] = "RETIREMENT";
    TransactionType["TOKENIZATION"] = "TOKENIZATION";
    TransactionType["FEE"] = "FEE";
    TransactionType["REFUND"] = "REFUND";
    TransactionType["DEPOSIT"] = "DEPOSIT";
    TransactionType["WITHDRAWAL"] = "WITHDRAWAL";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "PENDING";
    TransactionStatus["PROCESSING"] = "PROCESSING";
    TransactionStatus["COMPLETED"] = "COMPLETED";
    TransactionStatus["FAILED"] = "FAILED";
    TransactionStatus["CANCELLED"] = "CANCELLED";
    TransactionStatus["REFUNDED"] = "REFUNDED";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
var NotificationType;
(function (NotificationType) {
    NotificationType["INFO"] = "INFO";
    NotificationType["WARNING"] = "WARNING";
    NotificationType["ERROR"] = "ERROR";
    NotificationType["SUCCESS"] = "SUCCESS";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationCategory;
(function (NotificationCategory) {
    NotificationCategory["SYSTEM"] = "SYSTEM";
    NotificationCategory["TRANSACTION"] = "TRANSACTION";
    NotificationCategory["ORDER"] = "ORDER";
    NotificationCategory["VERIFICATION"] = "VERIFICATION";
    NotificationCategory["COMPLIANCE"] = "COMPLIANCE";
    NotificationCategory["MARKETPLACE"] = "MARKETPLACE";
    NotificationCategory["WALLET"] = "WALLET";
    NotificationCategory["ORGANIZATION"] = "ORGANIZATION";
    NotificationCategory["PROJECT"] = "PROJECT";
    NotificationCategory["CARBON_CREDIT"] = "CARBON_CREDIT";
})(NotificationCategory || (exports.NotificationCategory = NotificationCategory = {}));
var AuditAction;
(function (AuditAction) {
    AuditAction["CREATE"] = "CREATE";
    AuditAction["UPDATE"] = "UPDATE";
    AuditAction["DELETE"] = "DELETE";
    AuditAction["VIEW"] = "VIEW";
    AuditAction["LOGIN"] = "LOGIN";
    AuditAction["LOGOUT"] = "LOGOUT";
    AuditAction["APPROVE"] = "APPROVE";
    AuditAction["REJECT"] = "REJECT";
    AuditAction["TRANSFER"] = "TRANSFER";
    AuditAction["RETIRE"] = "RETIRE";
    AuditAction["TOKENIZE"] = "TOKENIZE";
})(AuditAction || (exports.AuditAction = AuditAction = {}));
var AuditResource;
(function (AuditResource) {
    AuditResource["USER"] = "USER";
    AuditResource["ORGANIZATION"] = "ORGANIZATION";
    AuditResource["PROJECT"] = "PROJECT";
    AuditResource["CARBON_CREDIT"] = "CARBON_CREDIT";
    AuditResource["WALLET"] = "WALLET";
    AuditResource["ORDER"] = "ORDER";
    AuditResource["TRANSACTION"] = "TRANSACTION";
    AuditResource["NOTIFICATION"] = "NOTIFICATION";
    AuditResource["DOCUMENT"] = "DOCUMENT";
    AuditResource["COMPLIANCE"] = "COMPLIANCE";
})(AuditResource || (exports.AuditResource = AuditResource = {}));
//# sourceMappingURL=enums.js.map