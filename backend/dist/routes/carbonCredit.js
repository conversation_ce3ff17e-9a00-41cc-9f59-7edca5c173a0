"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const sequelize_1 = require("sequelize");
const CarbonCredit_1 = require("@/models/CarbonCredit");
const Project_1 = require("@/models/Project");
const User_1 = require("@/models/User");
const Organization_1 = require("@/models/Organization");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const logger_1 = require("@/utils/logger");
const enums_1 = require("@/types/enums");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
const carbonCreditValidation = [
    (0, express_validator_1.body)('name').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('description').optional().isLength({ max: 1000 }).trim(),
    (0, express_validator_1.body)('quantity').isFloat({ min: 0.01 }),
    (0, express_validator_1.body)('price').isFloat({ min: 0.01 }),
    (0, express_validator_1.body)('vintage').isInt({ min: 1990, max: new Date().getFullYear() + 10 }),
    (0, express_validator_1.body)('standard').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('methodology').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('projectId').isUUID(),
    (0, express_validator_1.body)('minPurchaseQuantity').optional().isFloat({ min: 0.01 }),
    (0, express_validator_1.body)('location').optional().isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('country').optional().isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('externalProjectId').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('serialNumber').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('verificationBody').optional().isLength({ min: 2 }).trim(),
];
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, status, vintage, standard, methodology, minPrice, maxPrice, search, organizationId } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const whereClause = {};
    if (status) {
        whereClause.status = status;
    }
    if (vintage) {
        whereClause.vintage = vintage;
    }
    if (standard) {
        whereClause.standard = standard;
    }
    if (methodology) {
        whereClause.methodology = methodology;
    }
    if (minPrice || maxPrice) {
        whereClause.price = {};
        if (minPrice)
            whereClause.price[sequelize_1.Op.gte] = Number(minPrice);
        if (maxPrice)
            whereClause.price[sequelize_1.Op.lte] = Number(maxPrice);
    }
    if (search) {
        whereClause[sequelize_1.Op.or] = [
            { name: { [sequelize_1.Op.iLike]: `%${search}%` } },
            { description: { [sequelize_1.Op.iLike]: `%${search}%` } }
        ];
    }
    if (organizationId) {
        whereClause.organizationId = organizationId;
    }
    const { rows: carbonCredits, count } = await CarbonCredit_1.CarbonCredit.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['createdAt', 'DESC']],
        include: [
            { model: Project_1.Project, as: 'project' },
            { model: User_1.User, as: 'user', attributes: ['id', 'name', 'email'] },
            { model: Organization_1.Organization, as: 'organization', attributes: ['id', 'name'] }
        ]
    });
    res.json({
        carbonCredits,
        pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            pages: Math.ceil(count / Number(limit))
        }
    });
}));
router.get('/organization', auth_1.requireOrganization, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const carbonCredits = await CarbonCredit_1.CarbonCredit.findAll({
        where: {
            organizationId: req.user.organizationId
        },
        order: [['createdAt', 'DESC']],
        include: [
            { model: Project_1.Project, as: 'project' },
            { model: User_1.User, as: 'user', attributes: ['id', 'name', 'email'] }
        ]
    });
    res.json({ carbonCredits });
}));
router.post('/', auth_1.requireOrganization, carbonCreditValidation, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const { name, description, quantity, price, vintage, standard, methodology, projectId, minPurchaseQuantity, location, country, externalProjectId, serialNumber, verificationBody, images, metadata } = req.body;
    const project = await Project_1.Project.findOne({
        where: {
            id: projectId,
            organizationId: req.user.organizationId
        }
    });
    if (!project) {
        throw new errorHandler_1.CustomError('Project not found or access denied', 404);
    }
    const carbonCredit = await CarbonCredit_1.CarbonCredit.create({
        name,
        description,
        quantity,
        availableQuantity: quantity,
        retiredQuantity: 0,
        price,
        minPurchaseQuantity,
        vintage,
        standard,
        methodology,
        location,
        country,
        externalProjectId,
        serialNumber,
        verificationBody,
        status: enums_1.CarbonCreditStatus.PENDING,
        verificationStatus: enums_1.VerificationStatus.PENDING,
        images: images || [],
        metadata,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        projectId,
    });
    logger_1.logger.info(`Carbon credit ${carbonCredit.id} created by user ${req.user.id}`);
    res.status(201).json({
        carbonCredit,
        message: 'Carbon credit created successfully'
    });
}));
exports.default = router;
//# sourceMappingURL=carbonCredit.js.map