"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const router = (0, express_1.Router)();
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const startTime = Date.now();
    const checks = {
        database: { status: "unknown" },
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.APP_VERSION || '1.0.0',
    };
    try {
        await database_1.sequelize.authenticate();
        checks.database = {
            status: "healthy",
            responseTime: `${Date.now() - startTime}ms`,
        };
    }
    catch (error) {
        logger_1.logger.error("Database health check failed:", error);
        checks.database = {
            status: "unhealthy",
            error: "Database connection failed",
            responseTime: `${Date.now() - startTime}ms`,
        };
    }
    const isHealthy = checks.database.status === "healthy";
    logger_1.logger.info("Health check completed", {
        healthy: isHealthy,
        responseTime: `${Date.now() - startTime}ms`,
    });
    res.status(isHealthy ? 200 : 503).json({
        status: isHealthy ? "healthy" : "unhealthy",
        checks,
        responseTime: `${Date.now() - startTime}ms`,
    });
}));
exports.default = router;
//# sourceMappingURL=health.js.map