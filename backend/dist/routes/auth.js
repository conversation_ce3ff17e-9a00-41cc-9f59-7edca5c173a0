"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const User_1 = require("@/models/User");
const jwt_1 = require("@/utils/jwt");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const logger_1 = require("@/utils/logger");
const enums_1 = require("@/types/enums");
const router = (0, express_1.Router)();
const loginValidation = [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 6 }),
];
const registerValidation = [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
    (0, express_validator_1.body)('name').isLength({ min: 2 }).trim(),
];
router.post('/login', loginValidation, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const { email, password } = req.body;
    const user = await User_1.User.findOne({
        where: { email },
        include: ['organization']
    });
    if (!user) {
        throw new errorHandler_1.CustomError('Invalid credentials', 401);
    }
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
        throw new errorHandler_1.CustomError('Invalid credentials', 401);
    }
    await user.update({ lastLoginAt: new Date() });
    const accessToken = (0, jwt_1.generateAccessToken)({
        id: user.id,
        email: user.email,
        role: user.role,
        organizationId: user.organizationId || undefined,
    });
    const refreshToken = (0, jwt_1.generateRefreshToken)({
        id: user.id,
        email: user.email,
    });
    logger_1.logger.info(`User ${user.id} logged in successfully`);
    res.json({
        user: user.toJSON(),
        accessToken,
        refreshToken,
    });
}));
router.post('/register', registerValidation, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const { email, password, name } = req.body;
    const existingUser = await User_1.User.findOne({ where: { email } });
    if (existingUser) {
        throw new errorHandler_1.CustomError('User already exists', 409);
    }
    const user = await User_1.User.create({
        email,
        password,
        name,
        role: enums_1.UserRole.USER,
        twoFactorEnabled: false,
    });
    logger_1.logger.info(`New user registered: ${user.id}`);
    res.status(201).json({
        user: user.toJSON(),
        message: 'User created successfully. Please verify your email.',
    });
}));
router.post('/refresh', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        throw new errorHandler_1.CustomError('Refresh token required', 400);
    }
    try {
        const payload = (0, jwt_1.verifyRefreshToken)(refreshToken);
        const user = await User_1.User.findByPk(payload.id);
        if (!user) {
            throw new errorHandler_1.CustomError('User not found', 404);
        }
        const accessToken = (0, jwt_1.generateAccessToken)({
            id: user.id,
            email: user.email,
            role: user.role,
            organizationId: user.organizationId || undefined,
        });
        res.json({ accessToken });
    }
    catch (error) {
        throw new errorHandler_1.CustomError('Invalid refresh token', 401);
    }
}));
router.post('/logout', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info(`User ${req.user?.id} logged out`);
    res.json({ message: 'Logged out successfully' });
}));
exports.default = router;
//# sourceMappingURL=auth.js.map