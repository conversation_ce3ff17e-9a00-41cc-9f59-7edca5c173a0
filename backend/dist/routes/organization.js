"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const sequelize_1 = require("sequelize");
const Organization_1 = require("@/models/Organization");
const User_1 = require("@/models/User");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const logger_1 = require("@/utils/logger");
const enums_1 = require("@/types/enums");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
const organizationValidation = [
    (0, express_validator_1.body)('name').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('industry').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('size').isIn(Object.values(enums_1.OrganizationSize)),
    (0, express_validator_1.body)('country').isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('description').optional().isLength({ max: 1000 }).trim(),
    (0, express_validator_1.body)('website').optional().isURL(),
    (0, express_validator_1.body)('legalName').optional().isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('registrationNumber').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('taxId').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('address').optional().isLength({ max: 500 }).trim(),
    (0, express_validator_1.body)('city').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('state').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('postalCode').optional().isLength({ min: 1 }).trim(),
    (0, express_validator_1.body)('phoneNumber').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('foundedYear').optional().isInt({ min: 1800, max: new Date().getFullYear() }),
    (0, express_validator_1.body)('primaryContact').optional().isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('primaryContactEmail').optional().isEmail(),
    (0, express_validator_1.body)('primaryContactPhone').optional().isMobilePhone('any'),
];
router.post('/', organizationValidation, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const user = await User_1.User.findByPk(req.user.id, {
        include: ['organization']
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    if (user.organization) {
        throw new errorHandler_1.CustomError('You already have an organization', 409);
    }
    const { name, description, website, logo, legalName, registrationNumber, taxId, country, address, city, state, postalCode, phoneNumber, industry, size, foundedYear, primaryContact, primaryContactEmail, primaryContactPhone } = req.body;
    const organization = await Organization_1.Organization.create({
        name,
        description,
        website,
        logo,
        status: enums_1.OrganizationStatus.PENDING,
        verificationStatus: enums_1.VerificationStatus.PENDING,
        legalName,
        registrationNumber,
        taxId,
        country,
        address,
        city,
        state,
        postalCode,
        phoneNumber,
        industry,
        size,
        foundedYear,
        primaryContact,
        primaryContactEmail,
        primaryContactPhone,
        transactionFeeRate: 0.01,
        listingFeeRate: 0.005,
        subscriptionFee: 0.0,
    });
    await user.update({
        organizationId: organization.id,
        role: enums_1.UserRole.ORGANIZATION_ADMIN,
    });
    logger_1.logger.info(`Organization ${organization.id} created by user ${user.id}`);
    res.status(201).json({
        organization,
        message: 'Organization created successfully'
    });
}));
router.get('/', (0, auth_1.authorize)(enums_1.UserRole.ADMIN, enums_1.UserRole.SUPER_ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, status, search } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const whereClause = {};
    if (status) {
        whereClause.status = status;
    }
    if (search) {
        whereClause.name = {
            [sequelize_1.Op.iLike]: `%${search}%`
        };
    }
    const { rows: organizations, count } = await Organization_1.Organization.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['createdAt', 'DESC']],
        include: ['users']
    });
    res.json({
        organizations,
        pagination: {
            page: Number(page),
            limit: Number(limit),
            total: count,
            pages: Math.ceil(count / Number(limit))
        }
    });
}));
exports.default = router;
//# sourceMappingURL=organization.js.map