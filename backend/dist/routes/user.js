"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const User_1 = require("@/models/User");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/me', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await User_1.User.findByPk(req.user.id, {
        include: ['organization'],
        attributes: { exclude: ['password'] }
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    res.json({ user });
}));
router.put('/me', [
    (0, express_validator_1.body)('name').optional().isLength({ min: 2 }).trim(),
    (0, express_validator_1.body)('jobTitle').optional().isLength({ max: 100 }).trim(),
    (0, express_validator_1.body)('phoneNumber').optional().isMobilePhone('any'),
    (0, express_validator_1.body)('bio').optional().isLength({ max: 500 }).trim(),
    (0, express_validator_1.body)('preferences').optional().isObject(),
], (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const { name, jobTitle, phoneNumber, bio, preferences } = req.body;
    const user = await User_1.User.findByPk(req.user.id);
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    const updateData = {};
    if (name !== undefined)
        updateData.name = name;
    if (jobTitle !== undefined)
        updateData.jobTitle = jobTitle;
    if (phoneNumber !== undefined)
        updateData.phoneNumber = phoneNumber;
    if (bio !== undefined)
        updateData.bio = bio;
    if (preferences !== undefined)
        updateData.preferences = preferences;
    await user.update(updateData);
    logger_1.logger.info(`User ${user.id} updated their profile`);
    res.json({
        user: user.toJSON(),
        message: 'Profile updated successfully'
    });
}));
router.get('/organization', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await User_1.User.findByPk(req.user.id, {
        include: ['organization']
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    res.json({
        organization: user.organization || null
    });
}));
router.get('/notification-preferences', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await User_1.User.findByPk(req.user.id, {
        attributes: ['preferences']
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    const notificationPreferences = user.preferences?.notifications || {
        email: true,
        push: true,
        sms: false,
        categories: {
            transactions: true,
            orders: true,
            verification: true,
            compliance: true,
            marketplace: true,
            system: true
        }
    };
    res.json({ notificationPreferences });
}));
router.put('/notification-preferences', [
    (0, express_validator_1.body)('email').optional().isBoolean(),
    (0, express_validator_1.body)('push').optional().isBoolean(),
    (0, express_validator_1.body)('sms').optional().isBoolean(),
    (0, express_validator_1.body)('categories').optional().isObject(),
], (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.CustomError('Validation failed', 400);
    }
    const user = await User_1.User.findByPk(req.user.id);
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    const currentPreferences = user.preferences || {};
    const notificationPreferences = {
        ...currentPreferences.notifications,
        ...req.body
    };
    await user.update({
        preferences: {
            ...currentPreferences,
            notifications: notificationPreferences
        }
    });
    logger_1.logger.info(`User ${user.id} updated notification preferences`);
    res.json({
        notificationPreferences,
        message: 'Notification preferences updated successfully'
    });
}));
exports.default = router;
//# sourceMappingURL=user.js.map