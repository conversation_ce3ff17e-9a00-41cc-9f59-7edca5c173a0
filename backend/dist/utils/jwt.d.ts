import { UserRole } from '@/types/enums';
export interface JWTPayload {
    id: string;
    email: string;
    role: UserRole;
    organizationId?: string;
    iat?: number;
    exp?: number;
}
export interface RefreshTokenPayload {
    id: string;
    email: string;
    tokenVersion?: number;
    iat?: number;
    exp?: number;
}
export declare const generateAccessToken: (payload: Omit<JWTPayload, "iat" | "exp">) => string;
export declare const generateRefreshToken: (payload: Omit<RefreshTokenPayload, "iat" | "exp">) => string;
export declare const verifyAccessToken: (token: string) => JWTPayload;
export declare const verifyRefreshToken: (token: string) => RefreshTokenPayload;
export declare const decodeToken: (token: string) => JWTPayload | null;
//# sourceMappingURL=jwt.d.ts.map