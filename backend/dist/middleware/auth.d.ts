import { Request, Response, NextFunction } from 'express';
import { JWTPayload } from '@/utils/jwt';
import { UserRole } from '@/types/enums';
declare global {
    namespace Express {
        interface Request {
            user?: JWTPayload;
        }
    }
}
export declare const authenticate: (req: Request, res: Response, next: NextFunction) => void;
export declare const authorize: (...roles: UserRole[]) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireOrganization: (req: Request, res: Response, next: NextFunction) => void;
export declare const requireOrganizationAccess: (req: Request, res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.d.ts.map