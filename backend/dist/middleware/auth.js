"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireOrganizationAccess = exports.requireOrganization = exports.authorize = exports.authenticate = void 0;
const jwt_1 = require("@/utils/jwt");
const errorHandler_1 = require("./errorHandler");
const enums_1 = require("@/types/enums");
const authenticate = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new errorHandler_1.CustomError('Access token required', 401);
        }
        const token = authHeader.substring(7);
        const payload = (0, jwt_1.verifyAccessToken)(token);
        req.user = payload;
        next();
    }
    catch (error) {
        next(new errorHandler_1.CustomError('Invalid or expired token', 401));
    }
};
exports.authenticate = authenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new errorHandler_1.CustomError('Authentication required', 401));
        }
        if (roles.length > 0 && !roles.includes(req.user.role)) {
            return next(new errorHandler_1.CustomError('Insufficient permissions', 403));
        }
        next();
    };
};
exports.authorize = authorize;
const requireOrganization = (req, res, next) => {
    if (!req.user) {
        return next(new errorHandler_1.CustomError('Authentication required', 401));
    }
    if (!req.user.organizationId) {
        return next(new errorHandler_1.CustomError('Organization membership required', 403));
    }
    next();
};
exports.requireOrganization = requireOrganization;
const requireOrganizationAccess = (req, res, next) => {
    if (!req.user) {
        return next(new errorHandler_1.CustomError('Authentication required', 401));
    }
    const organizationId = req.params.organizationId || req.body.organizationId || req.query.organizationId;
    if (!organizationId) {
        return next(new errorHandler_1.CustomError('Organization ID required', 400));
    }
    const hasAccess = req.user.organizationId === organizationId ||
        req.user.role === enums_1.UserRole.ADMIN ||
        req.user.role === enums_1.UserRole.SUPER_ADMIN;
    if (!hasAccess) {
        return next(new errorHandler_1.CustomError('Access denied to this organization', 403));
    }
    next();
};
exports.requireOrganizationAccess = requireOrganizationAccess;
const optionalAuth = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            const payload = (0, jwt_1.verifyAccessToken)(token);
            req.user = payload;
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map