"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testConnection = exports.sequelize = void 0;
const sequelize_1 = require("sequelize");
const logger_1 = require("@/utils/logger");
const { DATABASE_URL, DATABASE_HOST = 'localhost', DATABASE_PORT = '5432', DATABASE_NAME = 'carbon_exchange', DATABASE_USER = 'postgres', DATABASE_PASSWORD = 'postgres', NODE_ENV = 'development' } = process.env;
exports.sequelize = new sequelize_1.Sequelize(DATABASE_URL || `postgres://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}`, {
    dialect: 'postgres',
    logging: NODE_ENV === 'development' ? (msg) => logger_1.logger.debug(msg) : false,
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
    },
    define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true,
    },
    dialectOptions: {
        ssl: NODE_ENV === 'production' ? {
            require: true,
            rejectUnauthorized: false
        } : false
    }
});
const testConnection = async () => {
    try {
        await exports.sequelize.authenticate();
        logger_1.logger.info('Database connection has been established successfully.');
        return true;
    }
    catch (error) {
        logger_1.logger.error('Unable to connect to the database:', error);
        return false;
    }
};
exports.testConnection = testConnection;
exports.default = exports.sequelize;
//# sourceMappingURL=database.js.map