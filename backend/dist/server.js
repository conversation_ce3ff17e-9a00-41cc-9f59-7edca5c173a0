"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler_1 = require("@/middleware/notFoundHandler");
const logger_1 = require("@/utils/logger");
const database_1 = require("@/config/database");
const auth_1 = __importDefault(require("@/routes/auth"));
const user_1 = __importDefault(require("@/routes/user"));
const organization_1 = __importDefault(require("@/routes/organization"));
const carbonCredit_1 = __importDefault(require("@/routes/carbonCredit"));
const wallet_1 = __importDefault(require("@/routes/wallet"));
const marketplace_1 = __importDefault(require("@/routes/marketplace"));
const order_1 = __importDefault(require("@/routes/order"));
const notification_1 = __importDefault(require("@/routes/notification"));
const analytics_1 = __importDefault(require("@/routes/analytics"));
const compliance_1 = __importDefault(require("@/routes/compliance"));
const admin_1 = __importDefault(require("@/routes/admin"));
const health_1 = __importDefault(require("@/routes/health"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
});
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
}));
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined', { stream: { write: (message) => logger_1.logger.info(message.trim()) } }));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter);
app.use('/api/health', health_1.default);
app.use('/api/auth', auth_1.default);
app.use('/api/user', user_1.default);
app.use('/api/organizations', organization_1.default);
app.use('/api/carbon-credits', carbonCredit_1.default);
app.use('/api/wallet', wallet_1.default);
app.use('/api/marketplace', marketplace_1.default);
app.use('/api/orders', order_1.default);
app.use('/api/notifications', notification_1.default);
app.use('/api/analytics', analytics_1.default);
app.use('/api/compliance', compliance_1.default);
app.use('/api/admin', admin_1.default);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
async function startServer() {
    try {
        await database_1.sequelize.authenticate();
        logger_1.logger.info('Database connection has been established successfully.');
        if (process.env.NODE_ENV === 'development') {
            await database_1.sequelize.sync({ alter: true });
            logger_1.logger.info('Database models synchronized.');
        }
        app.listen(PORT, () => {
            logger_1.logger.info(`Server is running on port ${PORT}`);
            logger_1.logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    }
    catch (error) {
        logger_1.logger.error('Unable to start server:', error);
        process.exit(1);
    }
}
process.on('SIGTERM', async () => {
    logger_1.logger.info('SIGTERM received, shutting down gracefully');
    await database_1.sequelize.close();
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger_1.logger.info('SIGINT received, shutting down gracefully');
    await database_1.sequelize.close();
    process.exit(0);
});
startServer();
exports.default = app;
//# sourceMappingURL=server.js.map