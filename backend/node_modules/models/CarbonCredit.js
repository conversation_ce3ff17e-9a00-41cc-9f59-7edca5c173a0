const { DataTypes } = require('sequelize');
const { CarbonCreditStatus, VerificationStatus } = require('../types/enums');

module.exports = (sequelize) => {
  const CarbonCredit = sequelize.define('CarbonCredit', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.FLOAT,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    availableQuantity: {
      type: DataTypes.FLOAT,
      allowNull: false,
      field: 'available_quantity',
      validate: {
        min: 0,
      },
    },
    retiredQuantity: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
      field: 'retired_quantity',
      validate: {
        min: 0,
      },
    },
    price: {
      type: DataTypes.FLOAT,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    vintage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1990,
        max: new Date().getFullYear() + 10,
      },
    },
    standard: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    methodology: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(...Object.values(CarbonCreditStatus)),
      allowNull: false,
      defaultValue: CarbonCreditStatus.PENDING,
    },
    verificationStatus: {
      type: DataTypes.ENUM(...Object.values(VerificationStatus)),
      allowNull: false,
      defaultValue: VerificationStatus.PENDING,
      field: 'verification_status',
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id',
      },
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'organization_id',
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'project_id',
      references: {
        model: 'projects',
        key: 'id',
      },
    },
  }, {
    tableName: 'carbon_credits',
    underscored: true,
    timestamps: true,
  });

  return CarbonCredit;
};
