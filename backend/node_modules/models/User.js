const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { UserRole } = require('../types/enums');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    password: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    role: {
      type: DataTypes.ENUM(...Object.values(UserRole)),
      allowNull: false,
      defaultValue: UserRole.USER,
    },
    emailVerified: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'email_verified',
    },
    jobTitle: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'job_title',
    },
    departmentName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'department_name',
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'phone_number',
    },
    profileImage: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'profile_image',
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_login_at',
    },
    twoFactorEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'two_factor_enabled',
    },
    preferences: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'organization_id',
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
  }, {
    tableName: 'users',
    underscored: true,
    timestamps: true,
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          const saltRounds = 12;
          user.password = await bcrypt.hash(user.password, saltRounds);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password') && user.password) {
          const saltRounds = 12;
          user.password = await bcrypt.hash(user.password, saltRounds);
        }
      },
    },
  });

  // Instance methods
  User.prototype.validatePassword = async function(password) {
    if (!this.password) return false;
    return bcrypt.compare(password, this.password);
  };

  User.prototype.setPassword = async function(password) {
    const saltRounds = 12;
    this.password = await bcrypt.hash(password, saltRounds);
  };

  User.prototype.toJSON = function() {
    const values = { ...this.get() };
    delete values.password;
    return values;
  };

  // Define associations (disabled for now)
  // User.associate = (models) => {
  //   User.belongsTo(models.Organization, {
  //     foreignKey: 'organizationId',
  //     as: 'organization',
  //   });
  // };

  return User;
};
