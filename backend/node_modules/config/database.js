const { Sequelize } = require('sequelize');
const { logger } = require('../utils/logger');

const {
  DATABASE_URL,
  DATABASE_HOST = 'localhost',
  DATABASE_PORT = '5432',
  DATABASE_NAME = 'carbon_exchange',
  DATABASE_USER = 'postgres',
  DATABASE_PASSWORD = 'postgres',
  NODE_ENV = 'development'
} = process.env;

// Create Sequelize instance
const sequelize = new Sequelize(
  DATABASE_URL || `postgres://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}`,
  {
    dialect: 'postgres',
    logging: NODE_ENV === 'development' ? (msg) => logger.debug(msg) : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
    dialectOptions: {
      ssl: NODE_ENV === 'production' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    }
  }
);

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully.');
    return true;
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    return false;
  }
};

module.exports = { sequelize, testConnection };
