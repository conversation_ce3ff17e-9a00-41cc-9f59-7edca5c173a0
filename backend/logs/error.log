{"level": "error", "message": "Unable to start server:", "name": "SequelizeConnectionRefusedError", "original": {"code": "ECONNREFUSED"}, "parent": {"code": "ECONNREFUSED"}, "service": "carbonx-backend", "stack": "SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)", "timestamp": "2025-06-26T07:57:06.779Z"}