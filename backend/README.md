# CarbonX Backend API

This is the Express.js backend API for the CarbonX platform, providing RESTful endpoints for carbon credit trading, organization management, and user authentication.

## Architecture

- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT-based authentication
- **Validation**: express-validator
- **Logging**: Winston
- **Security**: Helmet, CORS, Rate limiting

## Project Structure

```
backend/
├── src/
│   ├── config/          # Database and app configuration
│   ├── controllers/     # Route controllers (future)
│   ├── middleware/      # Custom middleware
│   ├── models/          # Sequelize models
│   ├── routes/          # API route definitions
│   ├── services/        # Business logic services (future)
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── migrations/      # Database migrations
│   ├── seeders/         # Database seeders
│   └── server.ts        # Main application entry point
├── scripts/             # Deployment and utility scripts
├── logs/                # Application logs
└── dist/                # Compiled JavaScript (production)
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout

### Users
- `GET /api/user/me` - Get current user
- `PUT /api/user/me` - Update current user
- `GET /api/user/organization` - Get user's organization
- `GET /api/user/notification-preferences` - Get notification preferences
- `PUT /api/user/notification-preferences` - Update notification preferences

### Organizations
- `POST /api/organizations` - Create organization
- `GET /api/organizations` - List organizations (admin only)

### Carbon Credits
- `GET /api/carbon-credits` - List carbon credits with filtering
- `GET /api/carbon-credits/organization` - Get organization's carbon credits
- `POST /api/carbon-credits` - Create carbon credit

### Health
- `GET /api/health` - Health check endpoint

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/carbon_exchange
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=carbon_exchange
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration
EMAIL_SERVER=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# Blockchain Configuration
ALCHEMY_API_KEY=your-alchemy-api-key
ALCHEMY_NETWORK=eth-sepolia
# ... other blockchain configs

# Logging
LOG_LEVEL=info
```

## Development

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- npm or pnpm

### Setup
1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. Run database migrations:
   ```bash
   npm run db:migrate
   ```

4. Start development server:
   ```bash
   npm run dev
   ```

### Available Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Run database seeders
- `npm run lint` - Run ESLint

## Docker Development

Use Docker Compose for full-stack development:

```bash
# Start all services (backend, frontend, database)
docker compose up backend-dev frontend-dev db

# Or start just the backend with database
docker compose up backend-dev db
```

## Production Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Run database migrations:
   ```bash
   npm run db:migrate
   ```

3. Start the production server:
   ```bash
   npm start
   ```

Or use Docker:
```bash
docker compose -f docker-compose.prod.yml up -d
```

## Authentication

The API uses JWT-based authentication. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

Access tokens expire in 7 days (configurable). Use the refresh token to get a new access token:

```bash
POST /api/auth/refresh
{
  "refreshToken": "<refresh_token>"
}
```

## Database

The application uses PostgreSQL with Sequelize ORM. Database schema is managed through migrations in `src/migrations/`.

### Running Migrations
```bash
# Development
npm run db:migrate

# Production
npm run db:migrate
```

### Creating Migrations
```bash
npx sequelize-cli migration:generate --name migration-name
```

## Logging

Application logs are written to:
- `logs/combined.log` - All logs
- `logs/error.log` - Error logs only
- Console (development only)

Log level can be configured via `LOG_LEVEL` environment variable.

## Security

- Helmet.js for security headers
- CORS configuration
- Rate limiting (100 requests per 15 minutes per IP)
- Input validation with express-validator
- Password hashing with bcrypt
- JWT token expiration

## Contributing

1. Follow TypeScript and ESLint conventions
2. Write tests for new features
3. Update documentation
4. Use conventional commit messages
