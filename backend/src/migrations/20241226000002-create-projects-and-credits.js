'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Projects table
    await queryInterface.createTable('projects', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      type: {
        type: Sequelize.ENUM(
          'RENEWABLE_ENERGY', 'FORESTRY', 'AGRICULTURE', 'WASTE_MANAGEMENT',
          'ENERGY_EFFICIENCY', 'TRANSPORTATION', 'INDUSTRIAL', 'BLUE_CARBON',
          'DIRECT_AIR_CAPTURE', 'BIOCHAR', 'OTHER'
        ),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'ACTIVE', 'COMPLETED', 'SUSPENDED', 'CANCELLED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      verification_status: {
        type: Sequelize.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED', 'EXPIRED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      start_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      end_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      location: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      coordinates: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      area: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      external_project_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      registry_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      standard: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      methodology: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      methodology_version: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      estimated_reductions: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      actual_reductions: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      verifier: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      validator: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      images: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: [],
      },
      budget: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      roi: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      sdgs: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: [],
      },
      tags: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: [],
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      organization_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Create Carbon Credits table
    await queryInterface.createTable('carbon_credits', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      quantity: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      available_quantity: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      retired_quantity: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      price: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      min_purchase_quantity: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      vintage: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      standard: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      methodology: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      location: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      external_project_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      serial_number: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      certification_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      expiration_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      verification_body: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'VERIFIED', 'LISTED', 'SOLD', 'RETIRED', 'TOKENIZED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      verification_status: {
        type: Sequelize.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED', 'EXPIRED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      listing_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      retirement_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      retirement_reason: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      retirement_beneficiary: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      images: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: true,
        defaultValue: [],
      },
      token_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      contract_address: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      chain_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      organization_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      project_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('carbon_credits');
    await queryInterface.dropTable('projects');
  }
};
