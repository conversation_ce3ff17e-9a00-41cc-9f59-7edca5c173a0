'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Organizations table first (referenced by Users)
    await queryInterface.createTable('organizations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      website: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      logo: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      verification_status: {
        type: Sequelize.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED', 'EXPIRED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      legal_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      registration_number: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      tax_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      city: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      state: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      postal_code: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      phone_number: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      industry: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      size: {
        type: Sequelize.ENUM('STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'),
        allowNull: true,
      },
      founded_year: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      primary_contact: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      primary_contact_email: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      primary_contact_phone: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      transaction_fee_rate: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.01,
      },
      listing_fee_rate: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.005,
      },
      subscription_fee: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0.0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Create Users table
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      password: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      role: {
        type: Sequelize.ENUM(
          'USER', 'ADMIN', 'SUPER_ADMIN', 'ORGANIZATION_ADMIN', 'COMPLIANCE_OFFICER',
          'AUDITOR', 'VERIFIER', 'MARKETPLACE_ADMIN', 'SUPPORT', 'DEVELOPER',
          'ANALYST', 'FINANCE', 'LEGAL', 'SALES', 'MARKETING', 'OPERATIONS', 'CUSTOM'
        ),
        allowNull: false,
        defaultValue: 'USER',
      },
      email_verified: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      job_title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      department_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      phone_number: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      profile_image: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      bio: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      last_login_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      two_factor_enabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      preferences: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      organization_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'organizations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('users');
    await queryInterface.dropTable('organizations');
  }
};
