const { DataTypes } = require('sequelize');
const { TransactionType, TransactionStatus } = require('../types/enums');

module.exports = (sequelize) => {
  const Transaction = sequelize.define('Transaction', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    fee: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    type: {
      type: DataTypes.ENUM(...Object.values(TransactionType)),
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(...Object.values(TransactionStatus)),
      allowNull: false,
      defaultValue: TransactionStatus.PENDING,
    },
    transactionHash: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      field: 'transaction_hash',
    },
    blockNumber: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'block_number',
    },
    network: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'wallet_id',
      references: {
        model: 'wallets',
        key: 'id',
      },
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'order_id',
      references: {
        model: 'orders',
        key: 'id',
      },
    },
  }, {
    tableName: 'transactions',
    underscored: true,
    timestamps: true,
  });

  return Transaction;
};
