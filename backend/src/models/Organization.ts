import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { OrganizationStatus, OrganizationSize, VerificationStatus } from '@/types/enums';

export interface OrganizationAttributes {
  id: string;
  name: string;
  description?: string;
  website?: string;
  logo?: string;
  status: OrganizationStatus;
  verificationStatus: VerificationStatus;
  legalName?: string;
  registrationNumber?: string;
  taxId?: string;
  country?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phoneNumber?: string;
  industry?: string;
  size?: OrganizationSize;
  foundedYear?: number;
  primaryContact?: string;
  primaryContactEmail?: string;
  primaryContactPhone?: string;
  transactionFeeRate: number;
  listingFeeRate: number;
  subscriptionFee: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface OrganizationCreationAttributes extends Omit<OrganizationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Organization extends Model<OrganizationAttributes, OrganizationCreationAttributes> implements OrganizationAttributes {
  public id!: string;
  public name!: string;
  public description?: string;
  public website?: string;
  public logo?: string;
  public status!: OrganizationStatus;
  public verificationStatus!: VerificationStatus;
  public legalName?: string;
  public registrationNumber?: string;
  public taxId?: string;
  public country?: string;
  public address?: string;
  public city?: string;
  public state?: string;
  public postalCode?: string;
  public phoneNumber?: string;
  public industry?: string;
  public size?: OrganizationSize;
  public foundedYear?: number;
  public primaryContact?: string;
  public primaryContactEmail?: string;
  public primaryContactPhone?: string;
  public transactionFeeRate!: number;
  public listingFeeRate!: number;
  public subscriptionFee!: number;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    users: Association<Organization, any>;
    projects: Association<Organization, any>;
    carbonCredits: Association<Organization, any>;
    wallets: Association<Organization, any>;
    notifications: Association<Organization, any>;
    auditLogs: Association<Organization, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Organization {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        website: {
          type: DataTypes.STRING,
          allowNull: true,
          validate: {
            isUrl: true,
          },
        },
        logo: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        status: {
          type: DataTypes.ENUM(...Object.values(OrganizationStatus)),
          allowNull: false,
          defaultValue: OrganizationStatus.PENDING,
        },
        verificationStatus: {
          type: DataTypes.ENUM(...Object.values(VerificationStatus)),
          allowNull: false,
          defaultValue: VerificationStatus.PENDING,
        },
        legalName: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        registrationNumber: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        taxId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        country: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        address: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        city: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        state: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        postalCode: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        phoneNumber: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        industry: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        size: {
          type: DataTypes.ENUM(...Object.values(OrganizationSize)),
          allowNull: true,
        },
        foundedYear: {
          type: DataTypes.INTEGER,
          allowNull: true,
          validate: {
            min: 1800,
            max: new Date().getFullYear(),
          },
        },
        primaryContact: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        primaryContactEmail: {
          type: DataTypes.STRING,
          allowNull: true,
          validate: {
            isEmail: true,
          },
        },
        primaryContactPhone: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        transactionFeeRate: {
          type: DataTypes.FLOAT,
          allowNull: false,
          defaultValue: 0.01,
          validate: {
            min: 0,
            max: 1,
          },
        },
        listingFeeRate: {
          type: DataTypes.FLOAT,
          allowNull: false,
          defaultValue: 0.005,
          validate: {
            min: 0,
            max: 1,
          },
        },
        subscriptionFee: {
          type: DataTypes.FLOAT,
          allowNull: false,
          defaultValue: 0.0,
          validate: {
            min: 0,
          },
        },
      },
      {
        sequelize,
        tableName: 'organizations',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Organization.hasMany(models.User, {
      foreignKey: 'organizationId',
      as: 'users',
    });

    Organization.hasMany(models.Project, {
      foreignKey: 'organizationId',
      as: 'projects',
    });

    Organization.hasMany(models.CarbonCredit, {
      foreignKey: 'organizationId',
      as: 'carbonCredits',
    });

    Organization.hasMany(models.Wallet, {
      foreignKey: 'organizationId',
      as: 'wallets',
    });

    Organization.hasMany(models.Notification, {
      foreignKey: 'organizationId',
      as: 'notifications',
    });

    Organization.hasMany(models.AuditLog, {
      foreignKey: 'organizationId',
      as: 'auditLogs',
    });
  }
}
