const { DataTypes } = require('sequelize');
const { OrderType, OrderStatus } = require('../types/enums');

module.exports = (sequelize) => {
  const Order = sequelize.define('Order', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    type: {
      type: DataTypes.ENUM(...Object.values(OrderType)),
      allowNull: false,
    },
    quantity: {
      type: DataTypes.FLOAT,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    price: {
      type: DataTypes.FLOAT,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    status: {
      type: DataTypes.ENUM(...Object.values(OrderStatus)),
      allowNull: false,
      defaultValue: OrderStatus.PENDING,
    },
    buyerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'buyer_id',
      references: {
        model: 'users',
        key: 'id',
      },
    },
    sellerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'seller_id',
      references: {
        model: 'users',
        key: 'id',
      },
    },
    carbonCreditId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'carbon_credit_id',
      references: {
        model: 'carbon_credits',
        key: 'id',
      },
    },
  }, {
    tableName: 'orders',
    underscored: true,
    timestamps: true,
  });

  return Order;
};
