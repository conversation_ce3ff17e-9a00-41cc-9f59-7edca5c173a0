import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { CarbonCreditStatus, VerificationStatus } from '@/types/enums';

export interface CarbonCreditAttributes {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  availableQuantity: number;
  retiredQuantity: number;
  price: number;
  minPurchaseQuantity?: number;
  vintage: number;
  standard: string;
  methodology: string;
  location?: string;
  country?: string;
  externalProjectId?: string;
  serialNumber?: string;
  certificationDate?: Date;
  expirationDate?: Date;
  verificationBody?: string;
  status: CarbonCreditStatus;
  verificationStatus: VerificationStatus;
  listingDate?: Date;
  retirementDate?: Date;
  retirementReason?: string;
  retirementBeneficiary?: string;
  images?: string[];
  tokenId?: string;
  contractAddress?: string;
  chainId?: number;
  metadata?: any;
  userId: string;
  organizationId: string;
  projectId: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CarbonCreditCreationAttributes extends Omit<CarbonCreditAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class CarbonCredit extends Model<CarbonCreditAttributes, CarbonCreditCreationAttributes> implements CarbonCreditAttributes {
  public id!: string;
  public name!: string;
  public description?: string;
  public quantity!: number;
  public availableQuantity!: number;
  public retiredQuantity!: number;
  public price!: number;
  public minPurchaseQuantity?: number;
  public vintage!: number;
  public standard!: string;
  public methodology!: string;
  public location?: string;
  public country?: string;
  public externalProjectId?: string;
  public serialNumber?: string;
  public certificationDate?: Date;
  public expirationDate?: Date;
  public verificationBody?: string;
  public status!: CarbonCreditStatus;
  public verificationStatus!: VerificationStatus;
  public listingDate?: Date;
  public retirementDate?: Date;
  public retirementReason?: string;
  public retirementBeneficiary?: string;
  public images?: string[];
  public tokenId?: string;
  public contractAddress?: string;
  public chainId?: number;
  public metadata?: any;
  public userId!: string;
  public organizationId!: string;
  public projectId!: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    user: Association<CarbonCredit, any>;
    organization: Association<CarbonCredit, any>;
    project: Association<CarbonCredit, any>;
    orders: Association<CarbonCredit, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof CarbonCredit {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        quantity: {
          type: DataTypes.FLOAT,
          allowNull: false,
          validate: {
            min: 0,
          },
        },
        availableQuantity: {
          type: DataTypes.FLOAT,
          allowNull: false,
          validate: {
            min: 0,
          },
        },
        retiredQuantity: {
          type: DataTypes.FLOAT,
          allowNull: false,
          defaultValue: 0,
          validate: {
            min: 0,
          },
        },
        price: {
          type: DataTypes.FLOAT,
          allowNull: false,
          validate: {
            min: 0,
          },
        },
        minPurchaseQuantity: {
          type: DataTypes.FLOAT,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        vintage: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1990,
            max: new Date().getFullYear() + 10,
          },
        },
        standard: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        methodology: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        location: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        country: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        externalProjectId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        serialNumber: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        certificationDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        expirationDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        verificationBody: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        status: {
          type: DataTypes.ENUM(...Object.values(CarbonCreditStatus)),
          allowNull: false,
          defaultValue: CarbonCreditStatus.PENDING,
        },
        verificationStatus: {
          type: DataTypes.ENUM(...Object.values(VerificationStatus)),
          allowNull: false,
          defaultValue: VerificationStatus.PENDING,
        },
        listingDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        retirementDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        retirementReason: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        retirementBeneficiary: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        images: {
          type: DataTypes.ARRAY(DataTypes.STRING),
          allowNull: true,
          defaultValue: [],
        },
        tokenId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        contractAddress: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        chainId: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        metadata: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
        projectId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'projects',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'carbon_credits',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    CarbonCredit.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    CarbonCredit.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    CarbonCredit.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });

    CarbonCredit.hasMany(models.Order, {
      foreignKey: 'carbonCreditId',
      as: 'orders',
    });
  }
}
