const { sequelize } = require('../config/database');

// Import model definitions
const UserModel = require('./User');
// const OrganizationModel = require('./Organization');
// const ProjectModel = require('./Project');
// const CarbonCreditModel = require('./CarbonCredit');
// const WalletModel = require('./Wallet');
// const OrderModel = require('./Order');
// const TransactionModel = require('./Transaction');
// const NotificationModel = require('./Notification');
// const AuditLogModel = require('./AuditLog');

// Initialize models
const models = {
  User: UserModel(sequelize),
  // Organization: OrganizationModel(sequelize),
  // Project: ProjectModel(sequelize),
  // CarbonCredit: CarbonCreditModel(sequelize),
  // Wallet: WalletModel(sequelize),
  // Order: OrderModel(sequelize),
  // Transaction: TransactionModel(sequelize),
  // Notification: NotificationModel(sequelize),
  // AuditLog: AuditLogModel(sequelize),
};

// Define associations (commented out for now)
// Object.values(models).forEach((model) => {
//   if (model.associate) {
//     model.associate(models);
//   }
// });

module.exports = { sequelize, ...models };
