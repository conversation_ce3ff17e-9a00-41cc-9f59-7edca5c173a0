import { Sequelize } from 'sequelize';
import { sequelize } from '@/config/database';

// Import all models
import { User } from './User';
import { Organization } from './Organization';
import { Project } from './Project';
import { CarbonCredit } from './CarbonCredit';
import { Wallet } from './Wallet';
import { Order } from './Order';
import { Transaction } from './Transaction';
import { Notification } from './Notification';
import { AuditLog } from './AuditLog';

// Initialize models
const models = {
  User: User.init(sequelize),
  Organization: Organization.init(sequelize),
  Project: Project.init(sequelize),
  CarbonCredit: CarbonCredit.init(sequelize),
  Wallet: Wallet.init(sequelize),
  Order: Order.init(sequelize),
  Transaction: Transaction.init(sequelize),
  Notification: Notification.init(sequelize),
  AuditLog: AuditLog.init(sequelize),
};

// Define associations
Object.values(models).forEach((model: any) => {
  if (model.associate) {
    model.associate(models);
  }
});

export { sequelize };
export default models;
