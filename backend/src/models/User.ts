import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { UserRole } from '@/types/enums';
import bcrypt from 'bcryptjs';

export interface UserAttributes {
  id: string;
  email: string;
  name?: string;
  password?: string;
  role: UserRole;
  emailVerified?: Date;
  jobTitle?: string;
  departmentName?: string;
  phoneNumber?: string;
  profileImage?: string;
  bio?: string;
  lastLoginAt?: Date;
  twoFactorEnabled: boolean;
  preferences?: any;
  organizationId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: string;
  public email!: string;
  public name?: string;
  public password?: string;
  public role!: UserRole;
  public emailVerified?: Date;
  public jobTitle?: string;
  public departmentName?: string;
  public phoneNumber?: string;
  public profileImage?: string;
  public bio?: string;
  public lastLoginAt?: Date;
  public twoFactorEnabled!: boolean;
  public preferences?: any;
  public organizationId?: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    organization: Association<User, any>;
    wallets: Association<User, any>;
    carbonCredits: Association<User, any>;
    buyOrders: Association<User, any>;
    sellOrders: Association<User, any>;
    notifications: Association<User, any>;
    auditLogs: Association<User, any>;
  };

  // Instance methods
  public async validatePassword(password: string): Promise<boolean> {
    if (!this.password) return false;
    return bcrypt.compare(password, this.password);
  }

  public async setPassword(password: string): Promise<void> {
    const saltRounds = 12;
    this.password = await bcrypt.hash(password, saltRounds);
  }

  public toJSON(): object {
    const values = { ...this.get() };
    delete values.password;
    return values;
  }

  // Model initialization
  public static init(sequelize: Sequelize): typeof User {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
          validate: {
            isEmail: true,
          },
        },
        name: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        password: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        role: {
          type: DataTypes.ENUM(...Object.values(UserRole)),
          allowNull: false,
          defaultValue: UserRole.USER,
        },
        emailVerified: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        jobTitle: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        departmentName: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        phoneNumber: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        profileImage: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        bio: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        lastLoginAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        twoFactorEnabled: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        preferences: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'users',
        underscored: true,
        timestamps: true,
        hooks: {
          beforeCreate: async (user: User) => {
            if (user.password) {
              await user.setPassword(user.password);
            }
          },
          beforeUpdate: async (user: User) => {
            if (user.changed('password') && user.password) {
              await user.setPassword(user.password);
            }
          },
        },
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    User.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    User.hasMany(models.Wallet, {
      foreignKey: 'userId',
      as: 'wallets',
    });

    User.hasMany(models.CarbonCredit, {
      foreignKey: 'userId',
      as: 'carbonCredits',
    });

    User.hasMany(models.Order, {
      foreignKey: 'buyerId',
      as: 'buyOrders',
    });

    User.hasMany(models.Order, {
      foreignKey: 'sellerId',
      as: 'sellOrders',
    });

    User.hasMany(models.Notification, {
      foreignKey: 'userId',
      as: 'notifications',
    });

    User.hasMany(models.AuditLog, {
      foreignKey: 'userId',
      as: 'auditLogs',
    });
  }
}
