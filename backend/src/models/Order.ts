import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { OrderType, OrderStatus } from '@/types/enums';

export interface OrderAttributes {
  id: string;
  type: OrderType;
  quantity: number;
  price: number;
  status: OrderStatus;
  buyerId: string;
  sellerId: string;
  carbonCreditId: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface OrderCreationAttributes extends Omit<OrderAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Order extends Model<OrderAttributes, OrderCreationAttributes> implements OrderAttributes {
  public id!: string;
  public type!: OrderType;
  public quantity!: number;
  public price!: number;
  public status!: OrderStatus;
  public buyerId!: string;
  public sellerId!: string;
  public carbonCreditId!: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    buyer: Association<Order, any>;
    seller: Association<Order, any>;
    carbonCredit: Association<Order, any>;
    transactions: Association<Order, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Order {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        type: {
          type: DataTypes.ENUM(...Object.values(OrderType)),
          allowNull: false,
        },
        quantity: {
          type: DataTypes.FLOAT,
          allowNull: false,
          validate: {
            min: 0,
          },
        },
        price: {
          type: DataTypes.FLOAT,
          allowNull: false,
          validate: {
            min: 0,
          },
        },
        status: {
          type: DataTypes.ENUM(...Object.values(OrderStatus)),
          allowNull: false,
          defaultValue: OrderStatus.PENDING,
        },
        buyerId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        sellerId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        carbonCreditId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'carbon_credits',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'orders',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Order.belongsTo(models.User, {
      foreignKey: 'buyerId',
      as: 'buyer',
    });

    Order.belongsTo(models.User, {
      foreignKey: 'sellerId',
      as: 'seller',
    });

    Order.belongsTo(models.CarbonCredit, {
      foreignKey: 'carbonCreditId',
      as: 'carbonCredit',
    });

    Order.hasMany(models.Transaction, {
      foreignKey: 'orderId',
      as: 'transactions',
    });
  }
}
