import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { NotificationType, NotificationCategory } from '@/types/enums';

export interface NotificationAttributes {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  category: NotificationCategory;
  isRead: boolean;
  data?: any;
  userId?: string;
  organizationId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NotificationCreationAttributes extends Omit<NotificationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Notification extends Model<NotificationAttributes, NotificationCreationAttributes> implements NotificationAttributes {
  public id!: string;
  public title!: string;
  public message!: string;
  public type!: NotificationType;
  public category!: NotificationCategory;
  public isRead!: boolean;
  public data?: any;
  public userId?: string;
  public organizationId?: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    user: Association<Notification, any>;
    organization: Association<Notification, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Notification {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        title: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        message: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        type: {
          type: DataTypes.ENUM(...Object.values(NotificationType)),
          allowNull: false,
        },
        category: {
          type: DataTypes.ENUM(...Object.values(NotificationCategory)),
          allowNull: false,
        },
        isRead: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        data: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'notifications',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Notification.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    Notification.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });
  }
}
