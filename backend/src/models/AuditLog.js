const { DataTypes } = require('sequelize');
const { AuditAction, AuditResource } = require('../types/enums');

module.exports = (sequelize) => {
  const AuditLog = sequelize.define('AuditLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    action: {
      type: DataTypes.ENUM(...Object.values(AuditAction)),
      allowNull: false,
    },
    resource: {
      type: DataTypes.ENUM(...Object.values(AuditResource)),
      allowNull: false,
    },
    resourceId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'resource_id',
    },
    details: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    ipAddress: {
      type: DataTypes.INET,
      allowNull: true,
      field: 'ip_address',
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'user_agent',
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id',
      },
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'organization_id',
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
  }, {
    tableName: 'audit_logs',
    underscored: true,
    timestamps: true,
  });

  return AuditLog;
};
