import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { TransactionType, TransactionStatus } from '@/types/enums';

export interface TransactionAttributes {
  id: string;
  amount: number;
  fee: number;
  gasPrice?: number;
  gasLimit?: number;
  gasUsed?: number;
  maxFeePerGas?: number;
  maxPriorityFeePerGas?: number;
  type: TransactionType;
  status: TransactionStatus;
  transactionHash?: string;
  blockNumber?: number;
  network?: string;
  metadata?: any;
  walletId?: string;
  orderId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TransactionCreationAttributes extends Omit<TransactionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Transaction extends Model<TransactionAttributes, TransactionCreationAttributes> implements TransactionAttributes {
  public id!: string;
  public amount!: number;
  public fee!: number;
  public gasPrice?: number;
  public gasLimit?: number;
  public gasUsed?: number;
  public maxFeePerGas?: number;
  public maxPriorityFeePerGas?: number;
  public type!: TransactionType;
  public status!: TransactionStatus;
  public transactionHash?: string;
  public blockNumber?: number;
  public network?: string;
  public metadata?: any;
  public walletId?: string;
  public orderId?: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    wallet: Association<Transaction, any>;
    order: Association<Transaction, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Transaction {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        amount: {
          type: DataTypes.FLOAT,
          allowNull: false,
        },
        fee: {
          type: DataTypes.FLOAT,
          allowNull: false,
          defaultValue: 0,
        },
        gasPrice: {
          type: DataTypes.FLOAT,
          allowNull: true,
        },
        gasLimit: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        gasUsed: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        maxFeePerGas: {
          type: DataTypes.FLOAT,
          allowNull: true,
        },
        maxPriorityFeePerGas: {
          type: DataTypes.FLOAT,
          allowNull: true,
        },
        type: {
          type: DataTypes.ENUM(...Object.values(TransactionType)),
          allowNull: false,
        },
        status: {
          type: DataTypes.ENUM(...Object.values(TransactionStatus)),
          allowNull: false,
          defaultValue: TransactionStatus.PENDING,
        },
        transactionHash: {
          type: DataTypes.STRING,
          allowNull: true,
          unique: true,
        },
        blockNumber: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        network: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        metadata: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        walletId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'wallets',
            key: 'id',
          },
        },
        orderId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'orders',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'transactions',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Transaction.belongsTo(models.Wallet, {
      foreignKey: 'walletId',
      as: 'wallet',
    });

    Transaction.belongsTo(models.Order, {
      foreignKey: 'orderId',
      as: 'order',
    });
  }
}
