import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { AuditAction, AuditResource } from '@/types/enums';

export interface AuditLogAttributes {
  id: string;
  action: AuditAction;
  resource: AuditResource;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  userId?: string;
  organizationId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AuditLogCreationAttributes extends Omit<AuditLogAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class AuditLog extends Model<AuditLogAttributes, AuditLogCreationAttributes> implements AuditLogAttributes {
  public id!: string;
  public action!: AuditAction;
  public resource!: AuditResource;
  public resourceId?: string;
  public details?: any;
  public ipAddress?: string;
  public userAgent?: string;
  public userId?: string;
  public organizationId?: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    user: Association<AuditLog, any>;
    organization: Association<AuditLog, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof AuditLog {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        action: {
          type: DataTypes.ENUM(...Object.values(AuditAction)),
          allowNull: false,
        },
        resource: {
          type: DataTypes.ENUM(...Object.values(AuditResource)),
          allowNull: false,
        },
        resourceId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        details: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        ipAddress: {
          type: DataTypes.INET,
          allowNull: true,
        },
        userAgent: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'audit_logs',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    AuditLog.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    AuditLog.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });
  }
}
