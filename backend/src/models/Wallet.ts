import { DataTypes, Model, Sequelize, Association } from 'sequelize';

export interface WalletAttributes {
  id: string;
  address: string;
  network: string;
  chainId?: number;
  type: string;
  name?: string;
  isActive: boolean;
  balance?: string;
  nativeBalance?: string;
  lastSyncAt?: Date;
  metadata?: any;
  userId?: string;
  organizationId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface WalletCreationAttributes extends Omit<WalletAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Wallet extends Model<WalletAttributes, WalletCreationAttributes> implements WalletAttributes {
  public id!: string;
  public address!: string;
  public network!: string;
  public chainId?: number;
  public type!: string;
  public name?: string;
  public isActive!: boolean;
  public balance?: string;
  public nativeBalance?: string;
  public lastSyncAt?: Date;
  public metadata?: any;
  public userId?: string;
  public organizationId?: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    user: Association<Wallet, any>;
    organization: Association<Wallet, any>;
    transactions: Association<Wallet, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Wallet {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        address: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true,
        },
        network: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        chainId: {
          type: DataTypes.INTEGER,
          allowNull: true,
        },
        type: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        balance: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: '0',
        },
        nativeBalance: {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: '0',
        },
        lastSyncAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        metadata: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'wallets',
        underscored: true,
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['address', 'network'],
          },
        ],
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Wallet.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    Wallet.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    Wallet.hasMany(models.Transaction, {
      foreignKey: 'walletId',
      as: 'transactions',
    });
  }
}
