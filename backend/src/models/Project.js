const { DataTypes } = require('sequelize');
const { ProjectType, ProjectStatus, VerificationStatus } = require('../types/enums');

module.exports = (sequelize) => {
  const Project = sequelize.define('Project', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    type: {
      type: DataTypes.ENUM(...Object.values(ProjectType)),
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM(...Object.values(ProjectStatus)),
      allowNull: false,
      defaultValue: ProjectStatus.PENDING,
    },
    verificationStatus: {
      type: DataTypes.ENUM(...Object.values(VerificationStatus)),
      allowNull: false,
      defaultValue: VerificationStatus.PENDING,
      field: 'verification_status',
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date',
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'end_date',
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    coordinates: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    area: {
      type: DataTypes.FLOAT,
      allowNull: true,
      validate: {
        min: 0,
      },
    },
    externalProjectId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'external_project_id',
    },
    registryId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'registry_id',
    },
    standard: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    methodology: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    methodologyVersion: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'methodology_version',
    },
    estimatedReductions: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'estimated_reductions',
      validate: {
        min: 0,
      },
    },
    actualReductions: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'actual_reductions',
      validate: {
        min: 0,
      },
    },
    verifier: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    validator: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    images: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
    },
    budget: {
      type: DataTypes.FLOAT,
      allowNull: true,
      validate: {
        min: 0,
      },
    },
    roi: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    sdgs: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    organizationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'organization_id',
      references: {
        model: 'organizations',
        key: 'id',
      },
    },
  }, {
    tableName: 'projects',
    underscored: true,
    timestamps: true,
  });

  // Define associations (commented out for now)
  // Project.associate = (models) => {
  //   Project.belongsTo(models.Organization, {
  //     foreignKey: 'organizationId',
  //     as: 'organization',
  //   });
  // };

  return Project;
};
