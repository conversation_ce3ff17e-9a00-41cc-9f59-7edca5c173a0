const { DataTypes } = require('sequelize');
const { OrganizationStatus, OrganizationSize, VerificationStatus } = require('../types/enums');

module.exports = (sequelize) => {
  const Organization = sequelize.define('Organization', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    logo: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(...Object.values(OrganizationStatus)),
      allowNull: false,
      defaultValue: OrganizationStatus.PENDING,
    },
    verificationStatus: {
      type: DataTypes.ENUM(...Object.values(VerificationStatus)),
      allowNull: false,
      defaultValue: VerificationStatus.PENDING,
      field: 'verification_status',
    },
    legalName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'legal_name',
    },
    registrationNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'registration_number',
    },
    taxId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'tax_id',
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    postalCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'postal_code',
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'phone_number',
    },
    industry: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    size: {
      type: DataTypes.ENUM(...Object.values(OrganizationSize)),
      allowNull: true,
    },
    foundedYear: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'founded_year',
      validate: {
        min: 1800,
        max: new Date().getFullYear(),
      },
    },
    primaryContact: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'primary_contact',
    },
    primaryContactEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'primary_contact_email',
      validate: {
        isEmail: true,
      },
    },
    primaryContactPhone: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'primary_contact_phone',
    },
    transactionFeeRate: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.01,
      field: 'transaction_fee_rate',
      validate: {
        min: 0,
        max: 1,
      },
    },
    listingFeeRate: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.005,
      field: 'listing_fee_rate',
      validate: {
        min: 0,
        max: 1,
      },
    },
    subscriptionFee: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.0,
      field: 'subscription_fee',
      validate: {
        min: 0,
      },
    },
  }, {
    tableName: 'organizations',
    underscored: true,
    timestamps: true,
  });

  // Define associations (commented out for now)
  // Organization.associate = (models) => {
  //   Organization.hasMany(models.User, {
  //     foreignKey: 'organizationId',
  //     as: 'users',
  //   });
  // };

  return Organization;
};
