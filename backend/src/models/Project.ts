import { DataTypes, Model, Sequelize, Association } from 'sequelize';
import { ProjectType, ProjectStatus, VerificationStatus } from '@/types/enums';

export interface ProjectAttributes {
  id: string;
  name: string;
  description?: string;
  type: ProjectType;
  status: ProjectStatus;
  verificationStatus: VerificationStatus;
  startDate?: Date;
  endDate?: Date;
  location?: string;
  country?: string;
  coordinates?: string;
  area?: number;
  externalProjectId?: string;
  registryId?: string;
  standard?: string;
  methodology?: string;
  methodologyVersion?: string;
  estimatedReductions?: number;
  actualReductions?: number;
  verifier?: string;
  validator?: string;
  images?: string[];
  budget?: number;
  roi?: number;
  sdgs?: string[];
  tags?: string[];
  metadata?: any;
  organizationId: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProjectCreationAttributes extends Omit<ProjectAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Project extends Model<ProjectAttributes, ProjectCreationAttributes> implements ProjectAttributes {
  public id!: string;
  public name!: string;
  public description?: string;
  public type!: ProjectType;
  public status!: ProjectStatus;
  public verificationStatus!: VerificationStatus;
  public startDate?: Date;
  public endDate?: Date;
  public location?: string;
  public country?: string;
  public coordinates?: string;
  public area?: number;
  public externalProjectId?: string;
  public registryId?: string;
  public standard?: string;
  public methodology?: string;
  public methodologyVersion?: string;
  public estimatedReductions?: number;
  public actualReductions?: number;
  public verifier?: string;
  public validator?: string;
  public images?: string[];
  public budget?: number;
  public roi?: number;
  public sdgs?: string[];
  public tags?: string[];
  public metadata?: any;
  public organizationId!: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public static associations: {
    organization: Association<Project, any>;
    carbonCredits: Association<Project, any>;
  };

  // Model initialization
  public static init(sequelize: Sequelize): typeof Project {
    return super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        type: {
          type: DataTypes.ENUM(...Object.values(ProjectType)),
          allowNull: false,
        },
        status: {
          type: DataTypes.ENUM(...Object.values(ProjectStatus)),
          allowNull: false,
          defaultValue: ProjectStatus.PENDING,
        },
        verificationStatus: {
          type: DataTypes.ENUM(...Object.values(VerificationStatus)),
          allowNull: false,
          defaultValue: VerificationStatus.PENDING,
        },
        startDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        endDate: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        location: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        country: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        coordinates: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        area: {
          type: DataTypes.FLOAT,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        externalProjectId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        registryId: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        standard: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        methodology: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        methodologyVersion: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        estimatedReductions: {
          type: DataTypes.FLOAT,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        actualReductions: {
          type: DataTypes.FLOAT,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        verifier: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        validator: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        images: {
          type: DataTypes.ARRAY(DataTypes.STRING),
          allowNull: true,
          defaultValue: [],
        },
        budget: {
          type: DataTypes.FLOAT,
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        roi: {
          type: DataTypes.FLOAT,
          allowNull: true,
        },
        sdgs: {
          type: DataTypes.ARRAY(DataTypes.STRING),
          allowNull: true,
          defaultValue: [],
        },
        tags: {
          type: DataTypes.ARRAY(DataTypes.STRING),
          allowNull: true,
          defaultValue: [],
        },
        metadata: {
          type: DataTypes.JSONB,
          allowNull: true,
        },
        organizationId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'organizations',
            key: 'id',
          },
        },
      },
      {
        sequelize,
        tableName: 'projects',
        underscored: true,
        timestamps: true,
      }
    );
  }

  // Define associations
  public static associate(models: any): void {
    Project.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    Project.hasMany(models.CarbonCredit, {
      foreignKey: 'projectId',
      as: 'carbonCredits',
    });
  }
}
