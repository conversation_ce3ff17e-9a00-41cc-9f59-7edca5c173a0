import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { Op } from 'sequelize';
import { Organization } from '@/models/Organization';
import { User } from '@/models/User';
import { asyncHandler, CustomError } from '@/middleware/errorHandler';
import { authenticate, authorize, requireOrganizationAccess } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { UserRole, OrganizationStatus, VerificationStatus, OrganizationSize } from '@/types/enums';

const router = Router();

// All organization routes require authentication
router.use(authenticate);

// Validation schemas
const organizationValidation = [
  body('name').isLength({ min: 2 }).trim(),
  body('industry').isLength({ min: 2 }).trim(),
  body('size').isIn(Object.values(OrganizationSize)),
  body('country').isLength({ min: 2 }).trim(),
  body('description').optional().isLength({ max: 1000 }).trim(),
  body('website').optional().isURL(),
  body('legalName').optional().isLength({ min: 2 }).trim(),
  body('registrationNumber').optional().isLength({ min: 1 }).trim(),
  body('taxId').optional().isLength({ min: 1 }).trim(),
  body('address').optional().isLength({ max: 500 }).trim(),
  body('city').optional().isLength({ min: 1 }).trim(),
  body('state').optional().isLength({ min: 1 }).trim(),
  body('postalCode').optional().isLength({ min: 1 }).trim(),
  body('phoneNumber').optional().isMobilePhone('any'),
  body('foundedYear').optional().isInt({ min: 1800, max: new Date().getFullYear() }),
  body('primaryContact').optional().isLength({ min: 2 }).trim(),
  body('primaryContactEmail').optional().isEmail(),
  body('primaryContactPhone').optional().isMobilePhone('any'),
];

/**
 * POST /api/organizations
 * Create a new organization
 */
router.post('/', organizationValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  // Check if user already has an organization
  const user = await User.findByPk(req.user!.id, {
    include: ['organization']
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  if (user.organization) {
    throw new CustomError('You already have an organization', 409);
  }

  const {
    name, description, website, logo, legalName, registrationNumber,
    taxId, country, address, city, state, postalCode, phoneNumber,
    industry, size, foundedYear, primaryContact, primaryContactEmail,
    primaryContactPhone
  } = req.body;

  // Create organization
  const organization = await Organization.create({
    name,
    description,
    website,
    logo,
    status: OrganizationStatus.PENDING,
    verificationStatus: VerificationStatus.PENDING,
    legalName,
    registrationNumber,
    taxId,
    country,
    address,
    city,
    state,
    postalCode,
    phoneNumber,
    industry,
    size,
    foundedYear,
    primaryContact,
    primaryContactEmail,
    primaryContactPhone,
    transactionFeeRate: 0.01,
    listingFeeRate: 0.005,
    subscriptionFee: 0.0,
  });

  // Update user to belong to organization and make them admin
  await user.update({
    organizationId: organization.id,
    role: UserRole.ORGANIZATION_ADMIN,
  });

  logger.info(`Organization ${organization.id} created by user ${user.id}`);

  res.status(201).json({
    organization,
    message: 'Organization created successfully'
  });
}));

/**
 * GET /api/organizations
 * Get organizations (admin only)
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.SUPER_ADMIN), asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, status, search } = req.query;

  const offset = (Number(page) - 1) * Number(limit);
  const whereClause: any = {};

  if (status) {
    whereClause.status = status;
  }

  if (search) {
    whereClause.name = {
      [Op.iLike]: `%${search}%`
    };
  }

  const { rows: organizations, count } = await Organization.findAndCountAll({
    where: whereClause,
    limit: Number(limit),
    offset,
    order: [['createdAt', 'DESC']],
    include: ['users']
  });

  res.json({
    organizations,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total: count,
      pages: Math.ceil(count / Number(limit))
    }
  });
}));

export default router;