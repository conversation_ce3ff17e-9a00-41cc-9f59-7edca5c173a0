import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { User } from '@/models/User';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '@/utils/jwt';
import { asyncHandler, CustomError } from '@/middleware/errorHandler';
import { authenticate } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { UserRole } from '@/types/enums';

const router = Router();

// Validation rules
const loginValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
];

const registerValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  body('name').isLength({ min: 2 }).trim(),
];

/**
 * POST /api/auth/login
 * Authenticate user and return JWT tokens
 */
router.post('/login', loginValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { email, password } = req.body;

  // Find user by email
  const user = await User.findOne({
    where: { email },
    include: ['organization']
  });

  if (!user) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Verify password
  const isValidPassword = await user.validatePassword(password);
  if (!isValidPassword) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Update last login
  await user.update({ lastLoginAt: new Date() });

  // Generate tokens
  const accessToken = generateAccessToken({
    id: user.id,
    email: user.email,
    role: user.role,
    organizationId: user.organizationId || undefined,
  });

  const refreshToken = generateRefreshToken({
    id: user.id,
    email: user.email,
  });

  logger.info(`User ${user.id} logged in successfully`);

  res.json({
    user: user.toJSON(),
    accessToken,
    refreshToken,
  });
}));

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', registerValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { email, password, name } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ where: { email } });
  if (existingUser) {
    throw new CustomError('User already exists', 409);
  }

  // Create new user
  const user = await User.create({
    email,
    password,
    name,
    role: UserRole.USER,
    twoFactorEnabled: false,
  });

  logger.info(`New user registered: ${user.id}`);

  res.status(201).json({
    user: user.toJSON(),
    message: 'User created successfully. Please verify your email.',
  });
}));

/**
 * POST /api/auth/refresh
 * Refresh access token using refresh token
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new CustomError('Refresh token required', 400);
  }

  try {
    const payload = verifyRefreshToken(refreshToken);

    // Find user to get current data
    const user = await User.findByPk(payload.id);
    if (!user) {
      throw new CustomError('User not found', 404);
    }

    // Generate new access token
    const accessToken = generateAccessToken({
      id: user.id,
      email: user.email,
      role: user.role,
      organizationId: user.organizationId || undefined,
    });

    res.json({ accessToken });
  } catch (error) {
    throw new CustomError('Invalid refresh token', 401);
  }
}));

/**
 * POST /api/auth/logout
 * Logout user (client should remove tokens)
 */
router.post('/logout', authenticate, asyncHandler(async (req: Request, res: Response) => {
  logger.info(`User ${req.user?.id} logged out`);

  res.json({ message: 'Logged out successfully' });
}));

export default router;
