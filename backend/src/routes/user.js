const { Router } = require('express');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');
const { asyncHandler, CustomError } = require('../middleware/errorHandler');
const { authenticate } = require('../middleware/auth');
const { logger } = require('../utils/logger');

const router = Router();

// All user routes require authentication
router.use(authenticate);

/**
 * GET /api/user/me
 * Get current user information
 */
router.get('/me', asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    include: ['organization'],
    attributes: { exclude: ['password'] }
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  res.json({ user });
}));

/**
 * PUT /api/user/me
 * Update current user information
 */
router.put('/me', [
  body('name').optional().isLength({ min: 2 }).trim(),
  body('jobTitle').optional().isLength({ max: 100 }).trim(),
  body('phoneNumber').optional().isMobilePhone('any'),
  body('bio').optional().isLength({ max: 500 }).trim(),
  body('preferences').optional().isObject(),
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { name, jobTitle, phoneNumber, bio, preferences } = req.body;

  const user = await User.findByPk(req.user.id);
  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Update user fields
  const updateData = {};
  if (name !== undefined) updateData.name = name;
  if (jobTitle !== undefined) updateData.jobTitle = jobTitle;
  if (phoneNumber !== undefined) updateData.phoneNumber = phoneNumber;
  if (bio !== undefined) updateData.bio = bio;
  if (preferences !== undefined) updateData.preferences = preferences;

  await user.update(updateData);

  logger.info(`User ${user.id} updated their profile`);

  res.json({
    user: user.toJSON(),
    message: 'Profile updated successfully'
  });
}));

/**
 * GET /api/user/organization
 * Get current user's organization
 */
router.get('/organization', asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    include: ['organization']
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  res.json({
    organization: user.organization || null
  });
}));

/**
 * GET /api/user/notification-preferences
 * Get user notification preferences
 */
router.get('/notification-preferences', asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    attributes: ['preferences']
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const notificationPreferences = user.preferences?.notifications || {
    email: true,
    push: true,
    sms: false,
    categories: {
      transactions: true,
      orders: true,
      verification: true,
      compliance: true,
      marketplace: true,
      system: true
    }
  };

  res.json({ notificationPreferences });
}));

/**
 * PUT /api/user/notification-preferences
 * Update user notification preferences
 */
router.put('/notification-preferences', [
  body('email').optional().isBoolean(),
  body('push').optional().isBoolean(),
  body('sms').optional().isBoolean(),
  body('categories').optional().isObject(),
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const user = await User.findByPk(req.user.id);
  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const currentPreferences = user.preferences || {};
  const notificationPreferences = {
    ...currentPreferences.notifications,
    ...req.body
  };

  await user.update({
    preferences: {
      ...currentPreferences,
      notifications: notificationPreferences
    }
  });

  logger.info(`User ${user.id} updated notification preferences`);

  res.json({
    notificationPreferences,
    message: 'Notification preferences updated successfully'
  });
}));

module.exports = router;
